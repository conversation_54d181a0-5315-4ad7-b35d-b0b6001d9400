package com.birdeye.social.constant;

public interface FireBaseConstants {

    String SOCIAL_ACCOUNT_SETUP_BUCKET = "socialAccountSetup";
    String SOCIAL_ACCOUNT_CHECK_STATUS = "status";
    String NEXUS_TOPIC_SEPARATOR = "/";
    String SOCIAL_POST_PROGRESS_BUCKET = "socialPostStatus";
    String SOCIAL_POST_PROGRESS_STATUS = "socialPostProgress";
    String SOCIAL_FAILED_POST_STATUS = "socialFailedPostStatus";
    String SOCIAL_FAILED_POST_BUCKET = "socialFailedPostStatus";

    String SOCIAL_POST_DELETE_STATUS = "socialPostDeleteStatus";

    String SOCIAL_POST_DELETE_CHECK_STATUS = "status";
    String SOCIAL_ENGAGE_NEW_NOTIFICATION_BUCKET = "socialEngage";

    String SOCIAL_ENGAGE_ES_FEED_BUCKET = "socialEngageEsFeed";

    String APPROVAL_APPROVED_COUNT = "approval";

    String STATUS = "status";

    static String getSocialAccountCheckStatusTopic(String channel,Long enterpriseId){
        return SOCIAL_ACCOUNT_SETUP_BUCKET+NEXUS_TOPIC_SEPARATOR+channel+NEXUS_TOPIC_SEPARATOR+enterpriseId+NEXUS_TOPIC_SEPARATOR+SOCIAL_ACCOUNT_CHECK_STATUS;
    }

    static String getSocialPostProgressStatusTopic(String key){
        return SOCIAL_POST_PROGRESS_BUCKET+NEXUS_TOPIC_SEPARATOR+key+NEXUS_TOPIC_SEPARATOR+SOCIAL_POST_PROGRESS_STATUS;
    }
    static String getSocialFailedPostStatusTopic(String key){
        return SOCIAL_FAILED_POST_BUCKET+NEXUS_TOPIC_SEPARATOR+key+NEXUS_TOPIC_SEPARATOR+SOCIAL_FAILED_POST_STATUS;
    }

    static String getSocialPostDeleteProgressStatusTopic(Integer postId){
        return SOCIAL_POST_DELETE_STATUS+NEXUS_TOPIC_SEPARATOR+postId+NEXUS_TOPIC_SEPARATOR+SOCIAL_POST_DELETE_CHECK_STATUS;
    }
    static String getSocialEngageNewNotificationTopic(Integer enterpriseId){
        return SOCIAL_ENGAGE_NEW_NOTIFICATION_BUCKET+NEXUS_TOPIC_SEPARATOR+enterpriseId;
    }

    static String getSocialEngageEsFeedUpdateTopic(Integer enterpriseId){
        return SOCIAL_ENGAGE_ES_FEED_BUCKET+NEXUS_TOPIC_SEPARATOR+enterpriseId;
    }

    static String getApprovalApprovedCountPath(Integer userId) {
        return APPROVAL_APPROVED_COUNT+NEXUS_TOPIC_SEPARATOR+ApprovalStatus.PENDING.getName()+NEXUS_TOPIC_SEPARATOR+userId;
    }

    static String getApprovalRejectedAndTerminatedCountPath(Long enterpriseId, ApprovalStatus approvalStatus) {
        return APPROVAL_APPROVED_COUNT+NEXUS_TOPIC_SEPARATOR+approvalStatus.getName()+NEXUS_TOPIC_SEPARATOR+enterpriseId;
    }
}
