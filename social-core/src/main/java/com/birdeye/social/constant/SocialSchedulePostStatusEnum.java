/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.birdeye.social.constant;

/**
 *
 * <AUTHOR>
 */
public enum SocialSchedulePostStatusEnum {
    SCHEDULED(0,"scheduled"), POSTED(1,"posted"), IS_PICKED(2,"processing"),
    APPROVAL_REJECTED(5, "approvalRejected"), APPROVAL_TERMINATED(6, "approvalTerminated");

    Integer id;
    String name;

    private SocialSchedulePostStatusEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static SocialSchedulePostStatusEnum getPostStatusByName(String name) {
        for (SocialSchedulePostStatusEnum status : SocialSchedulePostStatusEnum.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return null;
    }
    public static SocialSchedulePostStatusEnum getPostStatusById(Integer id) {
        for (SocialSchedulePostStatusEnum status : SocialSchedulePostStatusEnum.values()) {
            if (status.getId().equals(id)) {
                return status;
            }
        }
        return null;
    }
}