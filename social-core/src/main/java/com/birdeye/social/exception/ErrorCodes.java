package com.birdeye.social.exception;

public enum ErrorCodes {

	CALL_TO_ACTION_URL(2),

	INVALID_REQUEST(1011),
	USER_NOT_FOUND(1010),
	ERROR_CODE_NOT_FOUND(4404),
	BUSINESS_NOT_FOUND(1012),

	NO_RESELLER_BUSINESS_FOUND(1013),
	ERROR_FETCHING_RESELLER_DETAILS(1018),
	ERROR_FETCHING_LEAF_LOCATIONS(1019),

	INACTIVE_BUSINESS(1014),

	INVALID_BUSINESS_TYPE(1015),

	ONLY_RESELLER_CAN_ACCESS(1016),
	SCHEDULED_DATE_IN_PAST(1437),
	INVALID_FROM_DATE(1186),
	INVALID_TO_DATE(1187),
	UNABLE_TO_UPLOAD_ON_GOOGLE(1200),
	NOT_AUTHENTICATED(1201),
	UNABLE_TO_POST_ACTIVITY_ON_GOOGLE(1202),
	INVALID_SCHEDULED_DATE(1203),
	UNAUTHORIZED_OR_EXPIRED_TOKEN(1204),
	NO_SUCH_FACEBOOK_PAGE_EXIST(1205),
	MULTIPLE_FACEBOOK_PAGES_EXIST(1206),
	FACEBOOK_PAGE_USER_NOT_ADMIN(1207),
	UNABLE_TO_POST_ON_LINKEDIN(1208),
	NO_STREAM_FOUND(1209),
	NO_STREAM_PAGE_FOUND(1210),
	TWEET_ALREADY_LIKED(1211),
	GMB_PAGE_LOCATION_NOT_FOUND(1212),
	UNABLE_TO_POST_ACTIVITY_ON_FACEBOOK(1213),
	UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_FACEBOOK(1214),
	UNABLE_TO_GET_USER_DETAILS(1215),
	UNABLE_TO_FETCH_PAGE_DETAILS(1216),
	UNABLE_TO_INSTALL_BIRDEYE_APP_TAB_ON_FACEBOOK_PAGE(1217),
	UNABLE_TO_GENERATE_TOKEN_ON_FACEBOOK(1218),
	UNABLE_TO_FETCH_FACEBOOK_FEED(1219),
	UNABLE_TO_FETCH_FACEBOOK_POSTS(1220),
	UNABLE_TO_LIKE_FACEBOOK_OBJECT(1221),
	UNABLE_TO_UNLIKE_FACEBOOK_OBJECT(1222),
	UNABLE_TO_DELETE_FACEBOOK_OBJECT(1223),
	UNABLE_TO_GET_FACEBOOK_VIDEO_SOURCE(1224),
	UNABLE_TO_DELETE_GMB_OBJECT(1225),
	UNABLE_TO_DELETE_LINKEDIN_OBJECT(1227),
	UNABLE_TO_DELETE_TWITTER_OBJECT(1228),
	UNABLE_TO_EDIT_GMB_POST_TEXT(1229),
	UNSTALL_BIRDEYE_APP_TAB_ON_FACEBOOK_PAGE(1230),
	UNABLE_TO_COMMENT_FACEBOOK_OBJECT(1231),
	UNABLE_TO_ADD_LOCALPOST_ON_GOOGLE(1232),
	UNABLE_TO_FETCH_FB_CATEGORIES(1233),
	INVALID_FB_PAGE(1234),
	INVALID_FB_DATE_FORMAT(1236),
	INVALID_FB_START_END_DATE(1237),
	UNABLE_TO_FETCH_FB_INSIGHTS(1235),
	FB_READ_INSIGHTS_PERMISSION_MISSING(1238),

	FB_ACKNOWLEDGE_ERROR(1239),
	INVALID_FB_PAGE_IGNORE(1243),
	UNABLE_TO_EDIT_POST_TEXT_ON_FACEBOOK(1244),
	UNABLE_TO_EDIT_POST_TEXT_ON_LINKEDIN(1245),
	FACEBOOK_SESSION_TOKEN_IS_NULL(1323),
	FACEBOOK_INVALID_PLACES_DATA(1501),
	UNABLE_TO_FETCH_FB_POST_DATA(1239),
	LINKEDIN_PAGES_NOT_FOUND(1601),
	NO_PAGE_MAPPED_TO_BUSINESS(1602),
	NO_BUSINESS_MAPPED_WITH_PAGE(1607),
	DUPLICATE_STREAM_FOUND(1240),

	LINKEDIN_MENTION_ERROR(1603),
	UNABLE_TO_POST_IMAGE_LINKEDIN(1241),
	FB_SOCKET_TIME_OUT(1246),
	// Enterprise Setup
	INVALID_PAGE_OR_LOCATION_ID(1701),
	FB_PAGE_NOT_FOUND(1702),
	FB_PAGE_ALREADY_MAPPED(1703),
	FB_MULTIPLE_MAPPING_FOUND(1704),
	GP_MULTIPLE_MAPPING_FOUND(1705),
	UNABLE_TO_GENERATE_TOKEN_ON_GPlus(1706),
	GPLUS_PAGE_NOT_FOUND(1707),
	GPLUS_PAGE_ALREADY_MAPPED(1708),
	GPLUS_MULTIPLE_MAPPING_FOUND(1709),
	GMB_PAGE_NOT_FOUND(1710),
	GMB_PAGE_ALREADY_MAPPED(1711),
	GMB_MULTIPLE_MAPPING_FOUND(1712),
	GMB_LOC_NOT_FOUND_STAGING(1713),
	GMB_DOMAINS_NO_MATCH(1714),
	GMB_RAW_LOC_ALREADY_MAPPED(1715),
	GMB_LOC_ALREADY_MAPPED(1716),
	TWITTER_ACCOUNT_NOT_FOUND(1717),
	TWITTER_ACCOUNT_ALREADY_MAPPED(1718),
	TWITTER_MULTIPLE_MAPPING_FOUND(1719),
	TWITTER_CONNECT_ERROR(1720),
	GMB_PAGE_LOCATION_MISMATCH(1721),

	GMB_MULTIPLE_MAPPING_NOT_FOUND(1728),

	GMB_PLACE_ID_NOT_FOUND(1730),

	GMB_LOCATION_ID_NOT_FOUND(1731),

	GMB_REVIEW_NOT_FOUND(1735),


	GMB_INTERNAL_FAILURE(1722),
	GMB_MAPPING_NOT_FOUND(1722),
	GMB_MAPPING_INVALID(1723),
	GMB_PAGE_INVALID(1724),
	GMB_PAGE_INVALID_REQUEST_ID(1725),
	FB_PAGE_INVALID_REQUEST_ID(1726),
	TWITTER_PAGE_INVALID_REQUEST_ID(1727),
	SOCIAL_CONNECT_ALREADY_IN_PROGRESS(1729),
	TWITTER_DUPLICATE_ERROR(1730),
	TWITTER_TEXT_TOO_LONG_ERROR(1731),
	TWITTER_OPERATION_NOT_PERMITTED_ERROR(1732),
	TWITTER_CLIENT_ERROR(1733),

	UNABLE_TO_REVOKE_ACCESS_TOKEN(1734),
	FB_PAGE_NOT_FOUND_IGNORE(1735),
	FB_MULTIPLE_MAPPING_FOUND_IGNORE(1736),
	FB_PUBLIC_PROFILE_ERROR(1737),
	GMB_INCORRECT_ACTION_TYPE(1738),

	STATUS_CHANGED(2015),
	MULTI_FETCHED_ROWS(2016),

	INVALID_REMOVE_REQUEST(2058),
	INVALID_SESSION_TOKEN(1004),
	UNABLE_TO_FETCH_FACEBOOK_REVIEWS(2059),
	UNABLE_TO_ADD_FACEBOOK_COMMENT(2060),
	UNABLE_TO_GET_FACEBOOK_OVERALL_RATINGS(2061),
	UNABLE_TO_UPDATE_FACEBOOK_PAGE_INFO(2062),
	UNABLE_TO_UPDATE_GMB_PAGE_INFO(2063),
	GMP_PAGE_PIN_DROP_REQUIRED(2096),
	UNABLE_TO_FETCH_FACEBOOK_REPLIES(2064),
	PAGE_UNABLE_TO_SUBSCRIBE_FB_APP(2065),
	PAGE_UNABLE_TO_DELETE_SUBSCRIBE_FB_APP(2066),
	FB_MESSENGER_USER_DETAILS(2067),
	FB_UNABLE_TO_SEND_MESSAGE(2068),
	INVALID_SEND_FB_MESSAGE_REQUEST(2069),
	UNABLE_TO_GET_REVIEW_REPLY_ON_GOOGLE(2070),
	ERROR_WHILE_POSTING_FB_COMMENT(2071),
	UNABLE_TO_UPDATE_GMB_ATTRIBUTES_INFO(2072),
	FB_OAUTH_EXCEPTION(2000),
	INVALID_ARGUMENT(2072),
	UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB(2073),
	UNABLE_TO_GET_LOCATION_NAME(2074),
	PAGE_UNABLE_TO_DELETE_SUBSCRIBE_FB_MESSENGER(2001),
	FB_UNABLE_TO_FIND_FILTER_DATA(2095),

	UNABLE_TO_GENERATE_TOKEN_ON_LINKEDIN(2075),
	UNABLE_TO_RUN_TOKEN_INTROSPECTION(2076),
	LINKEDIN_DUPLICATE_CONTENT(2077),
	LINKEDIN_MEMBER_LIMIT_REACED(2078),
	LINKEDIN_MEMBER_FORBIDDEN(2079),
	LINKEDIN_PAGE_NOT_FOUND(2080),
	LINKEDIN_PAGE_ALREADY_MAPPED(2081),

	LINKEDIN_UNABLE_TO_DOWNLOAD_MEDIA(2082),
	INVALID_TWITTER_APP(2345),
	GOOGLE_MESSAGES_BRAND_ALREADY_CREATED(2100),
	GOOGLE_MESSAGES_AGENT_ALREADY_CREATED(2101),
	GOOGLE_MESSAGES_BRAND_NOT_CREATED(2102),
	GOOGLE_MESSAGES_AGENT_NOT_VERIFIED(2103),
	GOOGLE_MESSAGES_AGENT_NOT_CREATED(2104),
	GOOGLE_MESSAGES_AGENT_NOT_LAUNCHED(2105),
	GOOGLE_MESSAGES_AGENT_STATUS_NOT_CREATED(2106),
	GOOGLE_MESSAGES_AGENT_STATUS_NOT_VERIFIED(2107),
	GOOGLE_MESSAGES_LOCATION_NOT_VERIFIED(2108),
	GOOGLE_MESSAGES_LOCATION_NOT_LAUNCHED(2109),
	GOOGLE_MESSAGES_AGENT_NAME_INVALID(2110),
	GOOGLE_MESSAGES_AGENT_SETUP_ERROR(2111),
	GOOGLE_MESSAGES_TOKEN_FETCH_ERROR(2112),
	GOOGLE_MESSAGES_AGENT_UPDATE_ERROR(2113),
	GOOGLE_MESSAGES_BRAND_CREATE_ERROR(2114),
	TWITTER_PAGE_NOT_FOUND(2082),
	NO_BUSINESS_GET_PAGE_REQUEST_FOUND(2083),
	BUSINESS_GET_PAGE_REQUEST_STATUS_MOVED(2084),
	GMB_ACCOUNT_NOT_FOUND(2085),
	APPLE_MESSAGES_BRAND_ALREADY_CREATED(2086),
	GMB_FOOD_MENUS_NOT_ENABLED(2090),

	// Use 3000s code only for instagram.
	GMB_API_LIMIT_EXCEEDED(2086),

	SESSION_TOKEN_NOT_FOUND(2088),
	TWITTER_MEDIA_POST_FAILED(2089),

	// Use 3000s code only for instagram.
	NO_SUCH_INSTAGRAM_ACCOUNT_EXIST(3001),
	MULTIPLE_INSTAGRAM_ACCOUNT_EXIST(3002),
	INSTAGRAM_UNABLE_TO_SEND_MESSAGE(3003),
	INSTAGRAM_INVALID_ACCESS_TOKEN(3004),
	INSTAGRAM_ACCOUNT_ALREADY_ADDED(3005),
	INSTAGRAM_INVALID_FEED_DATA(3006),
	INSTAGRAM_INVALID_COMMENTS_DATA(3007),
	INSTAGRAM_POST_COMMENT_FAILED(3008),
	INSTAGRAM_DELETE_COMMENT_FAILED(3009),
	INVALID_INSTAGRAM_PAGE(3010),
	INSTAGRAM_PERMISSIONS_MISSING(3011),
	ERROR_IN_FETCHING_ROLE(3017),
	INSTAGRAM_ACCOUNT_NOT_CONNECTED(3012),
	INSTAGRAM_PAGE_ALREADY_MAPPED(3013),
	INSTAGRAM_PAGE_NOT_FOUND(3014),
	INSTAGRAM_UNABLE_TO_GET_SUBSCRIBE_APP(3015),
	UNABLE_TO_GET_INSTAGRAM_DETAILS(3016),
	UNABLE_TO_FETCH_INSTAGRAM_ROLE(3017),
	INVALID_REQUEST_INSERT_MAPPING(3018),
	INVALID_REQUEST_REMOVE_MAPPING(3019),
	APPLE_ACCOUNT_NOT_FOUND(3020),
	// Use 3000s code only for instagram.
	APPLE_UNABLE_TO_SEND_MESSAGE(3020),
	APPLE_ERROR_TO_GET_PRE_DOWNLOAD_URL(3021),
	APPLE_ERROR_TO_GET_PRE_UPLOAD_URL(3022),
	APPLE_ERROR_TO_GET_FILE_CHECKSUM(3023),
	INSTAGRAM_MENTION_ERROR(3024),

	// Use 3000s code only for instagram.
	INSTAGRAM_CONTAINER_CREATION_ERROR(9007),
	INSTAGRAM_CONTAINER_NOT_CREATED(9008),
	INSTAGRAM_CONTAINER_RETRY_LIMIT_EXHAUST(9015),
	INSTAGRAM_GENERIC_ERROR(10),

	INSTAGRAM_DELETE_POST_FAILED(3025),

	UNABLE_TO_FETCH_LOCATION(7000),
	UNABLE_TO_FETCH_MENTIONS(7001),
	UNABLE_TO_FETCH_IMAGE(7002),
	// Use 3000s code only for instagram.

	// AWS CODE
	FAILED_TO_GET_AWS_SM_SECRETS(4001),

	FAILED_TO_PARSE_AWS_SM_SECRETS(4002),

	// SOCIAL POST CODE
	UNABLE_TO_SAVE_SOCIAL_DRAFT(5001),
	UNABLE_TO_EDIT_SOCIAL_DRAFT(5002),
	POST_DNE(5003),
	DRAFT_DNE(5004),

	ENGAGE_DNE(5007),
	POST_LIB_DNE(5005),
	// YouTube post code
	UNKNOWN_ERROR_OCCURRED(6001),
	YOUTUBE_IO_EXCEPTION(6002),
	YOUTUBE_CATEGORY_EXCEPTION(6003),
	YOUTUBE_VIDEO_EMPTY(6004),
	FAILED_TO_UPLOAD_YOUTUBE_VIDEO(6005),
	MANDATORY_FIELD_IS_NULL(6006),
	YOUTUBE_GENERAL_SECURITY_EXCEPTION(6007),
	YOUTUBE_CONNECT_ERROR(6009),
	UNABLE_TO_EDIT_YOUTUBE_OBJECT(6010),
	UNABLE_TO_DELETE_YOUTUBE_OBJECT(1226),

	UNABLE_TO_FETCH_IG_INSIGHTS(6008),

	// Insights Error code
	UNABLE_TO_FETCH_PAGE_INSIGHTS(7001),
	UNKNOWN_ERROR_OCCURRED_PAGE_INSIGHTS(7002),

	// UI
	FAILED_TO_SEND_REVIEW_TO_UI(6001),
	INVALID_GROUPING_TYPE(7003),
	// Multi-brand
	WIDGET_ALREADY_EXISTS(8001),
	WIDGET_DOES_NOT_EXISTS(8002),
	UNABLE_TO_DELETE_BRAND(8003),
	AGENT_IS_NULL(8004),
	NO_PAGE_MAPPED_TO_BUSINESS_FOR_AGENTS(8005),
	UNABLE_TO_DELETE_GMB_LOCATION(8006),
	AGENT_DELETION_ERROR(8007),
	VERIFIED_AGENT_DELETE(8008),
	ERROR_WHILE_RELAUNCHING_LOCATION(8009),

	CLIENT_ERROR_400(400),
	CLIENT_ERROR_401(401),
	CLIENT_ERROR_402(402),
	CLIENT_ERROR_403(403),
	CLIENT_ERROR_404(404),
	CLIENT_ERROR_405(405),
	CLIENT_ERROR_411(411),
	CLIENT_ERROR_429(429),

	CLIENT_ERROR_422(422),
	NO_PROFILE_MAPPED_BUSINESS(700),
	FAILED_TO_CREATE_CONTAINER(701),
	NO_CONTAINER_ID_FOUND_FOR_POSTING(702),
	NO_MEDIA_FOUND_POSTING(703),

	POLICY_VIOLATION(704),
	UNSPECIFIED(705),

	POST_MORE_THAN_4_IMAGES(706),
	POST_TEXT_NOT_SET(707),
	TWITTER_RESOURCE_FAIL(708),
	NO_PAGE_FOR_BUSINESS_IDS(709),

	INVALID_PAGE(709),

	UNABLE_TO_FETCH_START_DATE(2235),

	RETRY_LIMIT_EXCEEDED(2086),
	EMPTY_POST_TEXT(2087),
	UNABLE_TO_UPLOAD_IMAGE(1241),
	INTERNAL_SERVER_ERROR(5500),
	ERROR_DOWNLOAD_MEDIA_FILE(550),
	INVALID_REQUEST_SOCIAL(1013),
	INVALID_REQUEST_MEDIA(1014),
	INVALID_REQUEST_DATE(1015),
	INVALID_REQUEST_TRACKING(1016),
	INVALID_TRACKING_SOCIAL(1017),
	NO_ACTIVE_AGGREGATION_SOURCE(1096),
	// UI

	// Freemium error codes
	DUPLICATE_SESSION_ID(8001),
	UNABLE_TO_COMPLETE_REQUEST(8002),
	LOCATION_ID_NULL(8003),
	LOCATION_IS_MAPPED_AGAINST_DIFFERENT_BUSINESS(8004),
	REQUEST_IS_INVALID_STATUS_NOT_IN_FETCHED_STATE(8005),
	SESSION_ID_NOT_FOUND(8006),
	TOO_MANY_REQUESTS(8010),
	MESSAGE_STATUS_ERROR(1007),
	REQUEST_ENTITY_NOT_FOUND(8011),
	CALLER_DOES_NOT_HAVE_PERMISSION(8012),
	LINKEDIN_ERROR(-1),
	FB_PERMISSION_MISSING(1242),
	SQL_EXCEPTION(8013),
	DUPLICATE_URL_FOUND(9001),
	UNABLE_TO_UNBAN_USER(9009),
	NO_SUCH_YOUTUBE_CHANNEL_EXIST(9010),
	PAGE_UNABLE_TO_SUBSCRIBE_IG_APP(9011),
	PAGE_UNABLE_TO_DELETE_SUBSCRIBE_IG_APP(9012),
	FAILED_TO_PARSE_DATE(9013),
	UNABLE_TO_UPDATE_ES(9014),
	LOCATION_NOTIFICATION_MISSING(1442),
	NO_USER_FOUND(1542),
	EMPTY_RESPONSE_BODY(1543),
	APPLE_BUSINESS_CONNECT_RATE_LIMIT_ERROR(1546),

	APPLE_BUSINESS_REPORT_ERROR(1550),
	LINKEDIN_NOTIFICATION_ERROR(1544),
	MESSAGE_NOT_FOUND(1545),
	APPROVAL_WORKFLOW_DELETED(1546),

	// Social Asset Library Error Codes
	ASSET_LIB_INVALID_INPUT(9001),
	ASSET_LIB_ASSET_DNE(9002),
	ASSET_LIB_BIZ_TAG_DNE(9003),

	// Tagging related Error Codes
	SOCIAL_TAG_DNE(9100),
	INVALID_TAG_REQUEST(9111),

	TWITTER_DM_YOU_ARE_BLOCKING_USER(1546),
	TWITTER_DM_OPERATION_IS_NOT_PERMITTED(1547),
	TWITTER_DM_PERMISSION(1548),
	TWITTER_DM_UNABLE_TO_SEND(1549),
	DELETE_FAILED(1560),
	TWITTER_LIKE_OPERATION_IS_NOT_PERMITTED(1562),
	TWITTER_LIKE_UNABLE_TO_PERFORM_THIS_ACTION(1563),

	PARENT_COMMENT_NOT_FOUND(1561), TIME_OUT_ERROR(1562),

	OFFER_URL_INVALID(1571),
	TERMS_AND_CONDITION_URL_INVALID(1572),
	UPLOAD_FAILED_AT_PICTURES_SERVICE(1600),
	MEDIA_UPLOAD_ALREADY_COMPLETED(1601),
	NO_MEDIA_UPLOAD_REQUEST_FOUND(1602),
	NO_MEDIA_CHUNK_FOUND_WITH_SEQUENCE_ID(1603),
	VIDEO_UNAVAILABLE(1604),
	MEDIA_IN_PROGRESS(1605),
	SOCIAL_RAW_PAGE_NOT_FOUND(1606),
	NO_DATA_FOUND(1701),
	MAPPING_ALREADY_EXISTS(1702),
	COMPETITOR_ACCOUNT_SIZE_EXCEED(1739),
	INVALID_PAGINATION_PARAMS(2010),
	UNAUTHORIZED_ACCESS(1244),


	//Error code for tiktok
	TIKTOK_PAGE_NOT_FOUND(4201);

	private final int value;

	ErrorCodes(int value) {
		this.value = value;
	}

	public int value() {
		return value;
	}

	public static ErrorCodes valueOf(int value) {
		for (ErrorCodes errorCodes : ErrorCodes.values()) {
			if (errorCodes.value() == value) {
				return errorCodes;
			}
		}
		return ERROR_CODE_NOT_FOUND;
	}

}
