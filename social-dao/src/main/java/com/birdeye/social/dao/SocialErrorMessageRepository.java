package com.birdeye.social.dao;

import com.birdeye.social.entities.SocialErrorMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SocialErrorMessageRepository extends JpaRepository<SocialErrorMessage, Integer> {

    Optional<SocialErrorMessage> findByAction(String action);

}
