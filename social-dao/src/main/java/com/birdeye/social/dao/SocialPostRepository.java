package com.birdeye.social.dao;

import com.birdeye.social.dto.SocialPostLite;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import com.birdeye.social.entities.SocialPost;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

public interface SocialPostRepository extends JpaRepository<SocialPost, Integer> {
	
	public SocialPost findById(Integer id);

	List<SocialPost> findByIdIn(Collection<Integer> ids);

	List<SocialPost> findByReviewId(Integer reviewId);

	List<SocialPost> findByMasterPostId(Integer id);

	@Query("select s.id from SocialPost s where s.masterPostId = :masterPostId")
	List<Integer> findIdByMasterPostId(@Param("masterPostId") Integer masterPostId);

	List<SocialPost> findAllByMasterPostIdIn(List<Integer> id);

	@Modifying
	@Transactional
	void deleteAllByIdIn(List<Integer> postIds);

	List<SocialPost> findByIdInAndApprovalStatusInAndApprovalWorkflowIdInAndCreatedByIn(Set<Integer> distinctPostIds, List<String> approvalState, List<Integer> approveWorkflowIds, List<Integer> creatorIds);

	List<SocialPost> findByIdInAndApprovalStatusIn(Set<Integer> distinctPostIds, List<String> approvalState);

	List<SocialPost> findByIdInAndApprovalStatusInAndCreatedByIn(Set<Integer> distinctPostIds, List<String> approvalState, List<Integer> creatorIds);

	List<SocialPost> findByIdInAndCreatedByIn(Set<Integer> distinctPostIds, List<Integer> creatorIds);

	List<SocialPost> findByIdInAndApprovalStatusInAndApprovalWorkflowIdIn(Set<Integer> distinctPostIds, List<String> approvalState, List<Integer> approveWorkflowIds);

	List<SocialPost> findByIdInAndApprovalWorkflowIdIn(Set<Integer> distinctPostIds, List<Integer> approveWorkflowIds);


	@Query("select a.masterPostId from SocialPost a where a.id = :socialPostId")
    Integer findMasterPostIdById(@Param("socialPostId") Integer socialPostId);

	List<SocialPostLite> findByMasterPostIdIn(List<Integer> masterPostIds);

	@Query("Select s.id from SocialPost s where s.createdDate between :startDate and :endDate ")
	Page<Integer> findByCreatedDate(@Param("startDate") Date startDate,
									@Param("endDate") Date endDate, Pageable pageable);

	@Modifying
	@Transactional
	@Query("UPDATE SocialPost s SET s.parentPostId = :parentPostId where s.id = :id")
	Integer updateParentPostIdFromId(@Param("parentPostId") Integer parentPostId, @Param("id") Integer id);

	List<SocialPost> findByMasterPostIdAndApprovalWorkflowIdIsNotNull(Integer masterPostId);

	@Query(value = "SELECT s.id from SocialPost s where s.masterPostId = :masterPostId AND s.approvalWorkflowId IS NOT NULL")
	List<Integer> findIdByMasterPostIdAndApprovalWorkflowIdIsNotNull(@Param("masterPostId") Integer masterPostId);

	@Modifying
	@Transactional
	@Query(value = "UPDATE SocialPost sp SET sp.approvalStatus = :status where sp.id IN :ids")
	void updateApprovalStatusForIds(@Param("status") String status, @Param("ids") List<Integer> ids);

}
