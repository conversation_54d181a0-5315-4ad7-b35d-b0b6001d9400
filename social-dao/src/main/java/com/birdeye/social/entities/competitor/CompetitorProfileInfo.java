package com.birdeye.social.entities.competitor;

import lombok.*;

import javax.persistence.*;
import java.util.Date;

@Entity
@Builder
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "competitor_profile_info")
public class CompetitorProfileInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;

    @Column(name = "raw_competitor_id")
    private Integer rawCompetitorId;

    @Column(name = "page_id")
    private String pageId;

    @Column(name = "source_id")
    private Integer sourceId;

    @Column(name = "insight_data")
    private String insightData;

    @Column(name = "action")
    private String action;

    @Column(name = "error_log")
    private String errorLog;

    @Column(name = "created_at")
    private Date createdAt;

    @Column(name = "updated_at")
    private Date updatedAt;
}
