package com.birdeye.social.platform.entities;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.birdeye.social.constant.AggregationStatusEnum;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "business_aggregation")
public class BusinessAggregation implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "id")
    private Integer id;

    @Basic(optional =     false)
    @Column(name = "updated")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updated;

    @Basic(optional =     false)
    @Column(name = "created")
    @Temporal(TemporalType.TIMESTAMP)
    private Date created;
    
    @JoinColumn(name = "business_id", referencedColumnName = "id", insertable = false, updatable = false)
    @ManyToOne(optional = false,fetch=FetchType.LAZY)
    private Business business;

    @Column(name="business_id")
    private Integer businessId;
    
    @Basic(optional = false)
    @NotNull
    @Size(min = 1, max = 1000)
    @Column(name = "source_url")
    private String sourceUrl;
    
    @JoinColumn(name = "source_id", referencedColumnName = "id")
    @ManyToOne(optional = false, fetch= FetchType.EAGER)
    private AggregationSource sourceId;
    
    @Column(name = "source_id", insertable = false, updatable = false)
    private Integer srcId;
    
    @JoinColumn(name = "updated_by", referencedColumnName = "id")
    @ManyToOne(fetch=FetchType.LAZY)
    private User updatedBy;
    
    @Column(name = "review_form_url")
    private String reviewFormUrl;
    
    @Column(name = "share_on")
    private String shareOn="n";
    
    @Column(name = "need_url")
    private String needUrl="n";

    @NotNull
    @Column(name = "show_in_req_mail")
    private String showInReqMail = "0";

    @NotNull
    @Column(name = "display_order")
    private Integer displayOrder = 0;

    @NotNull
    @Column(name = "show_in_ref_mail")
    private String showInRefMail = "0";
    
    @Column(name = "source_rating")
    private Float sourceRating;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "password")
    private String password;

    @Column(name = "last_review_date")
    @Temporal(TemporalType.TIMESTAMP)
    private Date lastReviewDate;

    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "is_added_by_competitor")
    private Integer isAddedByCompetitor = 0;

    @Column(name = "aggregation_status")
    private Integer aggregationStatus = AggregationStatusEnum.SEARCH.getId();
            
    @Column(name = "aggregation_servlet_url")
    private String aggregationServletUrl;
    
    @Column(name = "review_scanned")
    private Integer reviewScanned;

    public BusinessAggregation() {
        Calendar cal = Calendar.getInstance();
        cal.set(1970, 01, 02);
        lastReviewDate = cal.getTime();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }

    public AggregationSource getSourceId() {
        return sourceId;
    }

    public void setSourceId(AggregationSource sourceId) {
        this.sourceId = sourceId;
    }

    public User getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(User updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof BusinessAggregation)) {
            return false;
        }
        BusinessAggregation other = (BusinessAggregation) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    /**
	  * To String implemntation for logging the entities.
	  * @return the toString 
	*/
    public String entityLog() {
        return "BusinessAggregation{" + "id=" + id + ", updated=" + updated + ", created=" + created + ", businessId=" + businessId + ", sourceUrl=" + sourceUrl + ", sourceId=" + sourceId + ", updatedBy=" + updatedBy + ", aggregationStatus=" + aggregationStatus + "}";
    }

    

    public Business getBusiness() {
        return business;
    }

    public void setBusiness(Business business) {
        this.business = business;
    }

    public Integer getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public Date getUpdated() {
        return updated;
    }

    public void setUpdated(Date updated) {
        this.updated = updated;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return the reviewFormUrl
     */
    public String getReviewFormUrl() {
        return reviewFormUrl;
    }

    /**
     * @param reviewFormUrl the reviewFormUrl to set
     */
    public void setReviewFormUrl(String reviewFormUrl) {
        this.reviewFormUrl = reviewFormUrl;
    }

    /**
     * @return the shareOn
     */
    public String getShareOn() {
        return shareOn;
    }

    /**
     * @param shareOn the shareOn to set
     */
    public void setShareOn(String shareOn) {
        this.shareOn = shareOn;
    }

    /**
     * @return the needUrl
     */
    public String getNeedUrl() {
        return needUrl;
    }

    /**
     * @param needUrl the needUrl to set
     */
    public void setNeedUrl(String needUrl) {
        this.needUrl = needUrl;
    }

    public Integer getDisplayOrder() {
        return displayOrder;
    }

    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }

    public String getShowInRefMail() {
        return showInRefMail;
    }

    public void setShowInRefMail(String showInRefMail) {
        this.showInRefMail = showInRefMail;
    }

    public String getShowInReqMail() {
        return showInReqMail;
    }

    public void setShowInReqMail(String showInReqMail) {
        this.showInReqMail = showInReqMail;
    }

    public Float getSourceRating() {
        return sourceRating;
    }

    public void setSourceRating(Float sourceRating) {
        this.sourceRating = sourceRating;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Date getLastReviewDate() {
        return lastReviewDate;
    }

    public void setLastReviewDate(Date lastReviewDate) {
        this.lastReviewDate = lastReviewDate;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public Integer getIsAddedByCompetitor() {
        return isAddedByCompetitor;
    }

    public void setIsAddedByCompetitor(Integer isAddedByCompetitor) {
        this.isAddedByCompetitor = isAddedByCompetitor;
    }

    @Override
    public String toString() {
        return "BusinessAggregation{" + "id=" + id + ", updated=" + updated + ", created=" + created + ", businessId=" + businessId + ", sourceUrl=" + sourceUrl + ", sourceId=" + sourceId + ", updatedBy=" + updatedBy + ", reviewFormUrl=" + reviewFormUrl + ", shareOn=" + shareOn + ", needUrl=" + needUrl + ", showInReqMail=" + showInReqMail + ", displayOrder=" + displayOrder + ", showInRefMail=" + showInRefMail + ", sourceRating=" + sourceRating + ", userName=" + userName + ", password=" + password + ", lastReviewDate=" + lastReviewDate + ", profileId=" + profileId + ", isAddedByCompetitor=" + isAddedByCompetitor + ", aggregationStatus=" + aggregationStatus + "}";
    }
    
    public Integer getAggregationStatus() {
        return aggregationStatus;
    }

    public void setAggregationStatus(Integer aggregationStatus) {
        this.aggregationStatus = aggregationStatus;
    }

    public String getAggregationServletUrl() {
        return aggregationServletUrl;
    }

    public void setAggregationServletUrl(String aggregationServletUrl) {
        this.aggregationServletUrl = aggregationServletUrl;
    }

    public Integer getReviewScanned() {
        return reviewScanned;
    }

    public void setReviewScanned(Integer reviewScanned) {
        this.reviewScanned = reviewScanned;
    }   
}
