package com.birdeye.social.platform.entities;

public class BusinessFBPageEntity 
{
	
	private String fbPageId;
	private Integer businessId;
	private String businessName;
	private String businessAlias;
	
	public BusinessFBPageEntity(String fbPageId, Integer businessId, String businessName, String businessAlias) {
		super();
		this.fbPageId = fbPageId;
		this.businessId = businessId;
		this.businessName = businessName;
		this.businessAlias = businessAlias;
	}

	/**
	 * @return the fbPageId
	 */
	public String getFbPageId() {
		return fbPageId;
	}

	/**
	 * @param fbPageId the fbPageId to set
	 */
	public void setFbPageId(String fbPageId) {
		this.fbPageId = fbPageId;
	}

	/**
	 * @return the businessId
	 */
	public Integer getBusinessId() {
		return businessId;
	}

	/**
	 * @param businessId the businessId to set
	 */
	public void setBusinessId(Integer businessId) {
		this.businessId = businessId;
	}

	/**
	 * @return the businessName
	 */
	public String getBusinessName() {
		return businessName;
	}

	/**
	 * @param businessName the businessName to set
	 */
	public void setBusinessName(String businessName) {
		this.businessName = businessName;
	}

	/**
	 * @return the businessAlias
	 */
	public String getBusinessAlias() {
		return businessAlias;
	}

	/**
	 * @param businessAlias the businessAlias to set
	 */
	public void setBusinessAlias(String businessAlias) {
		this.businessAlias = businessAlias;
	}
	

}
