package com.birdeye.social.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.List;
import java.util.Set;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EsPostDataPoint {
    private String page_id;
    private String post_id;
    private String be_post_id;
    private Integer be_post_page_count;
    private String post_content;
    private List<String> image_urls;
    private List<String> video_urls;
    private int impression;
    private int reach;
    private int engagement;
    private double engagement_rate;
    private String posted_date;
    private String post_end_date;
    private String day;
    private Integer source_id;
    private Boolean is_be_post;
    private String page_name;
    private Integer business_id;
    private Long ent_id;
    private Integer like_count;
    private Integer link_click_count;
    private Integer comment_count;
    private Integer share_count;
    private Integer comment_mentions_count;
    private Integer click_count;
    private String post_type;
    private Set<Long> tagIds;
    private Integer video_views;
    private Boolean is_deleted = false;
    private Integer publisher_id;
    private String publisher_name;
    private String publisher_email;
    private Integer image_url_count;
    private Integer video_url_count;
    private Integer link_count;
    private Integer post_text_count;
    private String post_url;
}
