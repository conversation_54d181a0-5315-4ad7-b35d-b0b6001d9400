package com.birdeye.social.external.service;

import com.birdeye.social.dto.PicturesqueMediaResponse;
import com.birdeye.social.dto.PicturesqueMediaMetadataResponse;
import com.birdeye.social.dto.PicturesqueResponse;
import com.birdeye.social.external.request.media.ChunkDataResponse;
import com.birdeye.social.external.request.media.PicturesqueMediaUploadRequest;
import com.birdeye.social.external.request.media.PicturesqueRequest;

import java.util.List;

public interface PicturesqueGen {

    PicturesqueResponse getSingleMediaData(PicturesqueRequest request);

    String uploadMedia(PicturesqueMediaUploadRequest request);

    PicturesqueMediaResponse uploadMediaWithMetaData(PicturesqueMediaUploadRequest request);

    void uploadMediaWithMetaDataInAsync(PicturesqueMediaUploadRequest request) throws Exception;

    List<Boolean> validateImageUrls(List<String> imageUrls);

    PicturesqueMediaMetadataResponse getMediaMetadataByUrl(String mediaUrl);

    boolean uploadLargeVideoUrl(String mediaUrl, String callbackUrl, String callbackId,Long chunkSize);
    ChunkDataResponse getChunkDataFromLink(String chunkLink);
}
