package com.birdeye.social.external.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.dto.PicturesqueMediaMetadataResponse;
import com.birdeye.social.dto.PicturesqueMediaResponse;
import com.birdeye.social.dto.PicturesqueResponse;
import com.birdeye.social.dto.PicturesqueValidateUrlsResponse;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.external.request.media.*;
import com.birdeye.social.utils.JSONUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Service
public class PicturesqueGenImpl implements PicturesqueGen {

    public static final String MULTIPART_URL = "url";
    public static final String CALLBACK_URL = "callbackUrl";
    public static final String CALLBACK_ID = "callbackId";
    public static final String CHUNK_SIZE = "chunkLimit";
    public static final String CHUNK_URL_REQUIRED = "chunkUrlRequired";
    private final Logger LOGGER = LoggerFactory.getLogger(PicturesqueGenImpl.class);

    @Autowired
    RestTemplate restTemplate;

    public static String PICTURESQUE_S3_UPLOAD = "/fileOperation/uploadFileFromUrl";

    public static String PICTURESQUE_SINGLE_METADATA = "/imgGen/generateCollage";

    private final String MEDIA_METADATA_URL = "/fileOperation/getMetadataForImageUrls";

    private final String PICTURESQUE_LARGE_MEDIA_URL = "/multipartUpload/createMultipartChunks";

    public static String PICTURESQUE_VALIDATE_URLS="/fileOperation/validateImageUrls";
    public static String CHUNK_DATA_FETCH ="/fileOperation/getVideoChunkFromUrl";
    public static String CHUNK_DATA_DELETE ="/fileOperation/deleteMediaFromS3bucket";

    @Override
    public PicturesqueResponse getSingleMediaData(PicturesqueRequest request) {
        String picturesqueServiceUrl = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getPicturesqueMediaUploadUrl() + PICTURESQUE_SINGLE_METADATA;
        HttpEntity<PicturesqueRequest> requestEntity = new HttpEntity<>(request);
        try {
            LOGGER.info("Picturesque request :{}", JSONUtils.toJSON(request));
            ResponseEntity<PicturesqueResponse> picturesqueResponseResponseEntity =
                    restTemplate.exchange(picturesqueServiceUrl, HttpMethod.POST, requestEntity, PicturesqueResponse.class);
            if (picturesqueResponseResponseEntity.getStatusCode().equals(HttpStatus.OK)) {
                return picturesqueResponseResponseEntity.getBody();
            } else {
                LOGGER.error("Error while calling picturesque service for request with : response code:{}", picturesqueResponseResponseEntity.getStatusCode());
            }
        } catch (HttpStatusCodeException ex) {
            if (ex.getStatusCode().is5xxServerError()) {
                LOGGER.error("InternalServerException while picturesque  service for URL {} and exception {}", picturesqueServiceUrl, ex.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
            }
            if (ex.getStatusCode().is4xxClientError()) {
                LOGGER.error("ClientException while calling picturesque service for URL {} and exception {}", picturesqueServiceUrl, ex.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
            }
        }
        return null;
    }

    @Override
    public String uploadMedia(PicturesqueMediaUploadRequest request) {
        String picturesqueServiceUrl = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getPicturesqueMediaUploadUrl() + PICTURESQUE_S3_UPLOAD;

        HttpEntity<PicturesqueMediaUploadRequest> requestEntity = new HttpEntity<>(request);
        try{
            LOGGER.info("Picturesque request :{}", JSONUtils.toJSON(request));
            ResponseEntity<Map> picturesqueResponseResponseEntity =
                    restTemplate.exchange(picturesqueServiceUrl, HttpMethod.POST,requestEntity, Map.class);
            if (picturesqueResponseResponseEntity.getStatusCode().equals(HttpStatus.OK)){
                if(Objects.nonNull(picturesqueResponseResponseEntity.getBody().get("midSizeVersionUrl"))) {
                    Map data = (Map) picturesqueResponseResponseEntity.getBody().get("midSizeVersionUrl");
                    return (String) data.get("value");
                }

                if(Objects.nonNull(picturesqueResponseResponseEntity.getBody().get("location"))) {
                    return (String) picturesqueResponseResponseEntity.getBody().get("location");
                }

            } else{
                LOGGER.error("Error while calling picturesque service for request with : response code:{}",picturesqueResponseResponseEntity.getStatusCode());
            }
        }catch (HttpStatusCodeException ex){
            if (ex.getStatusCode().is5xxServerError()) {
                LOGGER.error("InternalServerException while picturesque  service for URL {} and exception {}", picturesqueServiceUrl, ex.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
            }
            if (ex.getStatusCode().is4xxClientError()) {
                LOGGER.error("ClientException while calling picturesque service for URL {} and exception {}", picturesqueServiceUrl, ex.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
            }
        }
        return null;
    }

    @Override
    public PicturesqueMediaMetadataResponse getMediaMetadataByUrl(String mediaUrl) {
        PicturesqueMediaMetadataResponse response = new PicturesqueMediaMetadataResponse();
        if (StringUtils.isBlank(mediaUrl)) {
            return response;
        }
        String picturesqueServiceUrl = new StringBuilder().append(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getPicturesqueMediaUploadUrl())
                                                           .append(MEDIA_METADATA_URL).toString();
       
        LOGGER.info("[PicturesqueGenImpl] getMediaMetadataByUrl called for :{} and mediaUrl:{}", picturesqueServiceUrl, mediaUrl);

        Map<String, Object> request = new HashMap<>();
        request.put("mediaUrls", Arrays.asList(mediaUrl));
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(request);
        try {
            ResponseEntity<PicturesqueMediaMetadataResponse> metadataResponseEntity =
                    restTemplate.exchange(picturesqueServiceUrl, HttpMethod.POST, requestEntity, PicturesqueMediaMetadataResponse.class);
            if (metadataResponseEntity.getStatusCode().equals(HttpStatus.OK)) {
                return metadataResponseEntity.getBody();
            } else {
                LOGGER.error("[PicturesqueGenImpl] getMediaMetadataByUrl Error while calling picturesque service for mediaUrl:{} with : response code:{}",
                             mediaUrl, metadataResponseEntity.getStatusCode());
            }
        } catch (HttpStatusCodeException ex) {
            if (ex.getStatusCode().is5xxServerError()) {
                LOGGER.error("[PicturesqueGenImpl] getMediaMetadataByUrl InternalServerException while picturesque  service for URL {} and exception {}",
                             picturesqueServiceUrl, ex.getResponseBodyAsString());
            }
            if (ex.getStatusCode().is4xxClientError()) {
                LOGGER.error("[PicturesqueGenImpl] getMediaMetadataByUrl ClientException while calling picturesque service for URL {} and exception {}",
                             picturesqueServiceUrl, ex.getResponseBodyAsString());
            }
        }
        return response;
    }

    @Override
    public boolean uploadLargeVideoUrl(String mediaUrl, String callbackUrl, String callbackId,Long chunkSize) {
        String picturesqueServiceUrl = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getPicturesqueMediaUploadUrl() + PICTURESQUE_LARGE_MEDIA_URL;
        Boolean chunkUrlRequired = Boolean.parseBoolean(CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getProperty("chunk.upload.url", "true"));
        if(StringUtils.isEmpty(callbackId) || StringUtils.isEmpty(callbackUrl)){
            return false;
        }
        Map<String, Object> request = new HashMap<>();
        request.put(MULTIPART_URL, mediaUrl);
        request.put(CALLBACK_URL, callbackUrl);
        request.put(CALLBACK_ID, callbackId);
        request.put(CHUNK_SIZE,chunkSize);
        request.put(CHUNK_URL_REQUIRED,chunkUrlRequired);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(request);
        try {
            LOGGER.info("Url :{} and request body : {}",picturesqueServiceUrl,request);
            ResponseEntity<?> uploadMediaUrl =
                    restTemplate.exchange(picturesqueServiceUrl, HttpMethod.POST, requestEntity, Void.class);
            LOGGER.info("Upload media response : {}",uploadMediaUrl);
            if(uploadMediaUrl.getStatusCode().is2xxSuccessful())
                return true;
        }catch (HttpStatusCodeException ex) {
            if (ex.getStatusCode().is5xxServerError()) {
                LOGGER.error("[PicturesqueGenImpl] uploadLargeVideoUrl InternalServerException while picturesque  service for URL {} and exception {}",
                        picturesqueServiceUrl, ex.getResponseBodyAsString());
            }
            if (ex.getStatusCode().is4xxClientError()) {
                LOGGER.error("[PicturesqueGenImpl] uploadLargeVideoUrl ClientException while calling picturesque service for URL {} and exception {}",
                        picturesqueServiceUrl, ex.getResponseBodyAsString());
            }
        }
        return false;
    }

    @Override
    public PicturesqueMediaResponse uploadMediaWithMetaData(PicturesqueMediaUploadRequest request) {
        String picturesqueServiceUrl = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getPicturesqueMediaUploadUrl() + PICTURESQUE_S3_UPLOAD;

        HttpEntity<PicturesqueMediaUploadRequest> requestEntity = new HttpEntity<>(request);
        try{
            PicturesqueMediaResponse response = new PicturesqueMediaResponse();

            LOGGER.info("Picturesque request :{}", JSONUtils.toJSON(request));
            ResponseEntity<Map> picturesqueResponseResponseEntity =
                    restTemplate.exchange(picturesqueServiceUrl, HttpMethod.POST,requestEntity, Map.class);
            LOGGER.info("Picturesque response :{}", picturesqueResponseResponseEntity);

            if (picturesqueResponseResponseEntity.getStatusCode().equals(HttpStatus.OK)){

                if(Objects.nonNull(picturesqueResponseResponseEntity.getBody().get("midSizeVersionUrl"))) {
                    Map data = (Map) picturesqueResponseResponseEntity.getBody().get("midSizeVersionUrl");
                    Map value = (Map) data.get("value");
                    response.setMediaUrl(String.valueOf(value.get("value")));
                    response.setHeight((Integer) value.get("height"));
                    response.setWidth((Integer) value.get("width"));
                    response.setSize((Integer) value.get("size"));
                }

                if(Objects.nonNull(picturesqueResponseResponseEntity.getBody().get("location"))) {
                    String medialUrl = (String) picturesqueResponseResponseEntity.getBody().get("location");
                    response.setMediaUrl(medialUrl);

                    response.setHeight((Integer) picturesqueResponseResponseEntity.getBody().get("height"));
                    response.setWidth((Integer) picturesqueResponseResponseEntity.getBody().get("width"));
                    response.setSize((Integer) picturesqueResponseResponseEntity.getBody().get("size"));
                    // commenting this line because aspect ratio data type is not fixed at picturesque end and same conversion width/height they are doing as per Satyam
                    //response.setAspectRatio((double) picturesqueResponseResponseEntity.getBody().get("aspectRatio"));
                }

                if(Objects.nonNull(response.getHeight()) && Objects.nonNull(response.getWidth())
                        && Objects.isNull(response.getAspectRatio()))  {
                    response.setAspectRatio((double) response.getWidth() / (double) response.getHeight());
                }

                return response;



            } else{
                LOGGER.error("Error while calling picturesque service for request with : response code:{}",picturesqueResponseResponseEntity.getStatusCode());
            }
        }catch (HttpStatusCodeException ex){
            if (ex.getStatusCode().is5xxServerError()) {
                LOGGER.error("InternalServerException while picturesque  service for URL {} and exception {}", picturesqueServiceUrl, ex.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
            }
            if (ex.getStatusCode().is4xxClientError()) {
                LOGGER.error("ClientException while calling picturesque service for URL {} and exception {}", picturesqueServiceUrl, ex.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
            }
        }
        return null;
    }

    @Override
    public void uploadMediaWithMetaDataInAsync(PicturesqueMediaUploadRequest request) throws Exception {
        String picturesqueServiceUrl = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getPicturesqueMediaUploadUrlV2() + PICTURESQUE_S3_UPLOAD;

        HttpEntity<PicturesqueMediaUploadRequest> requestEntity = new HttpEntity<>(request);
        try{
            PicturesqueMediaResponse response = new PicturesqueMediaResponse();

            LOGGER.info("Picturesque request :{}", JSONUtils.toJSON(request));
            ResponseEntity<?> picturesqueResponseResponseEntity =
                    restTemplate.exchange(picturesqueServiceUrl, HttpMethod.POST,requestEntity, Map.class);
            LOGGER.info("Picturesque response :{}", picturesqueResponseResponseEntity);

            if (picturesqueResponseResponseEntity.getStatusCode().equals(HttpStatus.OK)){
                return;
            } else{
                LOGGER.error("Error while calling picturesque service for request with : response code:{}",picturesqueResponseResponseEntity.getStatusCode());
                throw new Exception("Error while calling picturesque service");
            }
        }catch (HttpStatusCodeException ex){
            if (ex.getStatusCode().is5xxServerError()) {
                LOGGER.error("InternalServerException while picturesque  service for URL {} and exception {}", picturesqueServiceUrl, ex.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
            }
            if (ex.getStatusCode().is4xxClientError()) {
                LOGGER.error("ClientException while calling picturesque service for URL {} and exception {}", picturesqueServiceUrl, ex.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
            }
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<Boolean> validateImageUrls(List<String> imageUrls) {
        String picturesqueServiceUrl = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getPicturesqueMediaUploadUrl() + PICTURESQUE_VALIDATE_URLS;
        PicturesqueMediaUrlValidateRequest picturesqueMediaUrlValidateRequest=new PicturesqueMediaUrlValidateRequest();
        picturesqueMediaUrlValidateRequest.setImageUrls(imageUrls);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PicturesqueMediaUrlValidateRequest> requestEntity = new HttpEntity<>(picturesqueMediaUrlValidateRequest,headers);
        try{
            LOGGER.info("Picturesque request :{}", JSONUtils.toJSON(imageUrls));
            ResponseEntity<PicturesqueValidateUrlsResponse> picturesqueResponseResponseEntity =
                    restTemplate.exchange(picturesqueServiceUrl, HttpMethod.POST,requestEntity, PicturesqueValidateUrlsResponse.class);
            if (picturesqueResponseResponseEntity.getStatusCode().equals(HttpStatus.OK)){
                return picturesqueResponseResponseEntity.getBody().getResult();
            } else{
                LOGGER.error("Error while calling picturesque service for request with : response code:{}",picturesqueResponseResponseEntity.getStatusCode());
            }
        }catch (HttpStatusCodeException ex){
            if (ex.getStatusCode().is5xxServerError()) {
                LOGGER.error("InternalServerException while picturesque  service for URL {} and exception {}", picturesqueServiceUrl, ex.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getStatusCode().getReasonPhrase());
            }
            if (ex.getStatusCode().is4xxClientError()) {
                LOGGER.error("ClientException while calling picturesque service for URL {} and exception {}", picturesqueServiceUrl, ex.getResponseBodyAsString());
                throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
            }
        }
        return null;
    }

    @Override
    public ChunkDataResponse getChunkDataFromLink(String chunkLink) {
        String picturesqueServiceUrl = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class).getPicturesqueMediaUploadUrl() + CHUNK_DATA_FETCH;
        ChunkDataRequest chunkDataRequest=new ChunkDataRequest();
        chunkDataRequest.setChunkUrl(chunkLink);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<ChunkDataRequest> requestEntity = new HttpEntity<>(chunkDataRequest,headers);
        try{
            LOGGER.info("Picturesque request to get chunk data:{}", JSONUtils.toJSON(chunkDataRequest));
            ResponseEntity<ChunkDataResponse> chunkDataResponseResponseEntity =
                    restTemplate.exchange(picturesqueServiceUrl, HttpMethod.POST,requestEntity, ChunkDataResponse.class);
            return chunkDataResponseResponseEntity.getBody();
        }catch (HttpStatusCodeException ex){
            LOGGER.error("ClientException while calling picturesque service for URL {} and exception {}", picturesqueServiceUrl, ex.getResponseBodyAsString());
            throw new ExternalAPIException(ExternalAPIErrorCode.CLIENT_ERROR, ex.getStatusCode().getReasonPhrase());
        } catch (Exception ex) {
            LOGGER.error("Exception while picturesque  service for URL {} and exception {}", picturesqueServiceUrl, ex);
            throw new ExternalAPIException(ExternalAPIErrorCode.INTERNAL_SERVER_ERROR, ex.getMessage());
        }
    }


}
