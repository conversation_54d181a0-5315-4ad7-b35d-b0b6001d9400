package com.birdeye.social.facebook;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;

/**
 * API listing for Facebook integration.
 * 
 * <AUTHOR>
 *
 */
public interface FacebookApis {

	
	public static final String GRAPH_API_BASE = "https://graph.facebook.com/";
	
	public static final String GRAPH_API_BASE_V3_1 = GRAPH_API_BASE + "v3.1/";
	
	public static final String GRAPH_API_BASE_V3_2 = GRAPH_API_BASE + "v3.2/";
	
	public static final String GRAPH_API_BASE_V5_0 = GRAPH_API_BASE  + "v5.0/";

	public static final String GRAPH_API_BASE_V11_0 = GRAPH_API_BASE  + "v11.0/";
	public static final String GRAPH_API_BASE_V15_0 = GRAPH_API_BASE  + "v15.0/";
	public static final String GRAPH_API_BASE_V16_0 = GRAPH_API_BASE  + "v16.0/";
	
	// main version
	public static final String VERSION = (CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFbGraphApiVersion() == null ? "" : (CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFbGraphApiVersion() + "/")) ;
	
	// maintaining another version in case we need some APIs on other version : eg Instagram for now. If this version in null, main version will be considered
	public static final String VERSION_2 = (CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFbGraphApiVersionV2() == null ? VERSION : (CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFbGraphApiVersionV2() + "/")) ;
	
	public static final String DEFAULT_API_VERSION = FacebookApis.VERSION == "" ? "v3.1/": FacebookApis.VERSION;

	public static final String GRAPH_API_BASE_WITH_VERSION = GRAPH_API_BASE  + VERSION;
	
	public static final String GRAPH_API_BASE_VERSION_DEFAULT_V31 =  (VERSION == "" ? GRAPH_API_BASE_V3_1 : GRAPH_API_BASE_WITH_VERSION);
	
	public static final String GRAPH_API_BASE_VERSION_DEFAULT_V32 =  (VERSION == "" ? GRAPH_API_BASE_V3_2 : GRAPH_API_BASE_WITH_VERSION);

	public static final String GRAPH_API_BASE_VERSION_DEFAULT_V50 =  (VERSION == "" ? GRAPH_API_BASE_V5_0 : GRAPH_API_BASE_WITH_VERSION);

	public static final String GRAPH_API_BASE_VERSION_DEFAULT_V11 =  (VERSION == "" ? GRAPH_API_BASE_V11_0 : GRAPH_API_BASE_WITH_VERSION);

	public static final String GRAPH_API_BASE_VERSION_DEFAULT_V15 =  (VERSION == "" ? GRAPH_API_BASE_V15_0 : GRAPH_API_BASE_WITH_VERSION);
	public static final String GRAPH_API_BASE_VERSION_DEFAULT_V16 =  (VERSION == "" ? GRAPH_API_BASE_V16_0 : GRAPH_API_BASE_WITH_VERSION);
	
	public static final String GRAPH_API_BASE_WITH_VERSION_2 = GRAPH_API_BASE  + VERSION_2;
	
	
	//https://developers.facebook.com/docs/graph-api/reference/page/tabs/
	public static final String INSTALL_TAB = GRAPH_API_BASE_VERSION_DEFAULT_V31 +"{0}/tabs";

	
	public static final String UNINSTALL_TAB = GRAPH_API_BASE_VERSION_DEFAULT_V31 +"{0}/tabs";
	
	//public static final String DEBUG_TOKEN= GRAPH_API_BASE + "[v3.1]" +"/debug_token";
	public static final String DEBUG_TOKEN = GRAPH_API_BASE_VERSION_DEFAULT_V31 + "debug_token";

	public static final String GET_PAGE_INSIGHTS = GRAPH_API_BASE_VERSION_DEFAULT_V32+"{0}/insights";

	public static final String GET_PAGE_POST_COUNT = GRAPH_API_BASE_VERSION_DEFAULT_V32+"{0}/feed";

	public static final String GET_PAGE_POST = GRAPH_API_BASE_VERSION_DEFAULT_V32+"{0}";



	/**
	 * Access Token API
	 * https://developers.facebook.com/docs/facebook-login/access-tokens/
	 * https://developers.facebook.com/docs/facebook-login/access-tokens/refreshing/
	 */
	//public static final String OAUTH_API = GRAPH_API_BASE + "[v3.1]" +"/oauth/access_token";
	public static final String OAUTH_API = GRAPH_API_BASE_WITH_VERSION +"oauth/access_token";

	/**
	 * Details about current user.
	 */
	// public static final String ME = GRAPH_API_BASE + "[v3.1]/" + "me";
	public static final String ME = GRAPH_API_BASE_WITH_VERSION + "me";
	public static final String CONVERSATION = GRAPH_API_BASE_WITH_VERSION + "me"+"/conversations";
	//Manvi GRAPH_API_BASE_WITH_VERSION +  "/conversations"
	
	
	/**
	 * Account details for current user.
	 */
	public static final String ACCOUNTS = ME + "/accounts";

	/**
	 * https://developers.facebook.com/docs/graph-api/reference/user/
	 */
	//public static final String USER = GRAPH_API_BASE +"[v3.1]" +"/{0}";
	public static final String USER = GRAPH_API_BASE_VERSION_DEFAULT_V31 +"{0}";

	
	/**
	 * https://developers.facebook.com/docs/graph-api/reference/user/accounts/
	 */
	//public static final String USER_ACCOUNTS = GRAPH_API_BASE +"[v3.1]"+ "/{0}/accounts";
	public static final String USER_ACCOUNTS = GRAPH_API_BASE_VERSION_DEFAULT_V31 + "{0}/accounts";

	
	/**
	 * https://developers.facebook.com/docs/graph-api/reference/v12.0/page/locations
	 */
	public static final String PAGE_LOCATIONS = GRAPH_API_BASE_VERSION_DEFAULT_V11 + "{0}/locations";

	public static final String POST_INFO = GRAPH_API_BASE_VERSION_DEFAULT_V11 + "{0}";

	/**
	 * https://developers.facebook.com/docs/graph-api/reference/user/picture/
	 */
	//public static final String USER_PICTURE = GRAPH_API_BASE + "[v3.1]" +"/{0}/picture";
	public static final String USER_PICTURE = GRAPH_API_BASE_VERSION_DEFAULT_V31  +"{0}/picture";

	public static final String USER_DETAIL = GRAPH_API_BASE_VERSION_DEFAULT_V31  +"{0}";

	
	/**
	 * https://developers.facebook.com/docs/graph-api/reference/page/
	 */
	//public static final String PAGE = GRAPH_API_BASE + "[v3.1]" +"/{0}";
	public static final String PAGE = GRAPH_API_BASE_VERSION_DEFAULT_V31 +"{0}";

	/**
	 * https://developers.facebook.com/docs/graph-api/reference/page-category/
	 */
	public static final String FB_PAGE_CATEGORIES = GRAPH_API_BASE_VERSION_DEFAULT_V31 + "fb_page_categories";

	String FB_PAGE_INSIGHTS = GRAPH_API_BASE_WITH_VERSION + "{0}/insights";
	
	/**
	 * https://developers.facebook.com/docs/graph-api/reference/page/photos/
	 */
	//public static final String PAGE_PHOTOS = GRAPH_API_BASE + "[v3.1]" +"/{0}/photos";
	public static final String PAGE_PHOTOS = GRAPH_API_BASE_VERSION_DEFAULT_V31 +"{0}/photos";

	
	/**
	 * https://developers.facebook.com/docs/graph-api/reference/page/videos/
	 */
	public static final String PAGE_VIDEOS = GRAPH_API_BASE_VERSION_DEFAULT_V31 +"{0}/videos";
	
	
	/**
	 * 	https://developers.facebook.com/docs/graph-api/reference/v3.2/page/feed
	 *  The feed of posts (including status updates) and links published by this page, or by others on this page.
	 *  There are other edges which provide more specific sets of posts:
	 */
	//effected
	//public static final String FEED = GRAPH_API_BASE + "[v3.1]" +"/{0}/feed";
	public static final String FEED = GRAPH_API_BASE_VERSION_DEFAULT_V31 +"{0}/feed";

	
	/**
	 * Shows only the posts that were published by this page.
	 */
	//effected
	public static final String POSTS = GRAPH_API_BASE_VERSION_DEFAULT_V31 +"{0}/posts";
	
	/**
	 * https://developers.facebook.com/docs/graph-api/reference/v3.2/post
	 * An individual entry in a profile's feed. The profile could be a user, page, app, or group.
	 */
	//https://graph.facebook.com/v3.1/objectId
	public static final String POST = GRAPH_API_BASE_VERSION_DEFAULT_V31 +"{0}";
	/**
	 * https://developers.facebook.com/docs/graph-api/reference/v3.2/object/comments
	 * Object can be post, album and a comment itself.
	 */
	//public static final String COMMENTS = GRAPH_API_BASE + "[v3.1]" +"/{0}/comments";
	
	public static final String COMMENTS = GRAPH_API_BASE_VERSION_DEFAULT_V31 + "{0}/comments";

	
	/**
	 * https://developers.facebook.com/docs/graph-api/reference/v3.2/object/likes
	 */
	public static final String LIKES = GRAPH_API_BASE_VERSION_DEFAULT_V31 +"{0}/likes";
	
	// API parameter fields.
	public static final String PARAM_CI = "client_id";
	public static final String PARAM_CS = "client_secret";
	public static final String PARAM_REDIRECT_URI = "redirect_uri";
	public static final String PARAM_CODE = "code";
	public static final String PARAM_GT = "grant_type";
	public static final String PARAM_ET = "fb_exchange_token";

	public static final String LIMIT = "limit";
	public static final String FIELDS = "fields";
	public static final String PLATFORM= "platform";
	public static final String METRIC = "metric";
	public static final String PERIOD = "period";
	public static final String SINCE = "since";
	public static final String UNTIL = "until";
	public static final String ACCESS_TOKEN = "access_token";
	public static final String AFTER = "after";
	public static final String FILTER = "filter";

	public static final String FB_POST_METDATA_QUERY_FIELDS = "message,attachments{media_type,media{image,source},target{id},subattachments,description},created_time,sharedposts,shares,from,picture,likes,story,parent_id";
//message,attachments{media_type,media{image,source},target{id},subattachments,description},created_time,sharedposts,shares,from,picture,likes,story,parent_id
	public static final String FB_POST_DATA_FIELDS = "message,attachments,created_time";

	public static final String FB_QUERY_FIELDS = "message,attachments{media_type,media{image,source},subattachments,description,unshimmed_url},comments{comments{can_like,id,from,message,like_count,attachment,created_time},id,from,can_like,like_count,message,attachment,created_time},created_time,sharedposts,shares,from,picture,likes,story,parent_id";

	public static final String FB_COMPETITOR_QUERY_FIELDS = "id,message,created_time,from,permalink_url,shares,reactions.type(LIKE).limit(0).summary(total_count).as(reactions_like),reactions.type(CARE).limit(0).summary(total_count).as(reactions_care),comments.limit(0).filter(stream).summary(1),attachments";

	public static final String ERROR = "error";

	public static final String GET_EXTENDED_FACEBOOK_TOKEN = "getExtendedFacebookToken";
	public static final String MESSAGE = "message";
	
	public static final String	URL		= "https://graph.facebook.com/[Handler]/feed";
	
	public static final String WEBHOOK_SUBSCRIBE = GRAPH_API_BASE_VERSION_DEFAULT_V32 +"{0}/subscriptions";
	
	public static final String PAGE_SUBSCRIBED_APPS = GRAPH_API_BASE_VERSION_DEFAULT_V32 +"{0}/subscribed_apps";
	
	public static final String SEND_MESSAGE = GRAPH_API_BASE_VERSION_DEFAULT_V32 +"{0}/messages";

	public static final String FETCH_INSTAGRAM_INFO = GRAPH_API_BASE_WITH_VERSION + "{0}";
	
	/*https://developers.facebook.com/docs/messenger-platform/instagram/features/send-message*/
	public static final String SEND_MESSAGE_INSTAGRAM = GRAPH_API_BASE_WITH_VERSION + "me/messages";

	public static final String SEARCH_PAGES = GRAPH_API_BASE_VERSION_DEFAULT_V15+"pages/search";

	public static final String GET_IMAGES = GRAPH_API_BASE_VERSION_DEFAULT_V15+"{0}/picture";

	public static final String GET_BATCH_IMAGES = GRAPH_API_BASE_VERSION_DEFAULT_V15+"/me";

	public static final String GET_USER_PAGE_TASKS = GRAPH_API_BASE_VERSION_DEFAULT_V31+"{0}/roles";

	String GET_PAGE_FEED_DETAILS = "created_time,from,attachments,message,comments.summary(total_count),reactions.summary(total_count),likes";
	String GET_PAGE_FEED_DETAILS_ENGAGE = "created_time,from,attachments,message,comments.summary(total_count),reactions.summary(total_count),message_tags";


	String GET_PAGE_FEED_DETAILS_ENGAGE_MENTION = "created_time,from,attachments,message,comments.summary(total_count),reactions.summary(total_count),message_tags";

	String IS_HIDDEN = "is_hidden";

	String PARITY = "parity";

	//page scoped user id
	String PSID = "psid";

	String ASID = "asid";


	String FB_PAGE_USER_BLOCKED = GRAPH_API_BASE_WITH_VERSION + "{0}/blocked";

	String FB_POST_COMMENT_FIELDS = "comment_count,can_reply_privately,attachment,message,created_time,from,parent,like_count";

	String FB_POST_COMMENT_FILTER = "stream";

	String CONNECTION_TIMEOUT_ERROR = "connection time out";

	String POST_ENGAGEMENT_METRIC_COUNT = "shares,reactions.limit(0).summary(total_count),comments.limit(0).summary(total_count)";
}
