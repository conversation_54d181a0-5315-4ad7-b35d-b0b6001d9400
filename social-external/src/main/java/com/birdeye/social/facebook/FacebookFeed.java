package com.birdeye.social.facebook;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Facebook<PERSON>eed implements Serializable {

	/**
	 *
	 */
	private static final long	serialVersionUID	= 9001263676301196658L;
	private String id;
	private String message;
	private FbUser from;
	private FbAttachement attachments;
	private FbComments comments;
	private FbLikes likes;
	private String created_time;
	//Deprecated in graph api v3.1
	private String type;
	private FbShare shares;
	//Deprecated in graph api v3.1
	private String source;
	private String story;
	//Deprecated in graph api v3.1
	private String description;
	private String parent_id;
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public FbUser getFrom() {
		return from;
	}
	public void setFrom(FbUser from) {
		this.from = from;
	}
	public FbAttachement getAttachments() {
		return attachments;
	}
	public void setAttachments(FbAttachement attachments) {
		this.attachments = attachments;
	}
	public FbComments getComments() {
		return comments;
	}
	public void setComments(FbComments comments) {
		this.comments = comments;
	}
	public FbLikes getLikes() {
		return likes;
	}
	public void setLikes(FbLikes likes) {
		this.likes = likes;
	}
	public String getCreated_time() {
		return created_time;
	}
	public void setCreated_time(String created_time) {
		this.created_time = created_time;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
 
	public FbShare getShares() {
		return shares;
	}
	public void setShares(FbShare shares) {
		this.shares = shares;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}
	 
	public String getStory() {
		return story;
	}
	public void setStory(String story) {
		this.story = story;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getParent_id() {
		return parent_id;
	}
	public void setParent_id(String parent_id) {
		this.parent_id = parent_id;
	}
	
	@Override
	public String toString() {
		return "FacebookFeed [id=" + id + ", message=" + message + ", from=" + from + ", attachments=" + attachments + ", comments=" + comments + ", likes=" + likes + ", created_time=" + created_time
				+ ", type=" + type + ", shares=" + shares + ", source=" + source + ", story=" + story + ", description=" + description + ", parent_id=" + parent_id + "]";
	}
	
}
