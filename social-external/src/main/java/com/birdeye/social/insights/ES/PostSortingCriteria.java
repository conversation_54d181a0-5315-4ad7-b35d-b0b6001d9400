package com.birdeye.social.insights.ES;

public enum PostSortingCriteria {
    IMPRESSION("impression"),
    <PERSON><PERSON><PERSON>("reach"),
    ENGAGEMENT("engagement"),
    COMMENT_COUNT("commentCount"),
    SHARE_COUNT("shareCount"),
    LIKE_COUNT("likeCount"),
    POSTED_DATE("posted_date"),
    PUBLISHER_NAME("publisher_name");

    private final String name;

    public String getName() {
        return name;
    }

    PostSortingCriteria(String name) {
        this.name = name;
    }

    public static PostSortingCriteria postSortingCriteria(String name){
        try{
            for (PostSortingCriteria postSortingCriteria : PostSortingCriteria.values()){
                if(postSortingCriteria.getName().equalsIgnoreCase(name)){
                    return postSortingCriteria;
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
}
