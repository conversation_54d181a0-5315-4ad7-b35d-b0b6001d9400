package com.birdeye.social.insights.constants;

import com.birdeye.social.utils.StringUtils;

import java.util.Arrays;
import java.util.List;

public enum InstagramPostInsightMetric {
    impressions,
    reach,
//    engagement, it now replaced with total_interactions
    total_interactions,
    engagement,
//    video_views, deprecated, BIRD-100567
    plays; //video_views (non reel) and plays (reel) give video count,




    public static String getListOfMetric(String postUrl){
        boolean isIGTV = false;
        if(StringUtils.isNotEmpty(postUrl)) isIGTV = postUrl.contains("/tv/");
        List<InstagramPostInsightMetric> metrics  =  isIGTV
                ?Arrays.asList(InstagramPostInsightMetric.impressions,InstagramPostInsightMetric.reach)
                :Arrays.asList(InstagramPostInsightMetric.impressions,InstagramPostInsightMetric.reach,
                InstagramPostInsightMetric.total_interactions);

        StringBuilder list = new StringBuilder();
         for(InstagramPostInsightMetric metric : metrics){
             list.append(metric.name()).append(",");
         }

        return list.toString();
    }

    public static String getReelMetrics(){
        List<InstagramPostInsightMetric> metrics  =  Arrays.asList(InstagramPostInsightMetric.plays,InstagramPostInsightMetric.reach,
                InstagramPostInsightMetric.total_interactions);
        StringBuilder list = new StringBuilder();

        for(InstagramPostInsightMetric metric : metrics){
            list.append(metric.name()).append(",");
        }

        return list.toString();
    }

    public static String getStoryMetrics() {
        List<InstagramPostInsightMetric> metrics  =  Arrays.asList(InstagramPostInsightMetric.impressions,InstagramPostInsightMetric.reach,
                InstagramPostInsightMetric.total_interactions);
        StringBuilder list = new StringBuilder();

        for(InstagramPostInsightMetric metric : metrics){
            list.append(metric.name()).append(",");
        }

        return list.toString();
    }
}
