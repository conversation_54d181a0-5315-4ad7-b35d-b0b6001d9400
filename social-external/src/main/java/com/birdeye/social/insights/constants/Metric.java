package com.birdeye.social.insights.constants;


public enum Metric {

    page_post_engagements,
    page_impressions,
    page_impressions_unique,
    page_fan_removes,
    page_fan_adds,
    page_fans,
    page_daily_follows,
    page_daily_unfollows,
    page_follows,
  //  page_consumptions_by_consumption_type,//depricated
    page_video_views,
    page_actions_post_reactions_total;


    public static String getListOfMetric(){
        StringBuilder list = new StringBuilder();
        try {
            for(Metric metric : Metric.values()){
                list.append(metric.name()).append(",");
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return list.toString();
    }

}
