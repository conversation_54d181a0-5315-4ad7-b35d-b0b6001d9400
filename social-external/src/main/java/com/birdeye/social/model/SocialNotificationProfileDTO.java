package com.birdeye.social.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialNotificationProfileDTO implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 47223072636745668L;
	private String profileId;
	private String profilePictureUrl;
	private String name;
	private String handle;

	public SocialNotificationProfileDTO() {
	}

	public String getProfileId() {
		return profileId;
	}

	public void setProfileId(String profileId) {
		this.profileId = profileId;
	}

	public String getProfilePictureUrl() {
		return profilePictureUrl;
	}

	public void setProfilePictureUrl(String profilePictureUrl) {
		this.profilePictureUrl = profilePictureUrl;
	}

	public String getHandle() {
		return handle;
	}

	public void setHandle(String handle) {
		this.handle = handle;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Override
	public String toString() {
		return "SocialNotificationProfileDTO [profileId=" + profileId + ", profilePictureUrl=" + profilePictureUrl
				+ ", name=" + name + ", handle=" + handle + "]";
	}

}