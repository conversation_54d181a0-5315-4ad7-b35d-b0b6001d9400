package com.birdeye.social.businessgetpage;

import com.birdeye.social.aspect.Profiled;
import com.birdeye.social.entities.BusinessGetPageRequest;

import java.util.List;

public interface IBusinessGetPageService {

    BusinessGetPageRequest save(BusinessGetPageRequest businessGetPageRequest);

    List<BusinessGetPageRequest> saveAll(List<BusinessGetPageRequest> businessGetPageRequest);

    BusinessGetPageRequest saveAndFlush(BusinessGetPageRequest businessGetPageRequest);

    List<Integer> findIdsByEnterpriseIdAndStatusInAndChannel(Long businessId, List<String> statusList, String channel);

    List<BusinessGetPageRequest>  findByEnterpriseIdAndStatusInAndChannel(Long businessId, List<String>  statusList,String channel);

    List<BusinessGetPageRequest>  findByEnterpriseIdAndStatusInAndChannelAndRequestType(Long businessId, List<String>  statusList,String channel, String requestType);

    List<BusinessGetPageRequest> getRequestForBusiness(Long enterpriseId, String status, String channel, String requestType);

    BusinessGetPageRequest findLastRequestByEnterpriseIdAndChannelAndRequestType(Long businessId, String channel, String requestType);

    BusinessGetPageRequest findById(String id);

    BusinessGetPageRequest findOne(Integer id);

    BusinessGetPageRequest findLastRequestByEnterpriseIdAndChannel(Long businessId, String channel);

    Integer fetchUserFromBusinessGetPageRequestId(Integer id);

    List<BusinessGetPageRequest> findAllById(List<Integer> requestIds);

    void updateAll(List<Integer> requests,String status,String errorLog);

    BusinessGetPageRequest findLastRequestByResellerIdAndChannelAndRequestType(Long resellerId, String channel, String requestType);

    @Profiled
    BusinessGetPageRequest findLastRequestByResellerIdAndChannel(Long resellerId, String channel);

    BusinessGetPageRequest findByFreemiumSessionId(Integer sessionId);

	List<BusinessGetPageRequest> findByResellerIdAndStatusInAndChannel(Long businessId, List<String>  statusList,String channel);

}
