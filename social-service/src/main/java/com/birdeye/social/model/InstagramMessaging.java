package com.birdeye.social.model;

import com.birdeye.social.instagram.InstagramMessage;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InstagramMessaging implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long timestamp;
    private InstagramMessage message;
    private InstagramUser sender; // IGSID
    private InstagramUser recipient; // IGID
}
