package com.birdeye.social.model;

import com.birdeye.social.entities.Mention;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialDraftPostResponse {

    private Integer id;
    private String postText;
    private List<MediaData> images;
    private List<String> compressedImages;
    private List<MediaData> videoUrl;
    private List<String> videoThumbnails;
    private String createdDate;
    private String createdBy;
    private String editedDate;
    private String editedBy;
    private List<String> postingSites;
    private List<String> mediaSequence;
    private List<MentionData> mentions;
    private Boolean hasAccess = true;
    private List<String> incompleteChannel;
    private String linkPreviewUrl;
    private Integer duplicatedCount;
    private Boolean isValidDraft;
    private String type;
    private List<SocialTagBasicDetail> tags;
    private Integer approvalWorkflowId;
    @Builder.Default
    private Boolean approvalEnabled = false;
    private Boolean aiPost = false;
}
