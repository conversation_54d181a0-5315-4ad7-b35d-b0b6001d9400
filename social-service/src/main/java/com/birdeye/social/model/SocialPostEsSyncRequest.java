package com.birdeye.social.model;

import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostScheduleInfo;
import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class SocialPostEsSyncRequest {
    private Integer postId;
    private Integer enterpriseId;
    SocialPost socialPost;
    List<SocialPostScheduleInfo> socialPostScheduleInfoList;
    private Boolean isDeleteRequest;
}
