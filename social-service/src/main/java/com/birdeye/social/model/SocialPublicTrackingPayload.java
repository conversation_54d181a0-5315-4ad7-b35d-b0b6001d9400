package com.birdeye.social.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include. NON_NULL)
public class SocialPublicTrackingPayload {

    private String trackingId;
    private Long accountNumber;
    private String socialSite;
    private List<Integer> subBusinessIds;
    private List<SocialPublicPostDetails> socialPostDetails;


    public String getTrackingId() {
        return trackingId;
    }

    public void setTrackingId(String trackingId) {
        this.trackingId = trackingId;
    }

    public Long getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(Long accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getSocialSite() {
        return socialSite;
    }

    public void setSocialSite(String socialSite) {
        this.socialSite = socialSite;
    }

    public List<Integer> getSubBusinessIds() {
        return subBusinessIds;
    }

    public void setSubBusinessIds(List<Integer> subBusinessIds) {
        this.subBusinessIds = subBusinessIds;
    }

    public List<SocialPublicPostDetails> getSocialPostDetails() {
        return socialPostDetails;
    }

    public void setSocialPostDetails(List<SocialPublicPostDetails> socialPostDetails) {
        this.socialPostDetails = socialPostDetails;
    }


    public SocialPublicTrackingPayload() {

    }

}
