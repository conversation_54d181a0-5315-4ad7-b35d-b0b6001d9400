package com.birdeye.social.model;

import com.birdeye.social.insights.ES.CalendarViewPagePostInsightsData;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SocialSchedulePostResponse {
    private Map<String, List<SocialSchedulePostMessage>>  posts;

    private Boolean hasMorePosts = false;

    public Map<String, List<SocialSchedulePostMessage>> getPosts() {
        return posts;
    }

    public void setPosts(Map<String, List<SocialSchedulePostMessage>> posts) {
        this.posts = posts;
    }

    public Boolean getHasMorePosts() {
        return hasMorePosts;
    }

    public void setHasMorePosts(Boolean hasMorePosts) {
        this.hasMorePosts = hasMorePosts;
    }

    @Override
    public String toString() {
        return "SocialSchedulePostResponse{" +
                "posts=" + posts +
                ", hasMorePosts=" + hasMorePosts +
                '}';
    }
}
