package com.birdeye.social.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Helper class to extract information from Social API urls.
 * <AUTHOR>
 *
 */
public class ApiInfoHelper {
	
	private static final String FACEBOOK_GRAPH_URL = "graph.facebook.com/";
	private static final String FB_BASE_URL = "graph.facebook.com";
	private static final String GMB_BASE_URL = "mybusiness.googleapis.com";
	private static final String GMB_MESSAGE_BASE_URL = "businessmessages.googleapis.com";
	private static final String GOOGLE_BASE_URL = "www.googleapis.com/oauth2";
	private static final String GMB_BUSINESS_BASE_URL = "mybusinessbusinessinformation.googleapis.com";
	private static final String GMB_BUSINESS_NOTIFICATION= "mybusinessnotifications.googleapis.com";
	private static final String GOOGLE_MAPS_URL = "maps.googleapis.com";
	private static final String INTERNAL_URL = "api.birdeye.internal";
	private static final String GMB_PLACE_ACTION = "mybusinessplaceactions.googleapis.com";
	private static final String GMB_ACCOUNT_URL = "mybusinessaccountmanagement.googleapis.com";
	private static final String TWITTER_URL = "api.twitter.com";


	private static final String GMB_BUSINESS_PERFORMANCE_BASE_URL= "businessprofileperformance.googleapis.com";
	private static final String LINKEDIN_BASE_URL = "api.linkedin.com";
	private static final String LINKEDIN_BASE_URL2 = "www.linkedin.com";
	private static final String YOUTUBE_BASE_URL = "www.googleapis.com/youtube";
	public static final String USER_PAGES = "USER_PAGES";
	public static final String PAGE_LOCATIONS = "PAGE_LOCATIONS";
	public static final String REVIEWS = "REVIEWS";
	public static final String MENTIONS = "MENTIONS";
	public static final String REVIEW_REPLY = "REVIEW_REPLY";
	public static final String GET_COMMENTS = "GET_COMMENTS";
	public static final String BLOCK_USER = "BLOCK_USER";
	public static final String UNBLOCK_USER = "UNBLOCK_USER";
	public static final String HIDE_CONTENT = "HIDE_CONTENT";
	public static final String LIKE = "LIKE";
	public static final String UNLIKE = "UNLIKE";
	public static final String INSTAGRAM_POST_INFO = "INSTAGRAM_POST_INFO";
	public static final String TABS = "REVIEW_TAB";
	public static final String DEBUG_TOKEN = "DEBUG_TOKEN";
	public static final String SHARE = "SHARE";
	public static final String ACCESS_TOKEN="ACCESS_TOKEN";
	public static final String USER_INFO="USER_INFO";
	public static final String API = "api";
	public static final String EXT_ID = "ext_id";
	public static final String CHANNEL = "channel";
	public static final String  BUSINESS_UPDATE="BUSINESS_UPDATE";
	public static final String APPLE_BASE_URL= "mspgw.push.apple.com";
	public static final String CORE_BUSINESS_BASE_URL = "corebusiness.birdeye.com";
	public static final String FB_REVIEWS_AGGEREGATE_API = "https://graph.facebook.com/v16.0/";

	private static final String TIKTOK_BASE_URL = "open.tiktokapis.com";

	public static final String REPORT_INSIGHT_PAGE_ANALYTICS = "REPORT_INSIGHT_PAGE_ANALYTICS";

	public static final String REPORT_INSIGHT_KEYWORD_ANALYTICS = "REPORT_INSIGHT_KEYWORD_ANALYTICS";
	public static final String SEND_MESSAGE = "SEND_MESSAGE";
	public static final String  MEDIA_UPDATE="MEDIA_UPDATE";
	private static final String INSTALL_FB_TAB = "INSTALL_FB_TAB";
	private static final String UNINSTALL_FB_TAB = "UNINSTALL_FB_TAB";
	private static final String MESSENGER = "MESSENGER";
	private static final String MESSENGER_SUBSCRIBE = "MESSENGER_SUBSCRIBE";
	public static final String INSIGHTS = "INSIGHTS";
	public static final String INSTAGRAM_USER_INFO = "INSTAGRAM_USER_INFO";
	public static final String FACEBOOK_MSGR_USER_INFO = "FACEBOOK_MSGR_USER_INFO";
	public static final String LINKEDIN_POST = "POST_CONTENT";
	public static final String LINKEDIN_CATEGORY = "GET_TARGET_AUDIENCE_OPTIONS";
	public static final String LINKEDIN_REGISTER = "REGISTER_MEDIA";
	public static final String LINKEDIN_UPLOAD = "UPLOAD_MEDIA";
	public static final String LINKEDIN_UPLOAD_STATUS = "UPLOAD_STATUS";
	public static final String FACEBOOK_PAGE_RATING ="FACEBOOK_PAGE_RATING";
	public static final String SOCIAL_POST = "SOCIAL_POST";
	public static final String  BUSINESS_READ="BUSINESS_READ";
	public static final String  NOTIFICATION="NOTIFICATION";
	public static final String  MEDIA_PUBLISH="MEDIA_PUBLISH";
	public static final String YOUTUBE_SEARCH = "YOUTUBE_SEARCH";
	public static final String YOUTUBE_CATEGORIES = "YOUTUBE_CATEGORIES";
	public static final String YOUTUBE_PLAYLIST = "YOUTUBE_PLAYLIST";
	public static final String YOUTUBE_VIDEO_INFO = "YOUTUBE_VIDEO_INFO";
	public static final String YOUTUBE_BAN_AUTHOR = "YOUTUBE_BAN_AUTHOR";
	public static final String YOUTUBE_HIDE_COMMENT = "YOUTUBE_HIDE_COMMENT";
	public static final String YOUTUBE_UNHIDE_COMMENT = "YOUTUBE_UNHIDE_COMMENT";
	public static final String YOUTUBE_REPLY_TO_COMMENT = "YOUTUBE_REPLY_TO_COMMENT";
	public static final String YOUTUBE_CHANNEL_COMMENTS = "YOUTUBE_CHANNEL_COMMENTS";
	public static final String TWITTER_REPORT_AS_SPAM = "TWITTER_REPORT_AS_SPAM";
	public static final String TWITTER_LIKE = "TWITTER_LIKE";
	public static final String TWITTER_UNLIKE = "TWITTER_UNLIKE";
	public static final String TWITTER_POST = "TWITTER_POST";
	public static final String TWITTER_USER_FOLLOW = "TWITTER_USER_FOLLOW";
	public static final String TWITTER_USER_UNFOLLOW = "TWITTER_USER_UNFOLLOW";
	public static final String RETWEET = "RETWEET";
	public static final String LOCATION_FETCH = "LOCATION_FETCH";
	public static final String LINKEDIN_ENGAGE = "POST_ENGAGE";
	public static final String IG_STREAM_COMMENT_POST = "POST_COMMENT";
	public static final String MAPS_READ = "MAPS_READ";
	public static final String MENTION_REMOVE = "MENTION_REMOVE";
	public static final String PAGES_SEARCH = "PAGES_SEARCH";
	public static final String LINKEDIN_POST_GET = "FETCH_CONTENT";
	public static final String WEBHOOK_SUBSCRIBE = "WEBHOOK_SUBSCRIBE";
	public static final String DELETE_OBJECT = "DELETE_OBJECT";
	public static final String FETCH_COMMENTS = "FETCH_COMMENTS";
	public static final String PAGE_READ = "PAGE_READ";





	public static Map<String, String> getApiDetails(String apiName,
			String apiType) {
		String channelName = null;
		String bamIp = CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class)
				.getProperty("social.bam.server.ip");
		Map<String, String> apiRef = new HashMap<>();
		try {
			if (apiName != null) {
				if (apiName.contains(FB_BASE_URL)) {
					channelName = "facebook";
					apiRef = getFBApiRef(apiName, apiType);
				} else if (apiName.contains(GMB_BASE_URL)) {
					channelName = "GMB";
					apiRef = getGMBApiRef(apiName);
				} else if(apiName.contains(GMB_BUSINESS_BASE_URL)|| apiName.contains(GMB_MESSAGE_BASE_URL) ||
						apiName.contains(GMB_BUSINESS_PERFORMANCE_BASE_URL) || apiName.contains(GMB_BUSINESS_NOTIFICATION)){
					channelName = "GMB";
					apiRef = getGMBApiRef(apiName);
				}else if (apiName.contains(GOOGLE_BASE_URL)) {
					channelName = "Google";
					apiRef = getGoogleApiRef(apiName);
				} else if (apiName.contains(bamIp)) {
					channelName = "BAM";
				} else if (apiName.contains(LINKEDIN_BASE_URL) || apiName.contains(LINKEDIN_BASE_URL2)) {
					channelName = "Linkedin";
					apiRef = getLinkedinApiRef(apiName, apiType);
				} else if(apiName.contains(YOUTUBE_BASE_URL)) {
					channelName = "Youtube";
					apiRef = getYoutubeApiRef(apiName, apiType);
				} else if (apiName.contains(APPLE_BASE_URL)) {
					channelName = "apple";
					apiRef = getAppleApiRef(apiName, apiType);
				} else if (apiName.contains(CORE_BUSINESS_BASE_URL)) {
					channelName = "business-core";
					apiRef = getCoreApiRef(apiName, apiType);
				} else if(apiName.contains(GOOGLE_MAPS_URL)) {
					channelName = "Google";
					apiRef = getGoogleMapsApiRef(apiName, apiType);
				} else if(apiName.contains(INTERNAL_URL)) {
					channelName = "internal";
					apiRef = getInternalApiRef(apiName, apiType);
				} else if (apiName.contains(GMB_PLACE_ACTION)) {
					channelName = "GMB";
					apiRef = gmbPlaceActionApiRef(apiName, apiType);
				} else if(apiName.contains(GMB_ACCOUNT_URL)) {
					channelName = "GMB";
					apiRef = gmbAccountApiRef(apiName, apiType);
				} else if (apiName.contains(TWITTER_URL)) {
					channelName = "Twitter";
					apiRef = getTwitterApiRef(apiName, apiType);
				} else if (apiName.contains(TIKTOK_BASE_URL)) {
					channelName = "Tiktok";
					apiRef = getTiktokApiRef(apiName, apiType);
				}
			}

		} catch (Exception e) {
			// Noop
		}
		apiRef.put(CHANNEL, channelName);
		return apiRef;
	}

	private static Map<String, String> getFBApiRef(String apiName,
			String apiType) {
		String api = null;
		String extId = null;
		Map<String, String> map = new HashMap<>();
		if (apiName != null && apiName.contains(FB_BASE_URL)) {
			if (apiName.contains("/accounts")) {
				//https://graph.facebook.com/v6.0/***************/accounts?access_token=EAABm1gNd1MUBADfWUhWDiyhwebnSqeSTnamSdhullZCTFG0FkkaozbsG6bPRMJsgN7b5rbiLyVzgZBhQ2KmkZA5BaGtOamG9c5gope0FCbpG4i8ZAPKLRdZAmUgjcMchhZCHpacZC48SpNbuWLWjcRIHn7UHrCbO2ZCN8AZCcL9n4lgZDZD&fields=id,name,username,link,picture%7Burl%7D,single_line_address,locations%7Bid,name,single_line_address,link,picture%7Burl%7D,access_token%7D,access_token,temporary_status,location%7Blatitude,longitude%7D,emails,start_info,products,payment_options,impressum,phone,description,category,category_list,is_permanently_closed&limit=100
				api = USER_PAGES;
				// Page Id
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/accounts");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/locations")) {
				//https://graph.facebook.com/v6.0/****************/locations?access_token=EAABm1gNd1MUBAFfMUyEZAqzQarIV1aAOEBZBPiPu2BNZBCvDoI6qk1EOGC1EYlYiFZC9WUZAXgVLQzRYTRIHyRC8cNDXOq5S0KusCMfXY0y2vSENeaZCbMnxkPorM1p05bTWlZCYu1BQ1qgOwjEZBeBZBeoEy4FrSkpP47XHdOEFpWDZBSAnGSPAKX&fields=id,name,username,link,picture%7Burl%7D,single_line_address,locations%7Bid,name,single_line_address,link,picture%7Burl%7D,access_token%7D,access_token,temporary_status,location%7Blatitude,longitude%7D,emails,start_info,products,payment_options,impressum,phone,description,category,category_list,is_permanently_closed&limit=100
				api = PAGE_LOCATIONS;
				// Page Id
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/locations");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/ratings")) {
				// https://graph.facebook.com/v5.0/1142312485796735/ratings?fields=open_graph_story,reviewer%7Bid,name,picture.width(200).height(200)%7D&limit=200&access_token=EAABm1gNd1MUBAFBajHbWJ7ZBuF6i3ZA2915aVZC9AFOwV7dkNVHJMq726rIECVZCBvpH252ZC5w5ichrkr9jw3IFptHZBUpg0Ic8cRY0qZCeVlDAUfsjNNBnbbhZCeAe0o5pZB2vH4hZBlDaLfhm4MZBkkEB5NkcV4jpFoIqZA50ALU9EQZDZD
				api = REVIEWS;
				// Page Id
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/ratings");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/messages")) {
				// https://graph.facebook.com/v3.2/me/messages?access_token=EAABm1gNd1MUBABC1D3ckql9IB7BG5YWRPwe2KEhaup4AR1dU3I9wnQbCBpYszZBUZAvIcEeHqWZAjtvO5qoS9TZBCvIZCrv6YwdMhoLqWviU9vY42hDBEMvw6zVaLAMSJyssQG9Lq5ToUKXxd8q5BKjIpN3t1b7c6gMYlETcc18ZBF8nnmJJUw
				api = MESSENGER;
				// Page Id
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/messages");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/subscribed_apps")) {
				// https://graph.facebook.com/v3.2/me/messages?access_token=EAABm1gNd1MUBABC1D3ckql9IB7BG5YWRPwe2KEhaup4AR1dU3I9wnQbCBpYszZBUZAvIcEeHqWZAjtvO5qoS9TZBCvIZCrv6YwdMhoLqWviU9vY42hDBEMvw6zVaLAMSJyssQG9Lq5ToUKXxd8q5BKjIpN3t1b7c6gMYlETcc18ZBF8nnmJJUw
				api = MESSENGER_SUBSCRIBE;
				// Page Id
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/subscribed_apps");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/feed")) {
				// POST
				// https://graph.facebook.com/v2.9/220065381352433/feed?access_token=EAABm1gNd1MUBAFRZAePvZBUI6I1zUcBJDES2StZCU75kO9pZCbXA4AakNLzLEwJr7GIsS73gZBDbd3gUxK5uQgNZCaQKYYFCVuJZC82jPTGLnP8ilZCu1JCj1HzauMOJyTclPs6Gmi3a02ZAG9EraXKZAi93s2Ee9IvK6qSfd2LDZC5FwZDZD
				// GET
				// https://graph.facebook.com/WindstreamEnterprise/feed?fields=created_time,message,story,id,from,name&limit=100&access_token=113069365515461%257CaePk-WDY2ihQtv1WSLOiHqcUIG8
				api = ("GET".equalsIgnoreCase(apiType)) ? MENTIONS : SHARE;
				// Page Name / ID
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/feed");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/comments")) {
				// https://graph.facebook.com/10211486178858935/comments?access_token=EAABm1gNd1MUBAKccMMDmYkdPNYIr6OhUtDvUHlbDKOaS38uE5ScIRT7IklhDbFxfMVyOaBwZAlkUtlS5mEkgpYpaYxSkiy2fK6ZBx1TZCteUDjQGpa2mFQogr9hz4rLRL1Qeahd4wS2dZCByLged6Vri8NgeHSmQNk27ZBBe6MNTIbZAnxuWPo
				api = "POST".equalsIgnoreCase(apiType) ? REVIEW_REPLY : GET_COMMENTS;
				extId = StringUtils.stringBetween(apiName, FACEBOOK_GRAPH_URL, "/comments");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/blocked")) {
				api = "DELETE".equalsIgnoreCase(apiType) ? UNBLOCK_USER : BLOCK_USER;
				extId = StringUtils.stringBetween(apiName, FACEBOOK_GRAPH_URL, "/blocked");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			}else if (apiName.contains("/photos")) {
				// https://graph.facebook.com/10211486178858935/photos?access_token=EAABm1gNd1MUBAKccMMDmYkdPNYIr6OhUtDvUHlbDKOaS38uE5ScIRT7IklhDbFxfMVyOaBwZAlkUtlS5mEkgpYpaYxSkiy2fK6ZBx1TZCteUDjQGpa2mFQogr9hz4rLRL1Qeahd4wS2dZCByLged6Vri8NgeHSmQNk27ZBBe6MNTIbZAnxuWPo
				api = MEDIA_UPDATE;
				// Page ID
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/photos");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/picture")) {
				// https://graph.facebook.com/10211486178858935/picture?access_token=EAABm1gNd1MUBAKccMMDmYkdPNYIr6OhUtDvUHlbDKOaS38uE5ScIRT7IklhDbFxfMVyOaBwZAlkUtlS5mEkgpYpaYxSkiy2fK6ZBx1TZCteUDjQGpa2mFQogr9hz4rLRL1Qeahd4wS2dZCByLged6Vri8NgeHSmQNk27ZBBe6MNTIbZAnxuWPo
				api = MEDIA_UPDATE;
				// Page ID
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/picture");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("debug_token")) {
				//https://graph.facebook.com/v3.1/debug_token?input_token=CAABm1gNd1MUBAGi4cnAKalGp6WlnR3HlBxM0jEWp05aEbqYucjCZBZAX6MXsFYXfMJnU8JFTaDdy1ZBp14ViZBUoXCogZBwBqQsHSk1tgfm4g0EuMgTkb8YT0tpipeEdk8mNfuJt6s9AwGEUPtuXzGTZAgAJQlSYzs5Xd3JIIkoyQBmEaB5SehnhNR35SL7csZD&access_token=113069365515461%7CaePk-WDY2ihQtv1WSLOiHqcUIG8
				api = DEBUG_TOKEN;
			} else if (apiName.contains("tabs")) {
				// https://graph.facebook.com/v2.9/589746578049954/tabs?access_token=EAABm1gNd1MUBACu0NVi72I07vD81d6xFWOtUKSr3ZBnrLWdyDi9AjXqEBLn2reog3jIBdbeJoW464LZCud1v27mAlZC5y0TkwzFnNJvubETuWPjKiUpD0Df53sZBI1oWmoAuGHLiiml3sl430rDB1RLOcqeGox1yhuTRargC4QZDZD
				api = ("POST".equalsIgnoreCase(apiType)) ? INSTALL_FB_TAB : UNINSTALL_FB_TAB;
				// Page ID
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/tabs");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/insights")) {
				//https://graph.facebook.com/v6.0/97357461558/insights?access_token=EAABm1gNd1MUBADvd9hlpLAdtsvShanyOVAQQWkWGP8wF8GfjujfEwZAZAkZCbwZCoysvBl3lHmLKFnNjkm5HWgnUcFjpCxb5z4cw7v68gXqkJennZBgZCdseyI3HiDlm73ooCI7dEGaWQJvH15qOtHoQJNNYjYLWYKSpqtWeC67zOnfsCKdvAlgy2nx1BZBKC4ZD&metric=page_impressions_unique,page_total_actions&since=2021-08-21&until=2021-10-08&period=day
				api = INSIGHTS;
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/insights");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("media_count")) {
				//https://graph.facebook.com/v6.0/97357461558/insights?access_token=EAABm1gNd1MUBADvd9hlpLAdtsvShanyOVAQQWkWGP8wF8GfjujfEwZAZAkZCbwZCoysvBl3lHmLKFnNjkm5HWgnUcFjpCxb5z4cw7v68gXqkJennZBgZCdseyI3HiDlm73ooCI7dEGaWQJvH15qOtHoQJNNYjYLWYKSpqtWeC67zOnfsCKdvAlgy2nx1BZBKC4ZD&metric=page_impressions_unique,page_total_actions&since=2021-08-21&until=2021-10-08&period=day
				api = INSIGHTS;
				extId = StringUtils.stringBetween(apiName,
						"/", "?fields");
			}  else if (apiName.contains("/posts")) {
				//https://graph.facebook.com/v6.0/132283780156970/posts?access_token={token}
				api = SOCIAL_POST;
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/posts");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/media_publish")) {
				//https://graph.facebook.com/v6.0/132283780156970/posts?access_token={token}
				api = MEDIA_PUBLISH;
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/media_publish");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/media")) {
				//https://graph.facebook.com/v6.0/132283780156970/posts?access_token={token}
				api = MEDIA_PUBLISH;
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/media");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/videos")) {
				api = MEDIA_PUBLISH;
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/videos");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/oauth")) {
				api = ACCESS_TOKEN;
			} else if (apiName.contains("/replies")) {
				api = IG_STREAM_COMMENT_POST;
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/replies");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/likes")) {
				api = "DELETE".equalsIgnoreCase(apiType) ? UNLIKE : LIKE;
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "/likes");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("is_hidden")) {
				api = HIDE_CONTENT;
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "?");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			} else if (apiName.contains("/pages/search")) {
				api = PAGES_SEARCH;
			} else if("POST".equalsIgnoreCase(apiType) && !apiName.contains("?access_token") && apiName.contains("_")) {
				api = MEDIA_PUBLISH;
			} else if(apiName.contains("parity=ig_user")) {
				 extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "?");
					api = INSTAGRAM_USER_INFO;
					extId = extId.substring(extId.lastIndexOf('/') + 1);
			}
			//generic handling for post API's
			else if ("POST".equalsIgnoreCase(apiType)) {
				//https://graph.facebook.com/v3.2/1983555075204498?access_token=EAACLXaYYC40BAFBMTwwkjKxPk0NRUxY4XZA7INlSzK3C6DXqp6XzpGQud0KV8w88hw7jCX9jaR9v1WvmZBdiyWl69zZAPBRpku2ZCjk50L0Kd1WaEKEot5aTwEpMoZAi89rrCqoP5Glk4yqWiDeyjxGZBtGZChz9ixnAB46Gwoi3ouNdefdxXyV
				int uriLength = 0;
				try {
					URL url = new URL(apiName);
					if (StringUtils.isNotEmpty(url.getPath()) && url.getPath().contains("/")) {
						uriLength = url.getPath().split("/").length;
					}
				} catch (MalformedURLException e) {
				}
				if (uriLength == 3) {
					api = BUSINESS_UPDATE;
					// Page ID
					extId = StringUtils.stringBetween(apiName,
							FACEBOOK_GRAPH_URL, "?access_token");
					if (extId.contains("/")) {
						extId = extId.substring(extId.lastIndexOf('/') + 1);
					}
				}
			}//generic handling for get API's
			else if ("GET".equalsIgnoreCase(apiType)) {
				String apiInfo = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "?");
				MultiValueMap<String, String> uri=UriComponentsBuilder.fromUriString(apiName).build().getQueryParams();
				String checkFields= null;

				if(CollectionUtils.isNotEmpty(uri.get("fields"))) {
					checkFields = uri.get("fields").get(0);
				}
				if (StringUtils.isNotEmpty(apiInfo) && apiInfo.contains("/")) {
					String[] tokens = apiInfo.split("/");
					if (tokens.length == 2 && Objects.nonNull(checkFields)) {
						extId = tokens[1];
						if (checkFields.contains("followers_count,name,profile_picture_url,username")) {
							//https://graph.facebook.com/v11.0/17841403816795153?access_token=EAABm1gNd1MUBACQsEXZCZAQTtFqJQxp8qrFLZAjiIEoWd3Ls9Us07Ss0YTNgZCN5tfnocjTzZAGuEGTXjW38eG84m96TCpu5CnpH8ggibAL9XfGedZCeVZBE8Qv0KqirdZBso2bpoWU5eEZAmmdGW0rsQMfopMVmA7Xj18SrVkRc9b87UKNI03iCR&fields=followers_count,name,profile_picture_url,username
							api = INSTAGRAM_USER_INFO;
						} else if (checkFields.contains("media_url,caption,id,like_count,media_type,owner,permalink,shortcode,thumbnail_url,timestamp,username")) {
							api = INSTAGRAM_POST_INFO;
						} else if (checkFields.contains("first_name,last_name,id,profile_pic")) {
							//https://graph.facebook.com/v6.0/4229738687148053?access_token=EAABm1gNd1MUBABCjSodO9yZCqZAjMTJzZA4oE4NP7V20j70DlhIdBbx4Qcm7B3DeuTVMAKgwUkvPAQnN3xoLZBj8moiLmC45BOZA6ABeOqpIfSTshSKLkIOyn94ngPeIZBc5YJq6yYQ4qdQF9afzkbZCJguZBHp7fGOnEWUF7TZAZBpAZDZD&fields=first_name,last_name,id,profile_pic
							api = FACEBOOK_MSGR_USER_INFO;
						} else if(checkFields.contains("overall_star_rating,rating_count")) {
							api = FACEBOOK_PAGE_RATING;
							//https://graph.facebook.com/v6.0/1111?fields=overall_star_rating,rating_count&access_token=EAABm1gNd1MUBADP8542Hn5f8ZBzNzZCp7m69YFYPi0koTNTzMyzt9MttvP4BTi0uHYmjqzy058GYtZCfpQJHoKwZASy8lrgZCe0hiQYyQCtsTkf9hwAcg10E2eYzXlgPRVKj47Xnw009izloZArJf5jrhr4z10CUP3EdoA76pw1AZDZD
						} else if(checkFields.contains("mentioned_media")) {
							api = MENTIONS;
							extId = StringUtils.stringBetween(apiName, FACEBOOK_GRAPH_URL, "?fields");
							if (extId.contains("/")) {
								extId = extId.substring(extId.lastIndexOf('/') + 1);
							}
						} else if(apiName.contains("?access_token")) {
							api = BUSINESS_READ;
							extId = StringUtils.stringBetween(apiName,
									FACEBOOK_GRAPH_URL, "?access_token");
							if (extId.contains("/")) {
								extId = extId.substring(extId.lastIndexOf('/') + 1);
							}
						} else if (checkFields.contains("access_token"))  {
							api = BUSINESS_READ;
						} else if(Objects.isNull(checkFields)) {
							api = BUSINESS_READ;
						}
					}
				}
			}
			if ("GET".equalsIgnoreCase(apiType) && apiName.contains("?access_token") && Objects.isNull(api)) {
				api = BUSINESS_READ;
				// Page ID
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "?access_token");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			}
			if("DELETE".equalsIgnoreCase(apiType) && apiName.contains("?access_token") && Objects.isNull(api)) {
				api = DELETE_OBJECT;
				extId = StringUtils.stringBetween(apiName,
						FACEBOOK_GRAPH_URL, "?access_token");
				if (extId.contains("/")) {
					extId = extId.substring(extId.lastIndexOf('/') + 1);
				}
			}
			if(apiName.equalsIgnoreCase(FB_REVIEWS_AGGEREGATE_API) && "POST".equalsIgnoreCase(apiType)) {
				api = FETCH_COMMENTS;
			}
			if(apiName.contains("/me") && "GET".equalsIgnoreCase(apiType)) {
				api = PAGE_READ;
			}
			map.put(API, api);
			map.put(EXT_ID, extId);
		}
		return map;

	}

	private static Map<String, String> getGoogleApiRef(String apiName) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		if (apiName != null) {
			if (apiName.contains("/token")) {
				// https://www.googleapis.com/oauth2/v4/token
				api = ACCESS_TOKEN;
			} else if (apiName.contains("/userinfo")) {
				// https://www.googleapis.com/oauth2/v1/userinfo
				api = USER_INFO;
			}
			map.put(API, api);
		}
		return map;
	}

	private static Map<String, String> getGMBApiRef(String apiName) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		String extId = null;
		// TODO: Use pattern matching
		if (apiName != null) {
			if (apiName.contains("/reply")) {
				// https://mybusiness.googleapis.com/v4/accounts/105247142833187364092/locations/10874319773092602823/reviews/AIe9_BGErQpX2pRNZrXBy5NOsH0el6bEmHFAZkTAbLrZdznXJFVDycYYHO8gkbVbbSehAVQXColsE2p2wR3JOTEV2pLOE1SdHHV7kchHg9Urhj7m01kDlew/reply
				api = REVIEW_REPLY;
				// GMB review Id
				extId = StringUtils.stringBetween(apiName, "reviews/",
						"/reply");
			} else if (apiName.contains("/reviews")) {
				// https://mybusiness.googleapis.com/v4/accounts/100512838622732727086/locations/11535992802279343333/reviews?pageSize=200
				api = REVIEWS;
				// GMB location id
				extId = StringUtils.stringBetween(apiName, "locations/",
						"/reviews");
			}else if(apiName.contains("updateMask")){
				if(apiName.contains("/notificationSetting")) {
					api = NOTIFICATION;
					extId = StringUtils.stringBetween(apiName, "accounts/",
							"/notificationSetting");
				} else {
					api = BUSINESS_UPDATE;
					// GMB location id
					extId = StringUtils.stringBetween(apiName, "locations/",
							"?updateMask");
				}
			} else if(apiName.contains("media")){
				//https://mybusiness.googleapis.com/v4/accounts/111665400866701181021/locations/5781504852190752948/media
				api = MEDIA_UPDATE;
				// GMB location id
				extId = StringUtils.stringBetween(apiName, "locations/",
						"/media");
			} else if(apiName.contains("/conversations")){
				//https://businessmessages.googleapis.com/v1/conversations/7206e260-f817-4da5-9718-a5506dd3f996/messages
				api= SEND_MESSAGE;
				extId = StringUtils.stringBetween(apiName,"conversations/","/messages");
			} else if(apiName.contains("getDailyMetricsTimeSeries")){
				//https://businessprofileperformance.googleapis.com/v1/locations/15625292819159037420:getDailyMetricsTimeSeries
				api= REPORT_INSIGHT_PAGE_ANALYTICS;
				extId = StringUtils.stringBetween(apiName,"locations/",":getDailyMetricsTimeSeries");
			} else if(apiName.contains("/searchkeywords")){
				//https://businessprofileperformance.googleapis.com/v1/locations/3458879900715946439/searchkeywords/impressions/monthly
				api= REPORT_INSIGHT_KEYWORD_ANALYTICS;
				extId = StringUtils.stringBetween(apiName,"locations/","/searchkeywords");
			} else if(apiName.contains("/localPosts")){
				//https://mybusiness.googleapis.com/v4/accounts/117009032617899517275/locations/10818926604158456714/localPosts
				extId = StringUtils.stringBetween(apiName,"locations/","/localPosts");
				if(apiName.contains("reportInsights")) {
					api = INSIGHTS;
				} else {
					api= SOCIAL_POST;
				}
			}
			else if((apiName.contains("read_mask") || apiName.contains("readMask")) && apiName.contains("/locations")){
				//https://mybusinessbusinessinformation.googleapis.com/v1/locations/8913468471185321530?read_mask=name,phoneNumbers,title,storeCode,categories,storefrontAddress,websiteUri,regularHours,specialHours,serviceArea,labels,adWordsLocationExtensions,latlng,openInfo,metadata,profile,relationshipData,moreHours,serviceItems,languageCode
				api = BUSINESS_READ;
				if(apiName.contains("accounts/")) {
					extId = StringUtils.stringBetween(apiName, "accounts/",
							"/locations");
				} else {
					extId = StringUtils.stringBetween(apiName, "locations/",
							"?read_mask");
				}
			} else if(apiName.contains("/VoiceOfMerchantState")){
				//https://mybusinessbusinessinformation.googleapis.com/v1/locations/11412471759800789465/VoiceOfMerchantState
				api = LOCATION_FETCH;
				extId = StringUtils.stringBetween(apiName, "locations/",
						"/VoiceOfMerchantState");
			} else if(apiName.contains("/notifications")){
				//https://mybusiness.googleapis.com/v4/accounts/118352327669871471786/notifications
				api = NOTIFICATION;
				extId = StringUtils.stringBetween(apiName, "accounts/",
						"/notifications");
			} else if(apiName.contains("/attributes") && apiName.contains("/locations")){
				//https://mybusinessbusinessinformation.googleapis.com/v1/locations/5314358311527724188/attributes:getGoogleUpdated
				api = BUSINESS_READ;
				extId = StringUtils.stringBetween(apiName, "locations/",
						"/attributes");
			} else if(apiName.contains("/attributes")){
				https://mybusinessbusinessinformation.googleapis.com/v1/attributes?categoryName=categories/gcid:bakery&languageCode=en&regionCode=US
				api = BUSINESS_READ;
			}
			// https://mybusiness.googleapis.com/v4/accounts/117828152291300688317/locations/5044517946020693090
			map.put(API, api);
			map.put(EXT_ID, extId);
		}
		return map;
	}

	private static Map<String, String> getLinkedinApiRef(String apiName,String apiType) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		String extId = null;
		if (apiName != null) {
			if (apiName.contains("mediaUpload") || apiName.contains("initializeUpload")) {
				api = LINKEDIN_UPLOAD;
			} else if (apiName.contains("registerUpload")) {
				api = LINKEDIN_REGISTER;
			} else if (apiName.contains("/socialActions") || apiName.contains("/reactions")) {
				api = LINKEDIN_ENGAGE;
			} else if (apiName.contains("/ugcPosts") || ("POST".equalsIgnoreCase(apiType) && apiName.contains("/posts"))) {
				api = LINKEDIN_POST;
			} else if ("GET".equalsIgnoreCase(apiType) && apiName.contains("/assets")) {
				api = LINKEDIN_UPLOAD_STATUS;
			} else if ("GET".equalsIgnoreCase(apiType) && apiName.contains("/posts")) {
				api = LINKEDIN_POST_GET;
			}  else if(apiName.contains("networkSizes") || apiName.contains("/organizationalEntityFollowerStatistics")
			|| apiName.contains("/organizationalEntityShareStatistics") || apiName.contains("/socialMetadata")) {
				api = INSIGHTS;
			} else if(apiName.contains("/dms-uploads")) {
				api = LINKEDIN_UPLOAD;
			} else if(apiName.contains("/images")) {
				api = LINKEDIN_POST;
			} else if ("GET".equalsIgnoreCase(apiType)) {
				api = LINKEDIN_CATEGORY;
			} else if (apiName.contains("/accessToken")) {
				api = ACCESS_TOKEN;
			} else if(apiName.contains("/eventSubscriptions")) {
				api = WEBHOOK_SUBSCRIBE;
			} else if (apiName.contains("/introspectToken")) {
				api = DEBUG_TOKEN;
			}
			extId = getExternalId(apiName);
			map.put(API, api);
			map.put(EXT_ID, extId);
		}
		return map;
	}

	private static String getExternalId(String apiName) {

		Pattern pattern = Pattern.compile("organization:(\\d+)");
		if(apiName.contains("organization%3A")) {
			pattern = Pattern.compile("organization%3A(\\d+)");
		}
		Matcher matcher = pattern.matcher(apiName);

		if (matcher.find()) {
			String organizationId = matcher.group(1);
			return organizationId;
		}

		pattern = Pattern.compile("urn:li:person:([^/&,?]+)");
		if(apiName.contains("person%3A")) {
			pattern = Pattern.compile("person%3A([^/&,?]+)");
		}
		matcher = pattern.matcher(apiName);

		if(matcher.find()) {
			String personId = matcher.group(1);
			return personId;
		}

		return null;
	}

	private static Map<String, String> getYoutubeApiRef(String apiName,String apiType) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		String extId = null;
		if (apiName != null) {
			if (apiName.contains("/search")) {
				api = YOUTUBE_SEARCH;
			} else if (apiName.contains("/videoCategories")) {
				api = YOUTUBE_CATEGORIES;
			} else if (apiName.contains("/playlists")) {
				api = YOUTUBE_PLAYLIST;
			} else if (apiName.contains("/commentThreads")) {
				api = YOUTUBE_CHANNEL_COMMENTS;
				extId = StringUtils.stringBetween(apiName, "allThreadsRelatedToChannelId=", "&");
			} else if (apiName.contains("/video")) {
				api = YOUTUBE_VIDEO_INFO;
				extId = apiName.substring(apiName.lastIndexOf("id=") + 3);
			} else if (apiName.contains("/comments")) {
				if (apiName.contains("banAuthor")) {
					api = YOUTUBE_BAN_AUTHOR;
					extId = StringUtils.stringBetween(apiName, "id=", "&");
				} else if (apiName.contains("/setModerationStatus")) {
					api = apiName.contains("published") ? YOUTUBE_UNHIDE_COMMENT : YOUTUBE_HIDE_COMMENT;
					extId = StringUtils.stringBetween(apiName, "id=", "&");;
				} else {
					api = YOUTUBE_REPLY_TO_COMMENT;
				}
			}
			map.put(API, api);
			map.put(EXT_ID, extId);
		}
		return map;
	}

	private static Map<String, String> getAppleApiRef(String apiName,String apiType) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		String extId = null;
		if (apiName != null) {
			if (apiName.contains("/message")) {
				api = MESSENGER;
			}
			map.put(API, api);
			map.put(EXT_ID, extId);
		}
		return map;
	}

	private static Map<String, String> getCoreApiRef(String apiName,String apiType) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		String extId = null;
		if (apiName != null) {
			if (apiName.contains("/getBusinessLite")) {
				api = BUSINESS_READ;
			} else if (apiName.contains("/businessoptionsByNumber")) {
				api = BUSINESS_READ;
			} else if (apiName.contains("/custom-hierarchy-loc-mapping")) {
				api = BUSINESS_READ;
			} else if(apiName.contains("/businessoptions")) {
				api = BUSINESS_READ;
				extId = StringUtils.stringBetween(apiName, "?businessId=", "&");
			} else if(apiName.contains("/business/profile")) {
				api = BUSINESS_READ;
			} else if (apiName.contains("v1/user")) {
				api = BUSINESS_READ.concat("_USER_DETAILS");
			} else if (apiName.contains("/businessHierarchyList")) {
				api = BUSINESS_READ.concat("_HIERARCHY_LIST");
			}
			map.put(API, api);
			map.put(EXT_ID, extId);
		}
		return map;
	}

	private static Map<String, String> getGoogleMapsApiRef(String apiName,String apiType) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		String extId = null;
		if (apiName != null) {
			if (apiName.contains("/place/details")) {
				api = MAPS_READ;
			}
			map.put(API, api);
			map.put(EXT_ID, extId);
		}
		return map;
	}

	private static Map<String, String> getInternalApiRef(String apiName,String apiType) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		String extId = null;
		if (apiName != null) {
			if (apiName.contains("mention/remove")) {
				api = MENTION_REMOVE;
			}
			map.put(API, api);
			map.put(EXT_ID, extId);
		}
		return map;
	}

	private static Map<String, String> gmbPlaceActionApiRef(String apiName, String apiType) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		String extId = null;
		if (apiName != null) {
			if (apiName.contains("/placeActionLinks") && ("POST".equalsIgnoreCase(apiType) || "PATCH".equalsIgnoreCase(apiType))) {
				// https://www.googleapis.com/oauth2/v4/token
				api = "UPDATE_LOCATION_DETAILS";
				extId = StringUtils.stringBetween(apiName, "locations/",
						"/placeActionLinks");
			} else if (apiName.contains("/placeActionLinks") && "GET".equalsIgnoreCase(apiType)) {
				// https://www.googleapis.com/oauth2/v4/token
				api = "FETCH_LOCATION_DETAILS";
				extId = StringUtils.stringBetween(apiName, "locations/",
						"/placeActionLinks");
			}  else if (apiName.contains("/placeActionLinks") && "GET".equalsIgnoreCase(apiType)) {
				// https://www.googleapis.com/oauth2/v4/token
				api = "FETCH_LOCATION_DETAILS";
				extId = StringUtils.stringBetween(apiName, "locations/",
						"/placeActionLinks");
			}

			map.put(EXT_ID, extId);
			map.put(API, api);
		}
		return map;
	}

	private static Map<String, String> gmbAccountApiRef(String apiName, String apiType) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		String extId = null;
		if (apiName != null) {
			if (apiName.contains("/accounts") && "GET".equalsIgnoreCase(apiType)) {
				api = "FETCH_ACCOUNT_DETAILS";
			}

			map.put(EXT_ID, extId);
			map.put(API, api);
		}
		return map;
	}


	private static Map<String, String> getTwitterApiRef(String apiName, String apiType) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		String extId = null;
		if (apiName != null) {
			if (apiName.contains("/report_spam.json")) {
				api = TWITTER_REPORT_AS_SPAM;
				extId = apiName.substring(apiName.lastIndexOf("=") + 1);
			} else if (apiName.contains("/likes")) {
				api = "DELETE".equalsIgnoreCase(apiType) ? TWITTER_UNLIKE : TWITTER_LIKE ;
				extId = StringUtils.stringBetween(apiName, "/users/", "/likes");
			} else if (apiName.contains("/tweets")) {
				api = TWITTER_POST;
			} else if (apiName.contains("/following")) {
				api = "DELETE".equalsIgnoreCase(apiType) ? TWITTER_USER_UNFOLLOW : TWITTER_USER_FOLLOW;
				extId = StringUtils.stringBetween(apiName, "/users/", "/following");
			} else if (apiName.contains("/retweets")) {
				api = RETWEET;
			}
			map.put(API, api);
			map.put(EXT_ID, extId);
		}
		return map;
	}

	private static Map<String, String> getTiktokApiRef(String apiName, String apiType) {
		Map<String, String> map = new HashMap<>();
		String api = null;
		String extId = null;
		if (apiName != null) {
			if (apiName.contains("/oauth/token")) {
				api = DEBUG_TOKEN;
			} else if (apiName.contains("/user/info")) {
				api = USER_INFO;
			}
			map.put(API, api);
			map.put(EXT_ID, extId);
		}
		return map;
	}


}
