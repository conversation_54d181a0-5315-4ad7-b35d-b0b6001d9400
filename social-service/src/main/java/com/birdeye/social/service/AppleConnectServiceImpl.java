package com.birdeye.social.service;

import com.birdeye.social.apple.AppleErrorResponse;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.applePost.*;
import com.birdeye.social.sro.appleMaps.*;
import com.birdeye.social.sro.applePost.AppleShowcaseDetails;
import com.birdeye.social.utils.JSONUtils;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.*;

import static com.birdeye.social.constant.KafkaTopicEnum.APPLE_DELETE_INVALID_LOCATION;

@Service
public class AppleConnectServiceImpl implements AppleConnectService {

    @Value("${apple.client.id}")
    private String clientId;
    @Value("${apple.client.secret}")
    private String clientSecret;
    @Autowired
    @Qualifier("socialRestTemplate")
    private RestTemplate socialRestTemplate;

    @Autowired
    private KafkaProducerService producer;
    private static final Logger logger = LoggerFactory.getLogger(AppleConnectServiceImpl.class);
    private static final String APPLE_QUICK_REPORT_URL = "/companies/%s/reports/quick-report";

    private static final String AUTH_TOKEN_API = "/oauth2/token";
    private static final String GET_LOCATION_API = "/companies/%s/locations?ql=businessId==%s";
    private static final String UPLOAD_MEDIA_API = "/companies/%s/images/import";
    private static final String GET_BUSINESS_ASSET_API = "/companies/%s/businesses/%s/assets/%s";
    private static final String CREATE_BUSINESS_ASSET_API = "/companies/%s/businesses/%s/assets";
    private static final String UPDATE_BUSINESS_ASSET_API = "/companies/%s/businesses/%s/assets/%s";
    private static final String GET_IMAGE_METADATA = "/companies/%s/images/%s/metadata";
    private static final String DELETE_BUSINESS_ASSET_API = "/companies/%s/businesses/%s/assets/%s";
    private static final String GET_BUSINESS_API = "/companies/%s/businesses/%s";
    private static final String GET_INVALID_LOCATION_API = "/companies/%s/locations/%s";

    private String generateBearerToken() {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrl();
        String uri = baseUrl.concat(AUTH_TOKEN_API);
        AppleCredsDto credentials = AppleCredsDto.builder().client_id(this.clientId).client_secret(this.clientSecret)
                .grant_type(Constants.APPLE_GRANT_TYPE).scope(Constants.APPLE_SCOPE).build();
        ResponseEntity<AppleCredsDto> response;
        try {
            logger.info("uri to getLocationForBusiness {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.POST, new HttpEntity<>(credentials, null),
                    AppleCredsDto.class);
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while getting apple token: {}", e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple token:", e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        AppleCredsDto tokenDto = response.getBody();
        return tokenDto.getToken_type().concat(" ").concat(tokenDto.getAccess_token());
    }

    private HttpHeaders getHttpHeaders(final Map<String, String> providedHeaders) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set(HttpHeaders.AUTHORIZATION, this.generateBearerToken());
        if (MapUtils.isNotEmpty(providedHeaders)) {
            providedHeaders.keySet().forEach(h -> httpHeaders.set(h, providedHeaders.get(h)));
        }
        return httpHeaders;
    }

    private <T> HttpEntity<T> getHttpEntity(T body, final Map<String, String> extraHeaders) {
        return new HttpEntity<T>(body, getHttpHeaders(extraHeaders));
    }

    @Override
    public AppleLocationData getLocationForBusiness(String companyId, String businessId, String after) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat(String.format(GET_LOCATION_API, companyId, businessId));
        if (StringUtils.isNotEmpty(after)) {
            uri = uri.concat("&after=").concat(after);
        }
        ResponseEntity<AppleLocationData> response;
        try {
            logger.info("uri to getLocationForBusiness {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.GET, getHttpEntity(null, null),
                    AppleLocationData.class);
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while getting apple location info for url {}: {}", uri,
                    e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple location info for url: {} :", uri, e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        return response.getBody();
    }

    @Override
    public AppleImageUploadResponse mediaUpload(String companyId, AppleImageUploadRequest request) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrl();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/images/import");
        ResponseEntity<AppleImageUploadResponse> response;
        try {
            logger.info("uri to mediaUpload {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.POST, getHttpEntity(request, null), AppleImageUploadResponse.class);
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while mediaUpload for url {}: {}",uri,e.getResponseBodyAsString());
            throw e;
        } catch (Exception e) {
            logger.info("Exception while mediaUpload for url: {} :",uri,e);
            throw e;
        }
        return response.getBody();
    }

    @Override
    public AppleShowcaseCreativeResponse appleShowcaseCreativeGenerate(String companyId, String businessId, AppleShowcaseCreativeRequest request) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/businesses/").concat(businessId).concat("/showcase-creatives");
        ResponseEntity<AppleShowcaseCreativeResponse> response;
        try {
            logger.info("uri to appleShowcaseCreativeGenerate {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.POST, getHttpEntity(request, null), AppleShowcaseCreativeResponse.class);
        } catch (HttpStatusCodeException e) {
            logger.error("HttpStatusCodeException while appleShowcaseCreativeGenerate for url {}: {}",uri,e.getResponseBodyAsString());
            throw e;
        } catch (Exception e) {
            logger.error("Exception while appleShowcaseCreativeGenerate for url: {} :",uri,e);
            throw e;
        }
        return response.getBody();
    }

    @Override
    public AppleShowcaseResponse appleShowcaseGenerate(String companyId, String businessId, AppleShowcaseDetails request) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/businesses/").concat(businessId).concat("/showcases");
        ResponseEntity<AppleShowcaseResponse> response;
        try {
            logger.info("uri to appleShowcaseGenerate {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.POST, getHttpEntity(request, null), AppleShowcaseResponse.class);
        } catch (HttpStatusCodeException e) {
            logger.error("HttpStatusCodeException while appleShowcaseGenerate for url {}: {}",uri,e.getResponseBodyAsString());
            throw e;
        } catch (Exception e) {
            logger.error("Exception while appleShowcaseGenerate for url: {} :",uri,e);
            throw e;
        }
        return response.getBody();
    }

    @Override
    public AppleImageUploadResponse getShowcaseImageStatus(String companyId, String imageId) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrl();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/images/").concat(imageId).concat("/metadata");
        ResponseEntity<AppleImageUploadResponse> response;
        try {
            logger.info("uri to getShowcaseImageStatus {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.GET, getHttpEntity(null, null), AppleImageUploadResponse.class);
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while getting apple location info for url {}: {}",uri,e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple location info for url: {} :",uri,e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        return response.getBody();
    }

    @Override
    public AppleShowcaseCreativeResponse getShowcaseCreativeStatus(String companyId, String businessId, String creativeId) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/businesses/").concat(businessId).concat("/showcase-creatives/").concat(creativeId);
        ResponseEntity<AppleShowcaseCreativeResponse> response;
        try {
            logger.info("uri to getShowcaseCreativeStatus {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.GET, getHttpEntity(null, null), AppleShowcaseCreativeResponse.class);
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while getting apple location info for url {}: {}",uri,e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple location info for url: {} :",uri,e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        return response.getBody();
    }

    @Override
    public AppleShowcaseResponse getShowcaseStatus(String companyId, String businessId, String showcaseId) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/businesses/").concat(businessId).concat("/showcases/").concat(showcaseId);
        ResponseEntity<AppleShowcaseResponse> response;
        try {
            logger.info("uri to getShowcaseStatus {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.GET, getHttpEntity(null, null), AppleShowcaseResponse.class);
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while getting apple location info for url {}: {}",uri,e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple location info for url: {} :",uri,e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        return response.getBody();
    }

    @Override
    public Boolean deleteShowcase(String companyId, String businessId, String showcaseId, String eTag) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/businesses/").concat(businessId).concat("/showcases/").concat(showcaseId);
        ResponseEntity<AppleShowcaseResponse> response;
        Map<String, String> headers = new HashMap<>();
        headers.put("if-match", eTag);
        try {
            logger.info("uri to deleteShowcase {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.DELETE, getHttpEntity(null, headers), AppleShowcaseResponse.class);
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while getting apple location info for url {}: {}",uri,e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple location info for url: {} :",uri,e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        if(response.getStatusCode().is2xxSuccessful()) {
            return true;
        }
        return false;
    }

    @Override
    public Boolean deactivateShowcase(String companyId, String businessId, String showcaseId, String eTag) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/businesses/").concat(businessId).concat("/showcases/")
                .concat(showcaseId).concat("/deactivate");
        ResponseEntity<AppleShowcaseResponse> response;
        Map<String, String> headers = new HashMap<>();
        headers.put("if-match", eTag);
        try {
            logger.info("uri to deactivateShowcase {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.POST, getHttpEntity(null, headers), AppleShowcaseResponse.class);
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while getting apple location info for url {}: {}",uri,e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple location info for url: {} :",uri,e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        if(Objects.nonNull(response.getBody()) && response.getBody().getState().equalsIgnoreCase(ApplePublishStateEnum.DELETED.getName())) {
            return true;
        }
        return false;
    }
    @Override
    public AppleShowcaseResponse updateShowcase(String companyId, String businessId, String showcaseId, AppleShowcaseUpdateRequest request, String showcaseETag) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/businesses/").concat(businessId).concat("/showcases/").concat(showcaseId);
        ResponseEntity<AppleShowcaseResponse> response;
        Map<String, String> headers = new HashMap<>();
        headers.put("if-match", showcaseETag);
        try {
            logger.info("uri to updateShowcase {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.PUT, getHttpEntity(request, headers), AppleShowcaseResponse.class);
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while updating apple showcase for url {}: {}",uri,e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple updating apple showcase for url: {} :",uri,e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        return response.getBody();
    }

    @Override
    public AppleShowcaseResponse extendShowcase(String companyId, String businessId, String showcaseId, AppleShowcaseUpdateRequest request, String showcaseETag) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/businesses/").concat(businessId).concat("/extend/").concat(showcaseId).concat("/extend");
        ResponseEntity<AppleShowcaseResponse> response;
        Map<String, String> headers = new HashMap<>();
        headers.put("if-match", showcaseETag);
        try {
            logger.info("uri to extendShowcase {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.POST, getHttpEntity(request, headers), AppleShowcaseResponse.class);
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while updating apple showcase for url {}: {}",uri,e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple updating apple showcase for url: {} :",uri,e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        return response.getBody();
    }

    @Override
    public AppleShowcaseCreativeResponse appleShowcaseCreativeUpdate(String companyId, String businessId,
                                                                     AppleUpdateShowcaseCreativeRequest request,
                                                                     String showcaseETag) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/businesses/").concat(businessId)
                .concat("/showcase-creatives/".concat(request.getId()));
        ResponseEntity<AppleShowcaseCreativeResponse> response ;
        Map<String, String> headers = new HashMap<>();
        headers.put("if-match", showcaseETag);
        try {
            logger.info("uri to appleShowcaseCreativeUpdate {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.PUT, getHttpEntity(request, headers), AppleShowcaseCreativeResponse.class);
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while getting apple location info for url {}: {}",uri,e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple location info for url: {} :", uri, e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        return response.getBody();
    }
    public AppleCTAResponse getAppleCTA(String companyId, String businessId, String locationId) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/businesses/").concat(businessId)
                .concat("/locations/").concat(locationId).concat("/showcase-calls-to-action");
        ResponseEntity<AppleCTAResponse> response;
        try {
            logger.info("uri to getAppleCTA {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.GET, getHttpEntity(null, null), AppleCTAResponse.class);
            logger.info("response: {}",response.getBody());
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while getting apple CTA info for url {}: {}",uri,e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple CTA info for url: {} :",uri,e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        return response.getBody();
    }

    @Override
    public AppleFeedbackResponse getAppleFeedBack(String companyId, String resourceType, String id) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat("/companies/").concat(companyId).concat("/feedback").concat("?ql=resourceType==")
                .concat(resourceType).concat(";resourceId==").concat(id);
        ResponseEntity<AppleFeedbackResponse> response;
        try {
            logger.info("uri to getAppleFeedBack {}",uri );
            response = socialRestTemplate.exchange(uri, HttpMethod.GET, getHttpEntity(null, null), AppleFeedbackResponse.class);
            logger.info("response: {}", response.getBody());
        } catch (HttpStatusCodeException e) {
            logger.info("HttpStatusCodeException while getting apple feedback info for url {}: {}", uri, e.getResponseBodyAsString());
            throw new BirdeyeSocialException(e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.info("Exception while getting apple feedback info for url: {} :", uri, e);
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
        return response.getBody();
    }
    public AppleInsightsConnectResponse getAppleInsightsData(AppleInsightsConnectRequest appleInsightsConnectRequest) {
        logger.info("getAppleInsightsData called with request : {}", appleInsightsConnectRequest);
        StringBuilder uri = new StringBuilder(
                CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2());
        uri.append(String.format(APPLE_QUICK_REPORT_URL, appleInsightsConnectRequest.getCompanyId()));
        try {
            logger.info("uri to getAppleInsightsData {}",uri );
            ResponseEntity<AppleInsightsConnectResponse> response = socialRestTemplate.exchange(uri.toString(),
                    HttpMethod.POST, getHttpEntity(appleInsightsConnectRequest, null),
                    AppleInsightsConnectResponse.class);
            return response.getBody();
        } catch (HttpStatusCodeException e) {
            logger.error("HttpStatusCodeException: {}  while calling getAppleInsightsData for url {}", e.getResponseBodyAsString(), uri);
            if (e.getRawStatusCode() == 400 || e.getRawStatusCode() == 404) {
                AppleDeleteLocationRequest appleDeleteLocationRequest= new AppleDeleteLocationRequest(appleInsightsConnectRequest.getCompanyId(),appleInsightsConnectRequest.getResourceId()) ;
                checkAndPushToDeleteLocation(e,appleDeleteLocationRequest);
                return null;
            }
            if (e.getRawStatusCode() == 429) {
                throw new BirdeyeSocialException(ErrorCodes.APPLE_BUSINESS_CONNECT_RATE_LIMIT_ERROR,
                        "Apple business connect rate limit exceeded. Please try again after sometime.");
            }
            throw new BirdeyeSocialException(ErrorCodes.APPLE_BUSINESS_REPORT_ERROR, "API request failed");
        } catch (Exception e) {
            logger.error("Exception : {} occurred while calling getAppleInsightsData for url: {} :", e, uri);
            throw new BirdeyeSocialException(e.getMessage());
        }

    }

    private void checkAndPushToDeleteLocation(HttpStatusCodeException e, AppleDeleteLocationRequest appleDeleteLocationRequest) {

        try {
            AppleErrorResponse[] appleErrorResponseList = new ObjectMapper().disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES).
                    readValue(e.getResponseBodyAsString(), AppleErrorResponse[].class);
                if (!ObjectUtils.isEmpty(appleErrorResponseList)) {
                    Boolean statusCode=Constants.INSIGHTS_INVALID.equals(appleErrorResponseList[0].getCode());
                    if(statusCode) {
                        producer.sendObjectV1(APPLE_DELETE_INVALID_LOCATION.getName(), appleDeleteLocationRequest);
                    }
                }
             }catch (Exception ex){
            logger.info("Exception occurred while pushing data to apple location delete request for location :{} with exception :{}",
                    appleDeleteLocationRequest.getLocationId(),ex);
        }
    }

    @Override
    public AppleResponseDto uploadMedia(final String appleCompanyId, final AppleDto appleDto) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrl();
        String uri = baseUrl.concat(String.format(UPLOAD_MEDIA_API, appleCompanyId));
        try {
            ResponseEntity<Object> response = this.socialRestTemplate.exchange(uri, HttpMethod.POST,
                    getHttpEntity(appleDto, null), Object.class);
            AppleResponseDto appleResponseDto = new AppleResponseDto();
            if (!response.getStatusCode().is2xxSuccessful()) {
                logger.error("Error calling Apple import image for appleCompanyId:{}, response:{}", appleCompanyId,
                        JSONUtils.toJSON(response));
                appleResponseDto.setValidationReportsList(
                        JSONUtils.convertListToType(response.getBody(), AppleValidationReports.class));
            } else {
                appleResponseDto.setAppleDto(JSONUtils.convertObjectToType(response.getBody(), AppleDto.class));
            }
            return appleResponseDto;
        } catch (Exception e) {
            logger.info("Exception while calling upload media api:{}, ex:{}", uri, e.getStackTrace());
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
    }

    @Override
    public AppleResponseDto getBusinessAsset(final String appleCompanyId, final String appleBusinessId,
            final String businessAssetId) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl
                .concat(String.format(GET_BUSINESS_ASSET_API, appleCompanyId, appleBusinessId, businessAssetId));
        AppleResponseDto appleResponseDto = new AppleResponseDto();
        try {
            ResponseEntity<Object> response = this.socialRestTemplate.exchange(uri, HttpMethod.GET,
                    getHttpEntity(null, null), Object.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                logger.error(
                        "Error calling Apple get Business Asset for appleCompanyId:{}, appleBusinessId:{}, response:{}",
                        appleCompanyId, appleBusinessId, JSONUtils.toJSON(response));
                appleResponseDto.setValidationReportsList(
                        JSONUtils.convertListToType(response.getBody(), AppleValidationReports.class));
            } else {
                appleResponseDto.setAppleDto(JSONUtils.convertObjectToType(response.getBody(), AppleDto.class));
            }
            return appleResponseDto;
        } catch (Exception e) {
            logger.info("Exception while calling get businessAsset api:{}, ex:{}", uri, e.getStackTrace());
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
    }

    @Override
    public AppleResponseDto createBusinessAsset(final String appleCompanyId, final String appleBusinessId,
            final AppleDto appleDto) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat(String.format(CREATE_BUSINESS_ASSET_API, appleCompanyId, appleBusinessId));
        AppleResponseDto appleResponseDto = new AppleResponseDto();
        try {
            ResponseEntity<Object> response = this.socialRestTemplate.exchange(uri, HttpMethod.POST,
                    getHttpEntity(appleDto, null), Object.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                logger.error(
                        "Error calling Apple create Business Asset for appleCompanyId:{}, appleBusinessId:{}, response:{}",
                        appleCompanyId, appleBusinessId, JSONUtils.toJSON(response));
                appleResponseDto.setValidationReportsList(
                        JSONUtils.convertListToType(response.getBody(), AppleValidationReports.class));
            } else {
                appleResponseDto.setAppleDto(JSONUtils.convertObjectToType(response.getBody(), AppleDto.class));
            }
            return appleResponseDto;
        } catch (Exception e) {
            logger.info("Exception while calling create businessAsset api:{}, ex:{}", uri, e.getStackTrace());
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
    }

    @Override
    public AppleResponseDto updateBusinessAsset(final String appleCompanyId, final String appleBusinessId,
            final String businessAssetId, AppleDto appleDto) {
        AppleResponseDto baseDto = this.getBusinessAsset(appleCompanyId, appleBusinessId, businessAssetId);
        if (baseDto == null || baseDto.getAppleDto() == null
                || baseDto.getAppleDto().getBusinessAssetDetails() == null) {
            logger.error("Business Asset:{}, {} not found for update..!, response:{}", appleCompanyId, appleBusinessId,
                    baseDto);
            return null;
        }
        appleDto.setId(baseDto.getAppleDto().getId());
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl
                .concat(String.format(UPDATE_BUSINESS_ASSET_API, appleCompanyId, appleBusinessId, businessAssetId));
        try {
            ResponseEntity<Object> response = this.socialRestTemplate.exchange(uri, HttpMethod.PUT,
                    getHttpEntity(appleDto, new HashMap() {
                        {
                            put(HttpHeaders.IF_MATCH, baseDto.getAppleDto().getEtag());
                        }
                    }), Object.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                logger.error(
                        "Error calling Apple update Business Asset for appleCompanyId:{}, appleBusinessId:{}, response:{}",
                        appleCompanyId, appleBusinessId, JSONUtils.toJSON(response));
                baseDto.setValidationReportsList(
                        JSONUtils.convertListToType(response.getBody(), AppleValidationReports.class));
            } else {
                baseDto.setAppleDto(JSONUtils.convertObjectToType(response.getBody(), AppleDto.class));
            }
            return baseDto;
        } catch (Exception e) {
            logger.info("Exception while calling update businessAsset api:{}, ex:{}", uri, e.getStackTrace());
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
    }

    @Override
    public AppleResponseDto getImageMetaData(final String appleCompanyId, final String appleImageId) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat(String.format(GET_IMAGE_METADATA, appleCompanyId, appleImageId));
        AppleResponseDto appleResponseDto = new AppleResponseDto();
        try {
            ResponseEntity<Object> response = this.socialRestTemplate.exchange(uri, HttpMethod.GET,
                    getHttpEntity(null, null), Object.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                logger.error("Error calling Apple metadata api for appleCompanyId:{}, appleImageId:{}, response:{}",
                        appleCompanyId, appleImageId, JSONUtils.toJSON(response));
                appleResponseDto.setValidationReportsList(
                        JSONUtils.convertListToType(response.getBody(), AppleValidationReports.class));
            } else {
                appleResponseDto.setAppleDto(JSONUtils.convertObjectToType(response.getBody(), AppleDto.class));
            }
            return appleResponseDto;
        } catch (Exception e) {
            logger.info("Exception while calling get image metadata api:{}, ex:{}", uri, e.getStackTrace());
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
    }

    @Override
    public void deleteBusinessAsset(final String appleCompanyId, final String appleBusinessId,
            final String businessAssetId) {
        AppleResponseDto dto = this.getBusinessAsset(appleCompanyId, appleBusinessId, businessAssetId);
        if (dto == null || dto.getAppleDto() == null || StringUtils.isBlank(dto.getAppleDto().getId())) {
            logger.error(
                    "[AppleConnectServiceImpl] Failed to find business asset to delete: appleCompanyId:{}, appleBusinessId:{}",
                    appleCompanyId, appleBusinessId);
            throw new BirdeyeSocialException("Failed to delete business asset from Apple");
        }
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl
                .concat(String.format(DELETE_BUSINESS_ASSET_API, appleCompanyId, appleBusinessId, businessAssetId));
        try {
            ResponseEntity<Object> response = this.socialRestTemplate.exchange(uri, HttpMethod.DELETE,
                    getHttpEntity(null, new HashMap() {
                        {
                            put(HttpHeaders.IF_MATCH, dto.getAppleDto().getEtag());
                        }
                    }), Object.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                logger.error(
                        "Error calling deleting business asset for appleCompanyId:{}, appleBusinessId:{}, response::{}",
                        appleCompanyId, appleBusinessId, JSONUtils.toJSON(response));
                throw new BirdeyeSocialException("Failed to delete business asset from Apple");
            }
            logger.info("Successfully deleted business asset for appleCompanyId:{}, appleBusinessId:{}", appleCompanyId,
                    appleBusinessId);
        } catch (Exception e) {
            logger.info("Exception while calling delete businessAsset Api api:{}, ex:{}", uri, e.getStackTrace());
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
    }

    @Override
    public AppleResponseDto getBusiness(String appleCompanyId, String appleBusinessId) {
        String baseUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2();
        String uri = baseUrl.concat(String.format(GET_BUSINESS_API, appleCompanyId, appleBusinessId));
        AppleResponseDto appleResponseDto = new AppleResponseDto();
        try {
            ResponseEntity<Object> response = this.socialRestTemplate.exchange(uri, HttpMethod.GET,
                    getHttpEntity(null, null), Object.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                logger.error("Error calling Apple get Business for appleCompanyId:{}, appleBusinessId:{}, response:{}",
                        appleCompanyId, appleBusinessId, JSONUtils.toJSON(response));
                appleResponseDto.setValidationReportsList(
                        JSONUtils.convertListToType(response.getBody(), AppleValidationReports.class));
            } else {
                appleResponseDto.setAppleDto(JSONUtils.convertObjectToType(response.getBody(), AppleDto.class));
            }
            return appleResponseDto;
        } catch (Exception e) {
            logger.info("Exception while calling get business api:{}, ex:{}", uri, e.getStackTrace());
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        }
    }

    @Override
    public AppleErrorResponse[] getAppleInvalidLocationStatus(AppleDeleteLocationRequest appleDeleteLocationRequest) throws IOException {
        logger.info("getAppleInvalidLocationStatus called with request : {}", appleDeleteLocationRequest);
        StringBuilder uri = new StringBuilder(
                CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAppleBaseUrlV2());
        uri.append(String.format(GET_INVALID_LOCATION_API, appleDeleteLocationRequest.getCompanyId(), appleDeleteLocationRequest.getLocationId()));
        try {
            ResponseEntity<Map>  response =
                    this.socialRestTemplate.exchange(uri.toString(), HttpMethod.GET,getHttpEntity(null, null), Map.class);
            if (response.getStatusCode().is2xxSuccessful()) {
               String state = (String) response.getBody().get("state");
               if(state.equals(Constants.APPLE_LOCATION_REJECTED)) {
                   AppleErrorResponse errorResponse = new AppleErrorResponse();
                   errorResponse.setCode(Constants.APPLE_LOCATION_REJECTED);

                   return new AppleErrorResponse[]{errorResponse};
               }
            }
        } catch (HttpStatusCodeException e) {
            if (e.getRawStatusCode()==404) {
                ObjectMapper mapper = new ObjectMapper();
                AppleErrorResponse[] errorResponses = mapper.readValue(e.getResponseBodyAsString(), AppleErrorResponse[].class);
                return errorResponses;
            }
            logger.info("Exception while calling get location api:{}, ex:{}", uri, e.getStackTrace());
            throw new BirdeyeSocialException(e.getLocalizedMessage());
        } catch (Exception e) {
        logger.error("Exception : {} occurred while calling getAppleInvalidLocationStatus for url: {} :", e, uri);
        throw new BirdeyeSocialException(e.getMessage());
    }

        return null;
    }
}
