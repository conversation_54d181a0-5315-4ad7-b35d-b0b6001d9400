package com.birdeye.social.service;

import com.birdeye.social.constant.*;
import com.birdeye.social.model.AutoSuggesterPagesResponse;
import com.birdeye.social.model.PageConnectionStatus;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.applePost.AppleLogoURLRequest;
import com.birdeye.social.sro.applePost.AppleUpdateCTARequest;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface AppleSetupService {

    void fetchExistingAccounts(AppleExistingLocationRequest input);
    void syncExistingLocations(AppleExistingLocationRequest input);
    void saveNewLocation(AppleLocationRequest input);
    void updateLocation(AppleLocationRequest input);

    Map<String,String> getBusinessNameList(Long enterpriseId);

    DelegatedAppleAccount getDelegatedBusinessList(Integer pageNumber);

    DelegatedAppleAccount getDelegatedBusiness(Long enterpriseId,String appleCompanyId,String appleBusinessId);

    AppleBrandMappingList getBrandMappingPages(String brandName, LocationMappingRequest request);

    Boolean getAppleEnabledStatus(Long enterpriseId);

    List<String> getAppleLocationsFromBusiness (List<Integer> businessIds);
    
	Integer getBusinessIdByAppleLocationId(String locationId);

    List<String> getAppleLocationCTA(String appleLocationId);

    void fetchAppleLocationCTA(Integer id);

    void fetchAppleLocationCTAInit();

    void updateAppleLocationCTA(AppleUpdateCTARequest appleUpdateCTARequest);

    void updateAppleLocationLogoURL(AppleLogoURLRequest appleLogoURLRequest);

    AppleMapUnmapResponse mapUnmapPageForSmb(MappingOperationType mappingOperationType, String pageId, Long enterpriseId, Integer businessId);
    PaginatedConnectedPages getPages(Long resellerId, PageConnectionStatus pageConnectionStatus,
                                     Integer page, Integer size, String search, ResellerSearchType searchType,
                                     PageSortDirection sortDirection, ResellerSortType sortParam, List<Integer> locationIds,
                                     MappingStatus mappingStatus, List<Integer> userIds, Boolean locationFilterSelected, String type);

    void markAppleLocationInvalid(AppleDeleteLocationRequest appleDeleteLocationRequest) throws IOException;

    void updateAppleLocationState(AppleUpdateLocationStateRequest appleUpdateLocationStateRequest);

    AutoSuggesterPagesResponse fetchApplePages(Long enterpriseId);
}
