package com.birdeye.social.service;

import com.birdeye.social.constant.PageSortDirection;
import com.birdeye.social.constant.ResellerSortType;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.BusinessLocationLiteEntity;
import com.birdeye.social.entities.*;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.exception.TooManyRequestException;
import com.birdeye.social.external.request.google.AsyncRetryRequestWrapper;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequest;
import com.birdeye.social.insights.PostData;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.model.*;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.WebsiteDomain;
import com.birdeye.social.sro.ChannelPageDetails;
import com.birdeye.social.sro.ChannelPageInfo;
import com.birdeye.social.sro.LastReconnectDetails;
import com.birdeye.social.sro.socialenterprise.SocialEnterpriseBulkStatusDTO;

import java.io.File;
import java.util.List;
import java.util.Map;

public interface CommonService {
	public ConsumerTokenAndSecret getAppKeyAndToken(Business business, String type) throws Exception;

	public void handleExpiredTokenForTwitter(BusinessTwitterAccounts twitterAccounts);

	public  String getPropertyValue(String propertyKey, String defaultValue);

	public File getFile(Long businessId, String videoURL);

	public File getImageFile(String imageUrl);

	ConsumerTokenAndSecret getDefaultAppKeyAndToken(String type);

	ConsumerTokenAndSecret getDefaultAppKeyAndTokenDomain(String type, WebsiteDomain domain);

	void cleanRedisLock(String channel, String businessId);

	LastReconnectDetails getIntegrationSummary(Long businessId, Boolean reconnectFlag, String channel);

	String getFacebookIntegrationStatus(BusinessFBPage rawPage);

	Boolean isWebChatEnabled(Integer locationId);

	public WebsiteDomain getDomainForDirectBusiness();


	void sendGmbSetupAuditEvent(String action, List<BusinessGoogleMyBusinessLocation> pages, String user, Integer businessId, Long enterpriseId);

	void sendFbSetupAuditEvent(String action, List<BusinessFBPage> pages, String user, Integer businessId, Long enterpriseId);

	void sendTwitterSetupAuditEvent(String action, List<BusinessTwitterAccounts> pages, String user, Integer businessId, Long enterpriseId);

	void sendTiktokSetupAuditEvent(String action, List<BusinessTiktokAccounts> pages, String user, Integer businessId, Long enterpriseId);

	void sendWhatsappSetupAuditEvent(String action, List<BusinessWhatsappAccounts> pages, String user, Integer businessId, Long enterpriseId);

	void sendInstagramSetupAuditEvent(String action, List<BusinessInstagramAccount> pages, String user, Integer businessId, Long enterpriseId);

	void sendLinkedinSetupAuditEvent(String action, List<BusinessLinkedinPage> pages, String user, Integer businessId, Long enterpriseId);

	void sendYoutubeSetupAuditEvent(String action, List<BusinessYoutubeChannel> pages, String user, Integer businessId, Long enterpriseId);

	void sendAppleSetupAuditEvent(String action, List<BusinessAppleLocation> pages, String user, Integer businessId);

	void uploadImageToCDN(PostData postData);

	void uploadPageImageToCDN(Object pageData);

	void migrateImageToCDN(Integer id, List<String> images);

	Boolean retryPostIfEligible(BirdeyeSocialException exception, SocialPostPublishInfo publishInfo);

	File getPublicFile(String mediaUrls);

	boolean checkPermission(String permissions, Integer sourceId, String module);

	List<String> getFilteredScopeForPage(DebugTokenResponse tokenResponse, List<String> pageId);

	String getCdnImageForPageProfileUrl(String externalPageId, Integer sourceId);

	Map<String, String> getCdnImageListForPageProfileUrl(List<String> externalePageIds, Integer sourceId);

	boolean retryQuotaCheck(Integer postId);
	boolean retryQuotaCheck(String feedId);
	void sendPageDisconnectAuditEvent(Object account, String userId);

	void checkBusinessValidation(BusinessLiteDTO business);

	 ConsumerTokenAndSecret getAppKeyAndTokenV2(String type);

	List<String> getGoogleRedirectUrlInfoConfig();

    void removeMapping(Integer businessId, String s, String name, String pageType);

    void deletePage(String channel, String pageId);
    void sendPostInsights(PostData postData);

	PostData prepareDeltaInsightsForPost(PostData postData, BusinessPosts businessPosts);

	List<MediaUrls> convertMediaWithPictures(List<MediaListInfo> mediaListInfos, Long enterpriseId) throws Exception;

	Boolean isValidUrl(String imageUrl);

	String getInitials(String authorName);
	String extractGMBPermission(String scope);
	boolean checkBusinessSMB(BusinessLiteDTO business);

	void sortImageUrlFromSequenceId(List<String> imageUrls, String postMetaData);

	void sortImageUrlFromSequenceId(List<String> imageUrls, SocialPostSchedulerMetadata postMetaData);

	BusinessLocationLiteEntity getMappedLocationInfo(Map<String, Object> locationDetails, Integer businessId,
			String pageName);

	String prepareBusinessAddress(BusinessLocationLiteEntity location);

	ChannelPageDetails sortInvalidAndValidPage(List<ChannelPages> pageInfo);

	void setDisabledAsNullForAllChannel(ChannelPageInfo accountInfo);

	String getPageTokenGranularPermissions(String pageAccessToken, List<String> pageId);

	boolean checkGranular(Long enterpriseId);
	boolean checkScheduleEditLock(Long enterpriseId);

	void uploadMediaToPicturesque(PostData postData, BusinessPosts businessPosts);

	boolean checkSamayEventFlag(Integer enterpriseId);

	boolean checkRequestFromAuthorizedSourceUsingBusinessNumber(Long businessNumberStored, Long businessNumberExternal);

	boolean checkRequestFromAuthorizedSourceUsingResellerIdsList(List<Long> resellerIdsStored, Long resellerIdExternal);

	boolean checkRequestFromAuthorizedSourceUsingBusinessId(Integer businessIdStored, Integer businessIdExternal);

	boolean checkRequestFromAuthorizedSourceForReseller(Integer resellerId, Integer enterpriseIdStored);

	Integer getResellerIdFromEnterpriseId(Integer enterpriseId);

	boolean checkRequestFromAuthorizedSourceUsingLongResellerID(Long resellerIdStored, Long resellerIdExternal);

	boolean isBusinessAllowedToSyncBusinessPosts(Integer businessId);

	void retryForAsyncRequest(AsyncRetryRequestWrapper<?> request, String kafkaTopic, TooManyRequestException e);

	Boolean getRateLimitingCheckEnabled();

	boolean checkSocialPostScheduleDateIsSame(Integer masterPostId);

	boolean isEligibleForChunkMediaUpload(SocialPostsAssets asset, MediaUploadRequest request);

	boolean isTwitterVideoEligibleForChunkMediaUpload(SocialPostsAssets asset, MediaUploadRequest request);

	Boolean checkEligibilityForBulkApprovalEvents(Integer businessId);

	SocialEnterpriseBulkStatusDTO createEnterpriseDoupErrorResponse(SocialEnterpriseBulkStatusDTO socialEnterpriseBulkStatusDTO, long eventId,
																	String errMessage, String status);

	ChannelPageDetails  additionalSorting(ChannelPageDetails accountInfo, ResellerSortType sortType , PageSortDirection sortDirection) ;

	Map<Integer, BusinessCoreUser> fetchUserDetails(List<Integer> userIds, long enterpriseId, String channel) throws Exception;

	Map<String, Object> fetchBusinessLocations(List<Integer> businessIds, long enterpriseId, String channel) throws Exception;

    void setCommonChannelAccountInfo(ChannelAccountInfo accountInfo, Integer businessId, Integer isSelected, Long enterpriseId, Long rawTableEnterpriseId);

}
