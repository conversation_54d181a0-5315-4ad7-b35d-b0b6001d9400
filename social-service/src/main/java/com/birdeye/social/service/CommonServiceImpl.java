package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dao.reports.BusinessPostsRepository;
import com.birdeye.social.dto.*;
import com.birdeye.social.entities.*;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.exception.TooManyRequestException;
import com.birdeye.social.external.request.business.BusinessLiteRequest;
import com.birdeye.social.external.request.google.AsyncRetryRequestWrapper;
import com.birdeye.social.external.request.media.PicturesqueMediaUploadRequest;
import com.birdeye.social.external.request.media.PostAssetMetaData;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequest;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.external.service.PicturesqueGen;
import com.birdeye.social.facebook.FacebookService;
import com.birdeye.social.insights.PostData;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.post_lib.PostInsightsData;
import com.birdeye.social.platform.dao.*;
import com.birdeye.social.platform.entities.*;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.ControllerUtils;
import com.birdeye.social.utils.ConversionUtils;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SamayUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ForkJoinTask;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CommonServiceImpl implements CommonService {
	@Autowired
	private DomainSocialAppCredRepository domainSocialAppCred;
	@Autowired
	private ResellerDomainRepository resellerDomainRepository;

	@Autowired
	GoogleClientCredRepo googleClientCredRepo;
	@Autowired
	private BusinessTwitterPageRepository twitterPageRepository;
	@Autowired
	private SocialTwitterAccountRepository socialTwitterRepo;
	@Autowired
	private MicroServicesParametersRepository microServicesParametersRepository;
	@Autowired
	private BusinessPostsAssetsRepo businessPostsAssetsRepo;
	@Autowired
	private IRedisExternalService redisExternalService;

	@Autowired
	private IRedisLockService redisService;

	@Autowired
	private BusinessGetPageReqRepo businessGetPageReqRepo;

	@Autowired
	private BusinessRepository businessRepo;

	@Autowired
	private BusinessPagePictureAssetsRepo businessPagePictureAssetsRepo;

	@Autowired
	private IBusinessCoreService businessCoreService;

	@Autowired
	private IBrokenIntegrationService brokenIntegrationService;

	@Autowired
	private KafkaProducerService kafkaProducer;

	@Autowired
	private BusinessService businessService;

	@Autowired
	private PicturesqueGen picturesqueGen;
	@Autowired
	private ISocialAppService socialAppService;

	@Autowired
	private BusinessPostsRepository businessPostsRepository;

	@Autowired
	private SocialPostScheduleInfoRepo socialPostScheduleInfoRepository;

	@Autowired
	private SocialPostRepository socialPostRepository;

	public static final int MAX_RESELLER_HIERARCHY_DEPTH = 10;

	private static final String DEFAULT_DOMAIN = "birdeye.com";

	private static final Logger LOGGER = LoggerFactory.getLogger(CommonServiceImpl.class);
	private static final String RECONNECT = "reconnect";
	private static final String SOCIAL_POST_RETRY_TOPIC = "social-post-retry-topic";

	@Autowired
	private FacebookService fbService;

	@Autowired
	private ExceptionRetryService exceptionRetryService;

	@Autowired
	private SamayService samayService;

	@Autowired
	private SocialRetryPostRepo socialRetryPostRepo;

	@Autowired
	private ISocialModulePermissionService socialModulePermissionService;

	@Autowired
	private SocialPostRepository postMetaRepo;

	@Autowired
	private SocialFBPageRepository socialFBPageRepo;

	@Autowired
	private IPermissionMappingService permissionMappingService;
	@Autowired
	private GoogleRedirectUrlRepo googleRedirectUrlRepo;

	public static final String UNAUTHORIZED_ACCESS_MSG = "You are not Authorized to perform this action.";
	@Override
	public ConsumerTokenAndSecret getAppKeyAndToken(Business business, String type) throws Exception {
		WebsiteDomain domain = null;
		if(SocialChannel.YOUTUBE.getName().equalsIgnoreCase(type)){
			ConsumerTokenAndSecret consumerToken = new ConsumerTokenAndSecret();
			List<GoogleClientCred> domainCreds = googleClientCredRepo.findYoutubeCredDefault();
			if (CollectionUtils.isNotEmpty(domainCreds)) {
				consumerToken.setToken(domainCreds.get(0).getClientId());
				consumerToken.setSecret(domainCreds.get(0).getClientSecret());
				consumerToken.setCredentialId(domainCreds.get(0).getId());
			}
			return consumerToken;
		}
		if("linkedin".equalsIgnoreCase(type)){
			BusinessDomainDTO defaultDomain = businessService.getBusinessDomain(1);
			if (Objects.nonNull(defaultDomain)) {
				domain = new WebsiteDomain();
				domain.setDomainName(defaultDomain.getDomain());
				domain.setSecureEnabled(defaultDomain.getSecureEnabled());
			}
		}else {
			domain = getDomainForBusiness(business);
		}
		LOGGER.info("WebsiteDomain info for businessId: {}: {}",business.getId(),domain);
		LOGGER.info("Domain for business {} is {}",business.getName(), domain != null ? domain.getDomainName() : null);
		if ("googleplus".equalsIgnoreCase(type)) {
			ConsumerTokenAndSecret consumerToken = new ConsumerTokenAndSecret();
			LOGGER.info("Using default domain creds");
			GoogleClientCred defaultCreds = googleClientCredRepo.findGoogleCredDefault();
			consumerToken.setToken(defaultCreds.getClientId());
			consumerToken.setSecret(defaultCreds.getClientSecret());
			consumerToken.setCredentialId(defaultCreds.getId());

//			List<GoogleClientCredSocial> domainCreds = googleClientCredRepository.findGoogleCredByDomain(domain.getId());
//			if (CollectionUtils.isNotEmpty(domainCreds)) {
//				LOGGER.info("Using domain creds for domain#id {} ",domain.getId());
//				consumerToken.setToken(domainCreds.get(0).getClientId());
//				consumerToken.setSecret(domainCreds.get(0).getClientSecret());
//				consumerToken.setCredentialId(domainCreds.get(0).getId());
//			} else {
//				LOGGER.info("Using default domain creds");
//				GoogleClientCredSocial defaultCreds = googleClientCredRepository.findGoogleCredDefault();
//				consumerToken.setToken(defaultCreds.getClientId());
//				consumerToken.setSecret(defaultCreds.getClientSecret());
//				consumerToken.setCredentialId(defaultCreds.getId());
//			}
			consumerToken.setDomainName(domain.getDomainName());
			return consumerToken;
		}
        DomainSocialAppCreds creds = null;
		List<DomainSocialAppCreds> credList =  domainSocialAppCred.findDefaultConfig(type);
		if(CollectionUtils.isNotEmpty(credList)){
			creds = credList.get(0);
		}

		LOGGER.info("creds: {}",creds);
//        if (domain != null){
//            List<DomainSocialAppCreds> credList = domainSocialAppCred.findByDomainAndAppType(domain, type);
//            if(credList != null && ! credList.isEmpty()){
//                creds = credList.get(0);
//            }
//        }
//
//        if(creds == null ||
//                StringUtils.isBlank(creds.getAppKey()) ||
//                StringUtils.isBlank(creds.getAppSecret())){
//            List<DomainSocialAppCreds> credList =  domainSocialAppCred.findDefaultConfig(type);
//            if(credList != null && ! credList.isEmpty()){
//                creds = credList.get(0);
//            }
//        }
        
        if(creds == null){
        		switch(type) {
        		case "linkedin":
        			throw new Exception("NO_DEFAULT_LINKEDIN_CONSUMER_KEY_AND_SECRET");
        		case "facebook":
        			throw new Exception("NO_DEFAULT_FACEBOOK_CONSUMER_KEY_AND_SECRET");
        		case "twitter":
        			throw new Exception("NO_DEFAULT_TWITTER_CONSUMER_KEY_AND_SECRET");
           	case "instagram":
        			throw new Exception("NO_DEFAULT_INSTAGRAM_CONSUMER_KEY_AND_SECRET");
        		}
            
        }
        
        if(StringUtils.isBlank(creds.getAppKey())){
	        	switch(type) {
	    		case "linkedin":
	    			throw new Exception("LINKEDIN_CONSUMER_KEY_IS_NULL");
	    		case "facebook":
	    			throw new Exception("FACEBOOK_CONSUMER_KEY_IS_NULL");
	    		case "twitter":
	    			throw new Exception("TWITTER_CONSUMER_KEY_IS_NULL");
	    		case "instagram":
	    			throw new Exception("INSTAGRAM_CONSUMER_KEY_IS_NULL");
	        	}
        }
        
        if(StringUtils.isBlank(creds.getAppSecret())){
	        	switch(type) {
	    		case "linkedin":
	    			throw new Exception("LINKEDIN_CONSUMER_SECRET_IS_NULL");
	    		case "facebook":
	    			throw new Exception("FACEBOOK_CONSUMER_SECRET_IS_NULL");
	    		case "twitter":
	    			throw new Exception("TWITTER_CONSUMER_SECRET_IS_NULL");
	    		case "instagram":
	    			throw new Exception("INSTAGRAM_CONSUMER_SECRET_IS_NULL");
	        	}
        }
        ConsumerTokenAndSecret consumerToken = new ConsumerTokenAndSecret();
        consumerToken.setToken(creds.getAppKey());
        consumerToken.setSecret(creds.getAppSecret());
        consumerToken.setAppAccessToken(creds.getAppAccessToken());
        consumerToken.setDomainName(domain.getDomainName());
        return consumerToken;
	}


	private WebsiteDomain getDomainForBusiness(Business business) {
		WebsiteDomain domain = null;
		BusinessDomainDTO businessDomainDTO;
		LOGGER.info("WebsiteDomain info for businessId: {}",business.getId());
		int i = 0;
		while (domain == null && business != null && i < MAX_RESELLER_HIERARCHY_DEPTH) {
			businessDomainDTO = businessService.getBusinessDomain(business.getId());
			if (Objects.nonNull(businessDomainDTO) && !(businessDomainDTO.getDomain().equalsIgnoreCase(DEFAULT_DOMAIN)&&businessDomainDTO.getSecureEnabled()==0)) {
				domain = new WebsiteDomain();
				domain.setDomainName(businessDomainDTO.getDomain());
				domain.setSecureEnabled(businessDomainDTO.getSecureEnabled());
				break;
			}
			business = business.getReseller();
			i++;
		}
		if (domain == null) {
			businessDomainDTO = businessService.getBusinessDomain(1);
			if (Objects.nonNull(businessDomainDTO)) {
				domain = new WebsiteDomain();
				domain.setDomainName(businessDomainDTO.getDomain());
				domain.setSecureEnabled(businessDomainDTO.getSecureEnabled());
			}
		}
		LOGGER.info("WebsiteDomain info for businessId: {}: {}",business.getId(),domain);
		return domain;
	}

	public WebsiteDomain getDomainForDirectBusiness(){
		WebsiteDomain domain = null;
		BusinessDomainDTO businessDomainDTO;
		businessDomainDTO = businessService.getBusinessDomain(1);
		if (Objects.nonNull(businessDomainDTO)) {
			domain = new WebsiteDomain();
			domain.setDomainName(businessDomainDTO.getDomain());
			domain.setSecureEnabled(businessDomainDTO.getSecureEnabled());
		}
		LOGGER.info("getDomainForDirectBusiness: {}",domain);
//		List<WebsiteDomain> defaultDomains = websiteDomainRepository.findByIsDefault(1);
//		if (defaultDomains != null && !defaultDomains.isEmpty()) {
//			domain = defaultDomains.get(0);
//		}
		return domain;
	}

	@Override
	@Transactional
	public void handleExpiredTokenForTwitter(BusinessTwitterAccounts twitterAccounts) {
		twitterAccounts.setIsValid(0);
		twitterAccounts.setInvalidType(null);
		socialTwitterRepo.saveAndFlush(twitterAccounts);
		sendTwitterSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(twitterAccounts), null, twitterAccounts.getBusinessId(), twitterAccounts.getEnterpriseId());

		brokenIntegrationService.pushValidIntegrationStatus(twitterAccounts.getEnterpriseId(), SocialChannel.TWITTER.getName(),
				twitterAccounts.getId(),0,twitterAccounts.getProfileId().toString());
	}

	@Override
	public String getPropertyValue(String propertyKey, String defaultValue) {
		if (StringUtils.isEmpty(propertyKey)) {
			return null;
		}
		MicroServicesParameters entry = microServicesParametersRepository.findByName(propertyKey);
		if (entry == null || entry.getValue() == null) {
			return defaultValue;
		}
		return entry.getValue();
	}

	@Override
	public File getFile(Long businessId, String url) {
		File tempFile = null;
		FileOutputStream out = null;
		try {
			URL website = new URL(url.trim());
			HttpURLConnection conn = (HttpURLConnection) website.openConnection();
			tempFile = File.createTempFile(System.currentTimeMillis() + "", ".tmp");
			tempFile.deleteOnExit();
			out = new FileOutputStream(tempFile);
			IOUtils.copy(conn.getInputStream(), out);
		} catch (Exception e) {
			throw new BirdeyeSocialException("Could not download file", e);
		} finally {
			if(out != null) {
				try {
					out.close();
				} catch (IOException e) {
					LOGGER.info("inputStream close IOException: {}",e.getMessage());
				}
			}
		}
		return tempFile;
	}

	private String getSuffix(String url) {
		String[] split = url.split("\\.");
		int len = split.length;
		if(len > 1) {
			return "."+split[len-1];
		}
		return ".tmp";
	}

	@Override
	public File getImageFile(String imageUrl){
		File tempFile = null;
		FileOutputStream out = null;
		try {
			URL website = new URL(imageUrl.trim());
			HttpURLConnection conn = (HttpURLConnection) website.openConnection();
			tempFile = File.createTempFile(System.currentTimeMillis() + "", getSuffix(imageUrl));
			tempFile.deleteOnExit();
			out = new FileOutputStream(tempFile);
			IOUtils.copy(conn.getInputStream(), out);
		} catch (Exception e) {
			throw new BirdeyeSocialException("Could not download image file", e);
		} finally {
			if(out != null) {
				try {
					out.close();
				} catch (IOException e) {
					LOGGER.info("inputStream close IOException: {}",e.getMessage());
				}
			}
		}
		return tempFile;
	}
	@Override
	public ConsumerTokenAndSecret getDefaultAppKeyAndToken(String type) {
		ConsumerTokenAndSecret consumerToken = new ConsumerTokenAndSecret();
		switch (type) {
		case "google":
			BusinessDomainDTO businessDomainDTO;
			businessDomainDTO = businessService.getBusinessDomain(1);
			if (Objects.nonNull(businessDomainDTO)) {
				consumerToken.setDomainName(businessDomainDTO.getDomain());
			}
			LOGGER.error("[getDefaultAppKeyAndToken] consumer token domain name: {}", consumerToken.getDomainName());
//			List<WebsiteDomain> defaultDomains = websiteDomainRepository.findByIsDefault(1);
//			if (defaultDomains != null && !defaultDomains.isEmpty()) {
//				WebsiteDomain domain = defaultDomains.get(0);
//				consumerToken.setDomainName(domain.getDomainName());
//			}
			GoogleClientCred defaultCreds = googleClientCredRepo.findGoogleCredDefault();
			consumerToken.setToken(defaultCreds.getClientId());
			consumerToken.setSecret(defaultCreds.getClientSecret());
			consumerToken.setCredentialId(defaultCreds.getId());
			break;
		default:
			LOGGER.error("[Default configs] Not yet supported for channel: {}", type);
			break;
		}
		return consumerToken;
	}
	
	@Override
	public ConsumerTokenAndSecret getDefaultAppKeyAndTokenDomain(String type, WebsiteDomain domain) {
		ConsumerTokenAndSecret consumerToken = new ConsumerTokenAndSecret();
		switch (type) {
			case "google":
				if (domain != null) {
					consumerToken.setDomainName(domain.getDomainName());
				} else {
					BusinessDomainDTO businessDomainDTO;
					businessDomainDTO = businessService.getBusinessDomain(1);
					if (Objects.nonNull(businessDomainDTO)) {
						consumerToken.setDomainName(businessDomainDTO.getDomain());
					}
					LOGGER.error("[getDefaultAppKeyAndTokenDomain] consumer token domain name: {}", consumerToken.getDomainName());
//					List<WebsiteDomain> defaultDomains = websiteDomainRepository.findByIsDefault(1);
//					if (defaultDomains != null && !defaultDomains.isEmpty()) {
//						domain = defaultDomains.get(0);
//						consumerToken.setDomainName(domain.getDomainName());
//					}
				}
				GoogleClientCred defaultCreds = googleClientCredRepo.findGoogleCredDefault();
				consumerToken.setToken(defaultCreds.getClientId());
				consumerToken.setSecret(defaultCreds.getClientSecret());
				consumerToken.setCredentialId(defaultCreds.getId());
				break;
			default:
				LOGGER.error("[Default configs] Not yet supported for channel: {}", type);
				break;
		}
		return consumerToken;
	}
	
	/* (non-Javadoc)
	 * @see com.birdeye.social.service.CommonService#cleanRedisLock(java.lang.String, java.lang.String)
	 * Clean Redis Lock in error cases
	 */
	@Override
	public void cleanRedisLock(String channel, String businessId) {
		redisService.release(channel.concat(businessId));
		LOGGER.info("[Redis Lock] Lock released for business {}", businessId);
	}
	
	@Override
	public LastReconnectDetails getIntegrationSummary(Long businessId, Boolean reconnectFlag, String channel) {
		LastReconnectDetails response = null;
		if (reconnectFlag) {
			List<BusinessGetPageRequest> requestList = getMaxRequestForBusinessAndUser(businessId, channel, RECONNECT,Status.COMPLETE.getName());
			if (CollectionUtils.isNotEmpty(requestList)) {
				BusinessGetPageRequest req = requestList.get(0);
				
				String userName = getBirdeyeUserInfo(req.getBirdeyeUserId());

				String lastRequestTime = convertUTCToPST(req.getCreated());
				
				boolean viewSummary = isViewSummaryApplicable(req.getCreated());
				
				response = new LastReconnectDetails(req.getEmail(), userName, lastRequestTime);
				response.setViewSummary(viewSummary);
				return response;
			} else {
				LOGGER.info("getIntegrationSummary No reconnect Request found for channel {} business {}", channel, businessId);
			}
		}
		return null;
	}
	
	private List<BusinessGetPageRequest> getMaxRequestForBusinessAndUser(Long businessId, String channel, String requestType, String status) {
		return businessGetPageReqRepo.findLastRequestByEnterpriseIdAndChannelAndUserAndRequestType(businessId, channel, requestType, status);
	}
	
	private String getBirdeyeUserInfo(Integer userId) {
		LOGGER.info("getBirdeyeUserInfo for userId {} ", userId);
		String userName = null;
		StringBuilder requestBy;
		BusinessCoreUser user = businessCoreService.getUserInfo(userId);
		if (user != null) {
			if (StringUtils.isEmpty(user.getFirstName()) && StringUtils.isEmpty(user.getLastName())) {
				requestBy = new StringBuilder(user.getEmailId());
			} else if (StringUtils.isEmpty(user.getLastName())) {
				requestBy = new StringBuilder(user.getFirstName());
			} else if (StringUtils.isEmpty(user.getFirstName())) {
				requestBy = new StringBuilder(user.getLastName());
			} else {
				requestBy = new StringBuilder(user.getFirstName());
				requestBy.append(" ").append(user.getLastName());
			}
			userName = requestBy.toString();
		}
		return userName;
	}
	
	private String convertUTCToPST(Date date) {
		if (date != null) {
			LocalDateTime ldt = LocalDateTime.ofInstant(date.toInstant(), ZoneId.of("America/Los_Angeles"));
			return formatLocatDateTime(ldt);
		}
		return  null;
	}
	
	private String formatLocatDateTime(LocalDateTime ldt) {
		if (ldt != null) {
			return DateTimeFormatter.ofPattern("MM/dd/yyyy").format(ldt);
		}
		return  null;
	}
	
	private boolean isViewSummaryApplicable(Date createdDate) {
		Integer summaryHours = Integer.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("reconnect.summary.hours", "1"));
		
		LocalDateTime actualDateTime = LocalDateTime.ofInstant(createdDate.toInstant(), ZoneId.of("America/Los_Angeles"));
		LocalDateTime currentDateTime = LocalDateTime.now(ZoneId.of("America/Los_Angeles"));
		
		return (actualDateTime.plusHours(summaryHours).isAfter(currentDateTime));
	}
	
	@Override
	public String getFacebookIntegrationStatus(BusinessFBPage rawPage) {
		LOGGER.info("getFacebookIntegrationStatus for businessId {}", rawPage.getBusinessId());
		String facebookIntegrationStatus = "";
		facebookIntegrationStatus =  FacebookIntegrationStatus.getFbIntegrationStatus(rawPage.getIsValid()).getStatus();
		if(facebookIntegrationStatus.equalsIgnoreCase(FacebookIntegrationStatus.RECONNECT.getStatus())) {
			return facebookIntegrationStatus;
		}
		//get permission set from raw fb page
		String pagePermissons = rawPage != null ? rawPage.getPagePermissions() : "";
		if ((StringUtils.isNotBlank(pagePermissons) && !pagePermissons.contains("pages_messaging")) || pagePermissons == null){
			facebookIntegrationStatus = FacebookIntegrationStatus.ENABLE_MESSENGER.getStatus();
		} else if(StringUtils.isNotBlank(pagePermissons) && pagePermissons.contains("pages_messaging") && rawPage.getMessengerOpted() == 0) {
			facebookIntegrationStatus = FacebookIntegrationStatus.OPTED_OUT.getStatus();
		}
		return facebookIntegrationStatus;
	}

	public Boolean isWebChatEnabled(Integer locationId){
		if (locationId != null){
			Business business = businessRepo.getOne(locationId);
			if (business != null){
				Integer accountId = business.getEnterpriseId() != null?business.getEnterpriseId():locationId;

				try {
					Boolean isWebChatEnabled = businessCoreService.isWebChatEnabled(accountId);
					return isWebChatEnabled;
				}catch (Exception e) {
					LOGGER.error("Exception while getting web chat enabled field from business core service:{} exception:{}",locationId,e);
					return false;
				}
			}
		}
		return false;
	}

	@Override
	public void sendGmbSetupAuditEvent(String action, List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations, String user, Integer businessId, Long enterpriseId) {
		try {
			businessGoogleMyBusinessLocations.stream().forEach(page -> {
				String auditComment = "GMB location Id: " + page.getLocationId();
				sendSetupAuditEvent(action, page, page.getLocationId(), user, businessId, SocialChannel.GMB.getName(), page.getRequestId(), auditComment, enterpriseId);
			});
		} catch (Exception e) {
			LOGGER.info("Something went wrong while saving audit for gmb with action {} and payload {}", action, businessGoogleMyBusinessLocations);
		}
	}


	@Override
	public void sendFbSetupAuditEvent(String action, List<BusinessFBPage> fbPages, String user, Integer businessId, Long enterpriseId) {
		try {
			fbPages.stream().forEach(page -> {
				String auditComment = "Facebook page Id: " + page.getFacebookPageId();
				sendSetupAuditEvent(action, page, page.getFacebookPageId(), user, businessId,
						SocialChannel.FACEBOOK.getName(), page.getRequestId(), auditComment, enterpriseId);
			});
		} catch (Exception e) {
			LOGGER.info("Something went wrong while saving audit for facebook with action {} and payload {}", action, fbPages);
		}
	}




	@Async
	@Override
	public void sendPageDisconnectAuditEvent(Object account, String userId) {
		LOGGER.info("Page disconnect audit received for object {}", account);
		try {
			String action = SocialSetupAuditEnum.PAGE_DISCONNECTED.name();
			if(account instanceof BusinessTwitterAccounts) {
				BusinessTwitterAccounts businessTwitterAccount = (BusinessTwitterAccounts) account;
				String auditComment = "Twitter profile Id: " + businessTwitterAccount.getProfileId();
				sendSetupAuditEvent(action, businessTwitterAccount, businessTwitterAccount.getProfileId().toString(), userId,
						businessTwitterAccount.getBusinessId(),
						SocialChannel.TWITTER.getName(), businessTwitterAccount.getRequestId(), auditComment, businessTwitterAccount.getEnterpriseId());
			} else if (account instanceof BusinessFBPage) {
				BusinessFBPage fbPage = (BusinessFBPage) account;
				String auditComment = "Facebook page Id: " + fbPage.getFacebookPageId();
				sendSetupAuditEvent(action, fbPage, fbPage.getFacebookPageId(), userId, fbPage.getBusinessId(),
						SocialChannel.FACEBOOK.getName(), fbPage.getRequestId(), auditComment, fbPage.getEnterpriseId());
			} else if (account instanceof BusinessInstagramAccount) {
				BusinessInstagramAccount page = (BusinessInstagramAccount) account;
				String auditComment = "Instagram page Id: " + page.getInstagramAccountId();
				sendSetupAuditEvent(action, page, page.getInstagramAccountId(), userId, page.getBusinessId(),
						SocialChannel.INSTAGRAM.getName(), page.getBusinessGetPageId(), auditComment, page.getEnterpriseId());
			} else if (account instanceof  BusinessLinkedinPage) {
				BusinessLinkedinPage page = (BusinessLinkedinPage) account;
				String auditComment = "Linkedin page Id: " + page.getProfileId();
				sendSetupAuditEvent(action, page, page.getProfileId(), userId, page.getBusinessId(),
						SocialChannel.LINKEDIN.getName(), page.getBusinessGetPageId(), auditComment, page.getEnterpriseId());
			} else if (account instanceof BusinessYoutubeChannel) {
				BusinessYoutubeChannel channel = (BusinessYoutubeChannel) account;
				String auditComment = "Channel page Id: " + channel.getChannelId();
				sendSetupAuditEvent(action, channel, channel.getChannelId(), userId, channel.getBusinessId(),
						SocialChannel.YOUTUBE.getName(), channel.getRequestId(), auditComment, channel.getEnterpriseId());
			} else if (account instanceof BusinessGoogleMyBusinessLocation) {
				BusinessGoogleMyBusinessLocation page = (BusinessGoogleMyBusinessLocation) account;
				String auditComment = "GMB location Id: " + page.getLocationId();
				sendSetupAuditEvent(action, page, page.getLocationId(), userId, page.getBusinessId(),
						SocialChannel.GMB.getName(), page.getRequestId(), auditComment, page.getEnterpriseId());

			}
		} catch (Exception e) {
			LOGGER.info("Something went wrong while saving page update audit for channel with payload {}", account);
		}
	}


	@Override
	public void sendTwitterSetupAuditEvent(String action, List<BusinessTwitterAccounts> businessTwitterAccounts, String user, Integer businessId, Long enterpriseId) {
		try {
			businessTwitterAccounts.stream().forEach(page -> {
				String auditComment = "Twitter profile Id: " + page.getProfileId();
				sendSetupAuditEvent(action, page, page.getProfileId().toString(), user, businessId,
						SocialChannel.TWITTER.getName(), page.getRequestId(), auditComment, enterpriseId);
			});
		} catch (Exception e) {
			LOGGER.info("Something went wrong while saving audit for twitter with action {} and payload {}", action, businessTwitterAccounts);
		}
	}

	@Override
	public void sendTiktokSetupAuditEvent(String action, List<BusinessTiktokAccounts> pages, String user, Integer businessId, Long enterpriseId) {
		try {
			pages.stream().forEach(page -> {
				String auditComment = "Tiktok profile Id: " + page.getProfileId();
				sendSetupAuditEvent(action, page, page.getProfileId(), user, businessId,
						SocialChannel.TIKTOK.getName(), page.getRequestId(), auditComment, enterpriseId);
			});
		} catch (Exception e) {
			LOGGER.info("Something went wrong while saving audit for tiktok with action {} and payload {}", action, pages);
		}
	}


	@Override
	public void sendInstagramSetupAuditEvent(String action, List<BusinessInstagramAccount> pages, String user, Integer businessId, Long enterpriseId) {
		try {
			pages.stream().forEach(page -> {
				String auditComment = "Instagram page Id: " + page.getInstagramAccountId();
				sendSetupAuditEvent(action, page, page.getInstagramAccountId(), user, businessId,
						SocialChannel.INSTAGRAM.getName(), page.getBusinessGetPageId(), auditComment, enterpriseId);
			});
		} catch (Exception e) {
			LOGGER.info("Something went wrong while saving audit for instagram with action {} and payload {}", action, pages);
		}
	}

	@Override
	public void sendLinkedinSetupAuditEvent(String action, List<BusinessLinkedinPage> pages, String user, Integer businessId, Long enterpriseId) {
		try {
			pages.stream().forEach(page -> {
				String auditComment = "Linkedin page Id: " + page.getProfileId();
				sendSetupAuditEvent(action, page, page.getProfileId(), user, businessId,
						SocialChannel.LINKEDIN.getName(), page.getBusinessGetPageId(), auditComment, enterpriseId);
			});
		} catch (Exception e) {
			LOGGER.info("Something went wrong while saving audit for Linkedin with action {} and payload {}", action, pages);
		}
	}

	@Override
	public void sendYoutubeSetupAuditEvent(String action, List<BusinessYoutubeChannel> businessYoutubeChannelsList, String user, Integer businessId, Long enterpriseId) {
		try {
			businessYoutubeChannelsList.forEach(channel -> {
				String auditComment = "Channel page Id: " + channel.getChannelId();
				sendSetupAuditEvent(action, channel, channel.getChannelId(), user, businessId,
						SocialChannel.YOUTUBE.getName(), channel.getRequestId(), auditComment, enterpriseId);
			});
		} catch (Exception e) {
			LOGGER.info("Something went wrong while saving audit for Youtube with action {} and payload {}", action, businessYoutubeChannelsList);
		}
	}

	@Override
	public void sendAppleSetupAuditEvent(String action, List<BusinessAppleLocation> appleLocations, String user, Integer businessId) {
		try {
			appleLocations.forEach(location -> {
				String auditComment = "Channel location Id: " + location.getAppleLocationId();
				sendSetupAuditEvent(action, location, location.getAppleLocationId(), user, businessId,
						SocialChannel.APPLE_CONNECT.getName(), null, auditComment,location.getEnterpriseId());
			});
		} catch (Exception e) {
			LOGGER.info("Something went wrong while saving audit for Apple connect with action {} and payload {}", action, appleLocations);
		}
	}

	private void sendSetupAuditEvent(String action, Object payload, String pageId, String user, Integer businessId,
									 String channelName, String requestId, String auditComment, Long enterpriseId) {
		kafkaProducer.sendObject(Constants.SOCIAL_SETUP_AUDIT,new SocialSetupAudit(action, businessId, enterpriseId,
				pageId, user,
				JSONUtils.toJSON(payload), channelName, auditComment
				,requestId));
	}

	private CDNUploadDTO getCDNUploadDTO(String url,String id, String topic) {
		CDNUploadDTO cdnUploadDTO = new CDNUploadDTO();
		cdnUploadDTO.setImageUrl(url);
		cdnUploadDTO.setSourceKey(id);
		cdnUploadDTO.setKeyPrefix("cdn/");
		cdnUploadDTO.setSubBucket("social");
		cdnUploadDTO.setResponseTopicName(topic);
		return  cdnUploadDTO;
	}

	private CDNUploadDTO getCDNMigrateDTO(String url,Integer id) {
		CDNUploadDTO cdnUploadDTO = new CDNUploadDTO();
		cdnUploadDTO.setImageUrl(url);
		cdnUploadDTO.setSourceKey(String.valueOf(id));
		cdnUploadDTO.setKeyPrefix("cdn/");
		cdnUploadDTO.setSubBucket("social");
		cdnUploadDTO.setResponseTopicName(Constants.CDN_MIGRATE_TOPIC);
		return  cdnUploadDTO;
	}

	@Override
	public void uploadImageToCDN(PostData postData) {
		if(Objects.nonNull(postData) && CollectionUtils.isNotEmpty(postData.getImages())) {
			LOGGER.info("upload media to CDN initiated with request {}", postData);
			List<String> imageList = postData.getImages();
			List<BusinessPostsAssets> businessPostsAssetsList = new ArrayList<>();
			imageList.forEach(img->{
				BusinessPostsAssets businessPostsAssets = new BusinessPostsAssets();
				businessPostsAssets.setBusinessPostsId(postData.getId());
				businessPostsAssets.setIsImage(1);
				businessPostsAssets.setMediaUrl(img);
				businessPostsAssetsList.add(businessPostsAssetsRepo.saveAndFlush(businessPostsAssets));
				CDNUploadDTO uploadDTO = getCDNUploadDTO(img,String.valueOf(businessPostsAssets.getId()), Constants.CDN_UPLOAD_TOPIC);
				businessCoreService.uploadMediaToCDN(uploadDTO);
			});
			List<Integer> ids = businessPostsAssetsList.stream().map(BusinessPostsAssets::getId).collect(Collectors.toList());
			redisExternalService.set(String.valueOf(postData.getId()),ids);
			LOGGER.info("upload media to CDN processed");
		}
	}

	@Override
	public void uploadPageImageToCDN(Object pageData) {
		try {
			if(Objects.nonNull(pageData)) {
				String imageUrl = null;
				String key = null;
				String topic= Constants.CDN_UPLOAD_PAGE_TOPIC;
				if(pageData instanceof BusinessFBPage) {
					BusinessFBPage fbPage = (BusinessFBPage) pageData;
					key = SocialChannel.FACEBOOK.getName()+"_"+fbPage.getId();
					imageUrl = fbPage.getFacebookPagePictureUrl();
					topic+="-fb";
				} else if(pageData instanceof BusinessGoogleMyBusinessLocation) {
					BusinessGoogleMyBusinessLocation googleMyBusinessLocation = (BusinessGoogleMyBusinessLocation) pageData;
					key = SocialChannel.GOOGLE.getName()+"_"+googleMyBusinessLocation.getId();
					imageUrl = googleMyBusinessLocation.getPictureUrl();
					topic+="-"+SocialChannel.GOOGLE.getName();
				} else if(pageData instanceof BusinessTwitterAccounts) {
					BusinessTwitterAccounts twitterAccounts = (BusinessTwitterAccounts) pageData;
					key = SocialChannel.TWITTER.getName()+"_"+twitterAccounts.getId();
					imageUrl = twitterAccounts.getProfilePicUrl();
					topic+="-tw";
				} else if (pageData instanceof BusinessLinkedinPage) {
					BusinessLinkedinPage linkedinPage = (BusinessLinkedinPage) pageData;
					key = SocialChannel.LINKEDIN.getName()+"_"+linkedinPage.getId();
					imageUrl = linkedinPage.getLogoUrl();
					topic+="-li";
				} else if (pageData instanceof  BusinessInstagramAccount) {
					BusinessInstagramAccount instagramAccount = (BusinessInstagramAccount) pageData;
					key = SocialChannel.INSTAGRAM.getName()+"_"+instagramAccount.getId();
					imageUrl = instagramAccount.getInstagramAccountPictureUrl();
					topic+="-ig";
				} else if(pageData instanceof  BusinessYoutubeChannel) {
					BusinessYoutubeChannel youtubeChannel = (BusinessYoutubeChannel) pageData;
					key = SocialChannel.YOUTUBE.getName()+"_"+youtubeChannel.getId();
					imageUrl = youtubeChannel.getPictureUrl();
					topic+="-yt";
				} else if(pageData instanceof BusinessTiktokAccounts) {
					BusinessTiktokAccounts tiktokAccounts = (BusinessTiktokAccounts) pageData;
					key = SocialChannel.TIKTOK.getName().toLowerCase() + "_"+tiktokAccounts.getId();
					imageUrl = tiktokAccounts.getProfileImageUrl();
					topic+="-tk";
				}

				if(StringUtils.isNotEmpty(key) && StringUtils.isNotEmpty(imageUrl)) {
					CDNUploadDTO uploadDTO = getCDNUploadDTO(imageUrl, key, topic);
					businessCoreService.uploadMediaToCDN(uploadDTO);
				}
			}
		} catch (Exception e) {
			LOGGER.info("error while sending upload cdn req to core: {}", e.getMessage());
		}
	}

	@Override
	public void migrateImageToCDN(Integer id, List<String> images) {
		List<BusinessPostsAssets> businessPostsAssetsList = new ArrayList<>();
		images.forEach(img->{
			BusinessPostsAssets businessPostsAssets = new BusinessPostsAssets();
			businessPostsAssets.setBusinessPostsId(id);
			businessPostsAssets.setIsImage(1);
			businessPostsAssets.setMediaUrl(img);
			businessPostsAssetsList.add(businessPostsAssetsRepo.saveAndFlush(businessPostsAssets));
			CDNUploadDTO uploadDTO = getCDNMigrateDTO(img,businessPostsAssets.getId());
			businessCoreService.uploadMediaToCDN(uploadDTO);
		});
		List<Integer> ids = businessPostsAssetsList.stream().map(BusinessPostsAssets::getId).collect(Collectors.toList());
		redisExternalService.set(String.valueOf(id),ids);
	}
	private Date convertScheduleDateToTimezone(Date date, Long timeAddition, boolean ratesExhausted) {
		// when rates are exhausted, next scheduled time = current time + time after which rates will be refreshed
		// else, next scheduled time = actual scheduled time + time (configurable)
		Date publishDateUtc = null;
		//Date publishDate = new SimpleDateFormat(SCHEDULE_DATE_FORMAT).parse(date);
		Calendar calPst = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
		calPst.setTime(ratesExhausted ? new Date() : date);
		calPst.add(Calendar.SECOND, Math.toIntExact(timeAddition));
		publishDateUtc = ControllerUtils.convertToUTCTimeZone(calPst).getTime();
		return publishDateUtc;
	}

	@Override
	public Boolean retryPostIfEligible(BirdeyeSocialException exception,SocialPostPublishInfo publishInfo)  {
		if(Objects.isNull(publishInfo)) {
			LOGGER.info("PublishInfo is null");
			throw exception;
		}
		LOGGER.info("Retry post if eligible: {}", publishInfo.getId());

		if(Objects.nonNull(exception.getData())) {
			boolean flag = getExceptionRetryMatch(publishInfo.getSourceId(), exception.getMessage(), (Integer) exception.getData().get("error_code"),
					(Integer) exception.getData().get("error_sub_code"));
			Integer publishInfoId = publishInfo.getId();

			if (flag) {
				boolean retryLimit = retryQuotaCheck(publishInfoId);
				if(retryLimit) {
					LOGGER.info("Retry exceeded limit for post id :{}", publishInfoId);
					return false;
				}

				SocialPostRetryRequest socialPostScheduleIdsRequest = new SocialPostRetryRequest();
				socialPostScheduleIdsRequest.setSocialPostId(publishInfoId);
				socialPostScheduleIdsRequest.setSourceId(publishInfo.getSourceId());

				if(publishInfo.getSourceId().equals(SocialChannel.FACEBOOK.getId()) &&
						(StringUtils.isEmpty(publishInfo.getSocialPost().getPostText()))) {
					List<Object> facebookFailedResult= updateFacebookFailureData( exception ,publishInfo);
					if(CollectionUtils.isNotEmpty(facebookFailedResult)) {
						socialPostScheduleIdsRequest.setErrorMessage(facebookFailedResult.get(0).toString());
						socialPostScheduleIdsRequest.setFailureBucket((Integer) facebookFailedResult.get(1));
					}
				}
				SocialSamaySchedulingInfo socialPostRetry = new SocialSamaySchedulingInfo(Constants.retryConstants,JSONUtils.toJSON(socialPostScheduleIdsRequest), JSONUtils.toJSON(exception.getData()));
				Integer externalId=socialRetryPostRepo.saveAndFlush(socialPostRetry).getId();

				Long timeDurationDelayInSeconds = getTimeDurationToRetryInSeconds();
				Integer maxRetryTimeDelayInHours = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getMaxPostingRetryTimeInHours();
				if (timeDurationDelayInSeconds > TimeUnit.HOURS.toSeconds(maxRetryTimeDelayInHours)) {
					LOGGER.info("Time duration for retry is greater than {} hours for  post id :{}", maxRetryTimeDelayInHours, publishInfoId);
					return false;
				}
				Date scheduledDelayDate = convertScheduleDateToTimezone(publishInfo.getPublishDate(), timeDurationDelayInSeconds, isRateExhausted());
				LOGGER.info("Actual publish date: {}, delay scheduled date: {}", publishInfo.getPublishDate(), scheduledDelayDate);

				boolean flagSamay = submitPublishPostOnSamay(socialPostScheduleIdsRequest,publishInfoId, publishInfo.getBusinessId(),
						scheduledDelayDate, externalId, publishInfo.getSourceId());
				if (flagSamay) {
					//publishInfo.setPublishDate(scheduledDelayDate);
					LOGGER.info("Retry attempted for post id :{}", publishInfoId);
					return true;
				}
			}
		}
		return false;
	}

	private boolean isRateExhausted() {
		return StringUtils.isNotEmpty(MDC.get("requestId") + RateLimitingConstants.RETRY_TIME_SECONDS);
	}

	private Long getTimeDurationToRetryInSeconds() {
		String timeToRefreshRatesKey = MDC.get("requestId") + RateLimitingConstants.RETRY_TIME_SECONDS;
		String timeToRefreshRates = MDC.get(timeToRefreshRatesKey);

		return StringUtils.isNotEmpty(timeToRefreshRates)
				? Long.parseLong(timeToRefreshRates)
				: TimeUnit.MINUTES.toSeconds(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getTimeDurationDelay());
	}

	private List<Object> updateFacebookFailureData(BirdeyeSocialException e, SocialPostPublishInfo publishInfo) {
		List<PermissionMapping> permissionMapping = new ArrayList<PermissionMapping>();
		List<Object> failureData= new ArrayList<>();
		boolean check = false;
		boolean isErrorMessageFound = false;
		if (Objects.nonNull(e.getData().get("error_code"))) {
			if (Objects.nonNull(e.getData().get("error_sub_code"))) {
				permissionMapping = permissionMappingService.getDataByChannelandHttpResponseAndErrorCodeAndErrorSubCode(Constants.FACEBOOK, (Integer) e.getData().get("http_response"), (Integer) e.getData().get("error_code"), (Integer) e.getData().get("error_sub_code"));
				check = true;
			}
			if (!check) {
				permissionMapping = permissionMappingService.getDataByChannelandHttpResponseAndErrorCode(Constants.FACEBOOK, (Integer) e.getData().get("http_response"), (Integer) e.getData().get("error_code"));
			}
		} else {
			permissionMapping = permissionMappingService.getDataByChannelandHttpResponse(Constants.FACEBOOK, (Integer) e.getData().get("http_response"));
		}
		if (permissionMapping.size() == 1) {
			failureData.add(permissionMapping.get(0).getErrorMessage());
			failureData.add(permissionMapping.get(0).getBucket());
			isErrorMessageFound = true;
		} else if (permissionMapping.size() > 1) {
			for (PermissionMapping permissionMappingList : permissionMapping) {
				if (e.getMessage().contains(permissionMappingList.geterrorActualMessage())) {
					failureData.add(permissionMappingList.getErrorMessage());
					failureData.add(permissionMappingList.getBucket());
					isErrorMessageFound = true;
					break;
				}
			}
		}
		if(!isErrorMessageFound){
			PermissionMapping permissionMappingUnknown = permissionMappingService.getDataByChannelAndPermissionCodeAndModule(Constants.FACEBOOK, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR,Constants.PUBLISH_MODULE);
			failureData.add(permissionMappingUnknown.getErrorMessage());
			failureData.add(permissionMappingUnknown.getBucket());
			LOGGER.info("New error found retry : {}", e.getMessage());
			LOGGER.info("Error code sub values retry :{}", e.getData());
		}
		return failureData;
	}


	@Override
	public File getPublicFile(String mediaUrls) {
		String mediaUrl = mediaUrls;
		File tempFile = null;
		FileOutputStream out = null;
		try {
			URL website = new URL(mediaUrl.trim());

			HttpURLConnection conn = (HttpURLConnection) website.openConnection();
			String fileExtension=FilenameUtils.getExtension(mediaUrl);
			tempFile = File.createTempFile(System.currentTimeMillis() + "", "."+fileExtension);
			tempFile.deleteOnExit();
			out = new FileOutputStream(tempFile);
			IOUtils.copy(conn.getInputStream(), out);
		} catch (Exception e) {
			throw new SocialBirdeyeException(ErrorCodes.ERROR_DOWNLOAD_MEDIA_FILE, e.getMessage());
		} finally {
			if(out != null) {
				try {
					out.close();
				} catch (IOException e) {
					LOGGER.info("inputStream close IOException: {}",e.getMessage());
				}
			}
		}
		return tempFile;
	}

	@Override
	public boolean checkPermission(String permissions, Integer sourceId, String module) {
		try {
			SocialModulePermission socialModulePermission = socialModulePermissionService.getPermissionsForChannelAndModule(sourceId, module);

			List<String> modulePermissions = new ArrayList<>();
			if(Objects.nonNull(socialModulePermission) && Objects.nonNull(socialModulePermission.getPermissionsNeeded())) {
				modulePermissions = Arrays.asList(socialModulePermission.getPermissionsNeeded().split(","));
			}

			if (CollectionUtils.isEmpty(modulePermissions) || StringUtils.isEmpty(permissions)) {
				LOGGER.info("No permission found for this");
				return false;
			}

			for(int i = 0; i < modulePermissions.size(); i++) {
				if(!permissions.contains(modulePermissions.get(i))) {
					return false;
				}
			}

			return true;

		} catch (Exception ex) {
			LOGGER.info("Something went wrong while checking permission");
			return false;
		}
	}

	@Override
	public String getCdnImageForPageProfileUrl(String externalPageId, Integer sourceId) {
		BusinessPagePictureAssets asset = businessPagePictureAssetsRepo.findByExternalPageIdAndSourceId(externalPageId, sourceId);
		if(Objects.nonNull(asset)) {
			return asset.getCdnUrl();
		}

		return null;
	}

	@Override
	public Map<String, String> getCdnImageListForPageProfileUrl(List<String> externalPageIds, Integer sourceId) {
		List<BusinessPagePictureAssets> assetList = businessPagePictureAssetsRepo.findBySourceIdAndExternalPageIdIn(sourceId, externalPageIds);
		if(CollectionUtils.isNotEmpty(assetList)) {
			return assetList.stream().collect(Collectors.toMap(x->x.getExternalPageId(), x->x.getCdnUrl()));
		}
		return new HashMap<>();
	}

	@Override
	public boolean retryQuotaCheck(Integer postId) {
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date date = new Date();
		String key = "RETRY_POST_QUOTA:".concat(postId.toString()).concat(dateFormat.format(date));
		Optional<Object> data = redisExternalService.get(key);
		LOGGER.info("Social Post: retry quota key: {}", key);

		if(data.isPresent()){
			LOGGER.info("Social Post: retry post quota for businessId: {} is: {}", postId, data.get());

			Integer limit =  Integer.parseInt((String) data.get());
			if(limit <= 0) {
				return true;
			} else {
				limit -= 1;
				redisExternalService.set(key, limit.toString());
				return false;
			}
		} else {
			String limit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getRedisRetryLimit();
			redisExternalService.set(key, limit);
			return false;
		}
	}

	public boolean retryQuotaCheck(String feedId) {
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date date = new Date();
		String key = "RETRY_ENGAGE_QUOTA:".concat(feedId).concat(dateFormat.format(date));
		Optional<Object> data = redisExternalService.get(key);
		LOGGER.info("Social Engage: retry quota key: {}", key);

		if(data.isPresent()){
			LOGGER.info("retry engage quota for feedId {}", feedId);

			int limit =  Integer.parseInt((String) data.get());
			if(limit <= 0) {
				return true;
			} else {
				limit -= 1;
				redisExternalService.set(key, Integer.toString(limit));
				return false;
			}
		} else {
			String limit = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getRedisRetryLimit();
			redisExternalService.set(key, limit);
			return false;
		}
	}

	private boolean submitPublishPostOnSamay(SocialPostRetryRequest socialPostScheduleIdsRequest,Integer postId, Integer businessId, Date scheduledDate, Integer externalId, Integer sourceId) {
		Long scheduleDateToEpoch = scheduledDate.toInstant().toEpochMilli();
		SamayScheduleEventRequest samayRequest = SamayUtils.getSamayScheduleEventRequest(JSONUtils.toJSON(socialPostScheduleIdsRequest), Long.valueOf(externalId),
				Long.valueOf(businessId), 0, 0, SamayRetryTypeEnum.SIMPLE.name(), scheduleDateToEpoch, SamayEventTypeEnum.MESSENGER.getType(),
				SOCIAL_POST_RETRY_TOPIC,true);

		LOGGER.info("Social Post: Submitting request to samay for request retry: {}", samayRequest);
		boolean requestSent = samayService.pushMessageToSamayScheduler(samayRequest);
		return requestSent;
	}


	private boolean getExceptionRetryMatch(Integer channel, String message, Integer code,Integer subErrorCode) {
		List<SocialRetryException> socialRetryExceptions =new ArrayList<>();
		if(Objects.nonNull(subErrorCode) && Objects.nonNull(code)){
			LOGGER.info("checking retry for the following in the db, error_code: {}, error_subcode: {}, message: {}", code, subErrorCode, message);
			socialRetryExceptions =exceptionRetryService.getDataBySourceIdAndSubErrorCodeAndErrorCodeAndErrorMessage(channel,
					subErrorCode,code, message);
		} else if(Objects.nonNull(code)){
			LOGGER.info("checking retry for the following in the db, error_code: {}, message: {}", code, message);
			socialRetryExceptions =exceptionRetryService.getDataBySourceIdAndErrorCodeAndErrorMessage(channel,
					code,message);
		} else {
			LOGGER.info("checking retry for the following in the db, message: {}", message);
			socialRetryExceptions =exceptionRetryService.getDataBySourceIdAndErrorMessage(channel, message);
		}
		LOGGER.info("getExceptionRetryMatch List size :{}",socialRetryExceptions.size());
		if(!socialRetryExceptions.isEmpty()) {
			return true;
		}
		return  false;
	}

	/*private boolean getFBFeedLatestData(FacebookData fbData, FacebookPageAccessInfo accessInfo, SocialPostPublishInfo publishInfo)  {
		try {
			int duration=CacheManager.getInstance()
					.getCache(SystemPropertiesCache.class).getFeedDataTimeDuration();
			FacebookFeedData fbFeeds = fbService.getFacebookPostFeedDateWise(accessInfo.getAccessToken(), new DateTime().minusHours(duration).toString(),
					new DateTime().toString(),accessInfo.getProfileId());
			if (fbFeeds.getData().stream().noneMatch(e -> Objects.nonNull(e.getMessage()) && e.getMessage().equals(fbData.getText()))) {
				LOGGER.info("No data found for the request text for retry :{}", fbData.getText());
				return true;
			}
			LOGGER.info("Data found for the request text for retry :{}", fbData.getText());
			return false;
		}catch (Exception e){
			LOGGER.info("Exception for getting facebook feed data during retry :{}", e.getMessage());
			return false;
		}
	}*/

	public void checkBusinessValidation(BusinessLiteDTO business){
		if(business == null ||
				!(business.getType().equalsIgnoreCase(BusinessTypeEnum.BUSINESS.name())
						|| business.getType().equalsIgnoreCase(BusinessTypeEnum.PRODUCT.name()))){
			LOGGER.info("Invalid business with businessId:{}",business.getBusinessNumber());
			throw new BirdeyeSocialException(ErrorCodes.INVALID_BUSINESS_TYPE);
		}
		if(Objects.isNull(business.getResellerId())){
			LOGGER.info("business is not a child of reseller:{}",business.getBusinessNumber());
			throw new BirdeyeSocialException(ErrorCodes.ONLY_RESELLER_CAN_ACCESS);
		}
		BusinessLiteDTO resellerBusiness= businessCoreService.getBusinessLite(business.getResellerId(),false);
		if(!(resellerBusiness.getActivationStatus().equalsIgnoreCase("paid") || resellerBusiness.getActivationStatus().equalsIgnoreCase("demo")) ||
				!(business.getActivationStatus().equalsIgnoreCase("paid") || business.getActivationStatus().equalsIgnoreCase("demo"))){
			LOGGER.info("inactive business sent against  business ::{} ",business.getResellerId());
			throw new BirdeyeSocialException(ErrorCodes.INACTIVE_BUSINESS);
		}
	}

 	@Override
	public List<String> getFilteredScopeForPage(DebugTokenResponse tokenResponse, List<String> pageId) {
		List<String> filteredScope= new ArrayList<>();
		List<GranularScope> granularScopesArray= tokenResponse.getData().getGranular_scopes();
		List<String> scopesArray= tokenResponse.getData().getScopes();

		Map<String, List<String>> granularScopeMap = granularScopesArray.stream()
				.collect(Collectors.toMap(
						GranularScope::getScope,
						gs -> gs.getTarget_ids() != null ? gs.getTarget_ids() : Collections.emptyList()
				));

		scopesArray.forEach(scope -> {
			if (isScopeAssociatedWithPage(scope, granularScopeMap, pageId)) {
				filteredScope.add(scope);
			}
		});
		return filteredScope;
	}

	private static boolean isScopeAssociatedWithPage(String scope, Map<String, List<String>> granularScopeMap, List<String> pageIds) {

		if(MapUtils.isEmpty(granularScopeMap) || !granularScopeMap.containsKey(scope)) {
			return true;
		}
		List<String> targetIdsArray = granularScopeMap.get(scope);

		if (CollectionUtils.isEmpty(targetIdsArray)) {
			return true;
		} else {
			for (String pageId : pageIds) {
				if (targetIdsArray.contains(pageId)) {
					return true;
				}
			}
			return false;
		}
	}

	@Override
	public ConsumerTokenAndSecret getAppKeyAndTokenV2(String type){
		if ("googleplus".equalsIgnoreCase(type)) {
			ConsumerTokenAndSecret consumerToken = new ConsumerTokenAndSecret();
			LOGGER.info("Using default domain creds Google V2");
			GoogleClientCred defaultCreds = googleClientCredRepo.findGoogleCredDefault();
			consumerToken.setToken(defaultCreds.getClientId());
			consumerToken.setSecret(defaultCreds.getClientSecret());
			consumerToken.setCredentialId(defaultCreds.getId());
			return consumerToken;
		}
		return null;
	}

	@Cacheable(cacheNames = "GOOGLE_REDIRECT_URL", key = "'GoogleRedirectUrlInfo'")
	@Override
	public List<String> getGoogleRedirectUrlInfoConfig() {
		LOGGER.info("Loading google redirect url data");
		List<GoogleRedirectUrlInfo> googleRedirectUrlInfoList = googleRedirectUrlRepo.findAll();
		List<String> redirectUrls=googleRedirectUrlInfoList.stream().map(GoogleRedirectUrlInfo::getGoogleRedirectUrlInfo).collect(Collectors.toList());
		LOGGER.info("Loading google redirect url data -> Successful");
		return redirectUrls;
	}

	@Override
	public void removeMapping(Integer businessId, String pageId, String channel, String pageType) {
		LocationPageMappingRequest locationPageMappingRequest = new LocationPageMappingRequest();
		locationPageMappingRequest.setLocationId(businessId);
		locationPageMappingRequest.setPageId(String.valueOf(pageId));
		locationPageMappingRequest.setType(pageType);
		RemovePageMappingRequest request = RemovePageMappingRequest.builder()
				.channel(channel)
				.locationPageMappingRequests(Collections.singletonList(locationPageMappingRequest))
				.build();
		kafkaProducer.sendObjectV1(KafkaTopicEnum.SOCIAL_REMOVE_MAPPING.getName(), request);
	}

	@Override
	public void deletePage(String channel, String pageId) {
		DeleteEventRequest request = DeleteEventRequest.builder()
				.channel(channel)
				.pagesIds(Collections.singletonList(pageId))
				.build();
		//delete from BusinessFBPage
		kafkaProducer.sendObjectV1(KafkaTopicEnum.SOCIAL_DELETE_PAGE.getName(),request);
	}

	@Override
	public void sendPostInsights(PostData postData) {
		if(Objects.isNull(postData.getMasterPostId())){
			LOGGER.info("Master post is not available for this post");
			return;
		}
		PostInsightsData postInsightsData = ConversionUtils.convertPostInsightsData(postData);
		kafkaProducer.sendObjectWithKeyV1(String.valueOf(postData.getMasterPostId()),KafkaTopicEnum.SOCIAL_POST_INSIGHTS.getName(),postInsightsData);
	}

	@Override
	public PostData prepareDeltaInsightsForPost(PostData postData, BusinessPosts businessPosts) {
		if (Objects.nonNull(postData.getBePostId())) {
			PostInsightDTO oldPostInsight = null;
			try {
				if (Objects.nonNull(businessPosts.getResponse())) {
					oldPostInsight = JSONUtils.fromJSON(businessPosts.getResponse(), PostInsightDTO.class);
				}
			} catch (Exception e) {
				LOGGER.info("Exception while converting insight response for: {} {}: {}", businessPosts.getId(),businessPosts.getResponse(),e.getMessage());
			}
			if(Objects.isNull(oldPostInsight)) {
				postData.setDeltaEngagement(0);
				postData.setDeltaImpression(0);
				return postData;
			}
			postData.setDeltaEngagement((Objects.isNull(postData.getEngagement()) ? 0 : postData.getEngagement()) - (Objects.isNull(oldPostInsight.getEngagement()) ? 0 : oldPostInsight.getEngagement()));
			postData.setDeltaImpression((Objects.isNull(postData.getImpression()) ? 0 : postData.getImpression()) - (Objects.isNull(oldPostInsight.getImpression()) ? 0 : oldPostInsight.getImpression()));
		}
		return postData;
	}

	@Override
	public List<MediaUrls> convertMediaWithPictures(List<MediaListInfo> media, Long businessNumber) throws Exception {
		LOGGER.info("Upload media for media info list : {}", media);
		List<MediaUrls> mediaUrlsList = new ArrayList<>();
		ForkJoinPool forkJoinPool = new ForkJoinPool(4);

		try {
			forkJoinPool.invoke(ForkJoinTask.adapt(() -> media.parallelStream().forEach(mediaInfo-> {
				PicturesqueMediaUploadRequest uploadRequest = ConversionUtils.convertPicturesRequest(mediaInfo,businessNumber);
//				String mediaS3Url = picturesqueGen.uploadMedia(uploadRequest);
				PicturesqueMediaResponse mediaResponse = picturesqueGen.uploadMediaWithMetaData(uploadRequest);
				LOGGER.info("Media uploaded to S3 with url : {}", mediaInfo.getMediaUrl());
				if(Objects.isNull(mediaResponse)){
					return;
				}
				String mediaS3Url = mediaResponse.getMediaUrl();
				String shortUrl = null;
				if(StringUtils.isNotEmpty(mediaS3Url)) {
					String[] brokenUrl = mediaS3Url.split(businessNumber.toString());
					if(brokenUrl.length==2) {
						shortUrl = brokenUrl[1].substring(1,brokenUrl[1].length());
					}
				}
				MediaUrls mediaUrls = new MediaUrls();
				if (Objects.nonNull(shortUrl)) {
					if (isVideoStream(mediaInfo)) {
						mediaUrls.setVideoUrl(shortUrl);
						mediaInfo.setMediaAssetUrl(shortUrl);
					} else {
						mediaUrls.setImageUrl(shortUrl);
						mediaInfo.setMediaAssetUrl(shortUrl);
					}
					mediaUrls.setMetaData(JSONUtils.toJSON(mediaResponse));
					mediaUrls.setMediaSequence(shortUrl);
					mediaUrlsList.add(mediaUrls);
				}
			})));
		} catch (Exception e) {
			LOGGER.info("failed to execute picturesque in parallel");
			throw new Exception("not able to execute the picture upload");
		} finally {
			forkJoinPool.shutdown();
		}
		return mediaUrlsList;
	}

	private boolean isVideoStream (MediaListInfo mediaInfo){
		return "VIDEO".equalsIgnoreCase(mediaInfo.getType());
	}

	@Override
	public Boolean isValidUrl(String imageUrl) {
		List<String> request = Collections.singletonList(imageUrl);
		List<Boolean> response = picturesqueGen.validateImageUrls(request);
		if(Objects.nonNull(response) && CollectionUtils.isNotEmpty(response)) {
			return response.get(0);
		}
		return false;
	}

	@Override
	public String getInitials(String authorName) {
		if(Objects.isNull(authorName) || authorName.isEmpty()) {
			return "";
		}
		List<String> initials= Arrays.asList(authorName.toUpperCase().split(" "));
		StringBuilder result = new StringBuilder();
		if(initials.size() >= 2) {
			result.append(initials.get(0).isEmpty()?"":initials.get(0).charAt(0));
			result.append(initials.get(1).isEmpty()?"":initials.get(1).charAt(0));
		}
		else {
			result.append(initials.get(0).isEmpty()?"":initials.get(0).charAt(0));
		}
		return result.toString();
	}
	public String extractGMBPermission( String scope) {
		List<String> strArray = Arrays.asList(scope.replace(Constants.SPLIT_STRING_FOR_GMB, "").split(" "));
		if (CollectionUtils.isNotEmpty(strArray)) {
			String permissions = strArray.toString().replace("[", "").replace("]", "").trim();
			LOGGER.info("permissions: {}", permissions);
			return permissions;
		}
		return "";
	}
	@Override
	public boolean checkBusinessSMB(BusinessLiteDTO business) {
		return ("Business".equals(business.getType()) || "Product".equals(business.getType()) && business.getEnterpriseId()==null);
	}

	@Override
	public void sortImageUrlFromSequenceId(List<String> imageUrls, String postMetaData) {
		SocialPostSchedulerMetadata schedulerMetadata = JSONUtils.fromJSON(postMetaData,SocialPostSchedulerMetadata.class);
		sortImageUrlFromSequenceId(imageUrls, schedulerMetadata);
	}

	@Override
	public void sortImageUrlFromSequenceId(List<String> imageUrls, SocialPostSchedulerMetadata schedulerMetadata) {
		if(Objects.isNull(schedulerMetadata) || com.birdeye.social.utils.StringUtils.isEmpty(schedulerMetadata.getMediaSequence())){
			LOGGER.info("schedulerMetadata is null or schedulerMetadata.getMediaSequence() is empty");
			return;
		}
		List<String> mediaSequence = JSONUtils.collectionFromJSON(schedulerMetadata.getMediaSequence(),String.class);
		if(CollectionUtils.isEmpty(mediaSequence) || CollectionUtils.isEmpty(imageUrls)){
			LOGGER.info("Media Sequence or Image url is empty");
			return;
		}
		imageUrls.sort((o1, o2) -> {
			int index1 = getOrderIndex(mediaSequence, o1);
			int index2 = getOrderIndex(mediaSequence, o2);
			return Integer.compare(index1, index2);
		});
	}

	private static int getOrderIndex(List<String> orderList, String item) {
		for (int i = 0; i < orderList.size(); i++) {
			if (item.contains(orderList.get(i))) {
				return i;
			}
		}
		return orderList.size(); // If not found, put it at the end
	}

	@Override
	public String prepareBusinessAddress(BusinessLocationLiteEntity location) {
		StringBuilder address = new StringBuilder();
		if (StringUtils.isNotEmpty(location.getAddress1())) {
			address.append(location.getAddress1()).append(", ");

		}

		if (StringUtils.isNotEmpty(location.getAddress2())) {
			address.append(location.getAddress2()).append(", ");

		}
		if (StringUtils.isNotEmpty(location.getCity())) {
			address.append(location.getCity()).append(", ");

		}
		if (StringUtils.isNotEmpty(location.getState())) {
			address.append(location.getState()).append(" ");

		}
		// Zipcode will be always there
		if (StringUtils.isNotEmpty(location.getZip())) {
			address.append(location.getZip());

		}
		return address.toString();
	}
	@Override
	public BusinessLocationLiteEntity getMappedLocationInfo(Map<String,Object> locationDetails, Integer businessId, String pageName) {

		if(Objects.nonNull(locationDetails) && Objects.nonNull(locationDetails.get("location"))){

			Map<String,Object> locationData  = (Map<String,Object>) locationDetails.get("location");
			BusinessLocationLiteEntity data = new BusinessLocationLiteEntity();

			log.info("Prepare BusinessLiteDTO for location id :{}",locationData.get("id"));
			if( Objects.nonNull(locationData)) {
				data.setAddress1((String) locationData.get("address1"));
				data.setCity((String) locationData.get("city"));
				data.setState((String) locationData.get("state"));
				data.setId((Integer) locationDetails.get("businessId"));
				data.setZip((String) locationData.get("zip"));
				data.setCountryCode((String) locationData.get("countryCode"));
				data.setAlias1((String) locationDetails.get("businessAlias"));
				data.setName((String) locationDetails.get("businessName"));
				Long businessNumber = (Long) locationDetails.get("businessNumber");
				Long enterpriseNumber = (Long) locationDetails.get("enterpriseNumber");
				if(Objects.nonNull(businessNumber) && Objects.nonNull(enterpriseNumber) && businessNumber.equals(enterpriseNumber)) {
					data.setAccountName((String) locationDetails.get("businessName"));
				} else {
					data.setAccountName((String) locationDetails.get("enterpriseName"));
				}
			}
			return data;
		} else {
			if(Objects.nonNull(locationDetails)) {
				BusinessLocationLiteEntity data = new BusinessLocationLiteEntity();
				data.setId((Integer) locationDetails.get("businessId"));
				data.setZip((String) locationDetails.get("businessName"));
				data.setAlias1((String) locationDetails.get("businessAlias"));
				data.setName((String) locationDetails.get("businessName"));
				data.setAccountName((String) locationDetails.get("enterpriseName"));
				Long businessNumber = (Long) locationDetails.get("businessNumber");
				Long enterpriseNumber = (Long) locationDetails.get("enterpriseNumber");
				if(Objects.nonNull(businessNumber) && Objects.nonNull(enterpriseNumber) && businessNumber.equals(enterpriseNumber)) {
					data.setAccountName((String) locationDetails.get("businessName"));
				} else {
					data.setAccountName((String) locationDetails.get("enterpriseName"));
				}
				return data;
			} else {
				BusinessLocationLiteEntity data = new BusinessLocationLiteEntity();
				data.setId(businessId);
				data.setZip(pageName);
				return data;
			}
		}
	}

	@Override
	public ChannelPageDetails sortInvalidAndValidPage(List<ChannelPages> pageInfo) {
		ChannelPageDetails channelPageDetails = new ChannelPageDetails();
		Integer invalidPagesCount = 0;
		for(ChannelPages page: pageInfo) {
			if(!page.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())) {
				invalidPagesCount++;
			}
		}
		channelPageDetails.setDisconnected(invalidPagesCount);
		channelPageDetails.setPages(pageInfo);
		return channelPageDetails;
	}

	@Override
	public void setDisabledAsNullForAllChannel(ChannelPageInfo accountInfo) {
		if(MapUtils.isEmpty(accountInfo.getPageTypes())) return;
		for(Map.Entry<String, List<ChannelAccountInfo>> entry: accountInfo.getPageTypes().entrySet()) {
			List<ChannelAccountInfo> channelAccountInfos = entry.getValue();
			if(CollectionUtils.isNotEmpty(channelAccountInfos)) channelAccountInfos.forEach(s->s.setDisabled(null));
		}
	}

	@Override
	public String getPageTokenGranularPermissions(String pageAccessToken, List<String> pageId) {
		SocialAppCredsInfo appCredsInfo = socialAppService.getFacebookAppSettings();
		String appAccessToken = appCredsInfo.getChannelAccessToken();
		DebugTokenResponse tokenResponse = null;
		tokenResponse = fbService.getTokenDetails(pageAccessToken, appAccessToken);
		LOGGER.info("Response instagram  page access token {} getTokenDetails :: response {}", pageAccessToken, tokenResponse);
		if (tokenResponse == null || tokenResponse.getData() == null) {
			LOGGER.error("Token response is null");
			return null;
		} else {
			List<String> filteredTokenScopes = getFilteredScopeForPage(tokenResponse,pageId);
			return String.join(",", filteredTokenScopes);
		}
	}

	@Override
	public boolean checkGranular(Long enterpriseId) {
		String flaggedEnterprises = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getGranularScopeFlag();
		if(StringUtils.isEmpty(flaggedEnterprises)) {
			return false;
		}
		if(flaggedEnterprises.equalsIgnoreCase("1")) {
			return true;
		}
		List<Long> flaggedEnterpriseList = Arrays.stream(flaggedEnterprises.split(","))
				.map(String::trim)
				.filter(s -> !s.isEmpty())
				.map(Long::parseLong)
				.collect(Collectors.toList());
		return flaggedEnterpriseList.contains(enterpriseId);
	}

	@Override
	public boolean checkScheduleEditLock(Long enterpriseId) {
		String flaggedEnterprises = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getScheduleEditLockFlag();
		if(StringUtils.isEmpty(flaggedEnterprises)) {
			return false;
		}
		if(flaggedEnterprises.equalsIgnoreCase("1")) {
			return true;
		}
		List<Long> flaggedEnterpriseList = Arrays.stream(flaggedEnterprises.split(","))
				.map(String::trim)
				.filter(s -> !s.isEmpty())
				.map(Long::parseLong)
				.collect(Collectors.toList());
		return flaggedEnterpriseList.contains(enterpriseId);
	}

	@Override
	public void uploadMediaToPicturesque(PostData postData, BusinessPosts businessPosts) {
		LOGGER.info("[uploadMediaToPicturesque] Upload media to Picturesque initiated with request {}", postData);

		if (Objects.isNull(postData) || Objects.isNull(businessPosts)) {
			LOGGER.warn("[uploadMediaToPicturesque] PostData or businessPosts is null, aborting media upload.");
			return;
		}

		// Process images and videos
		List<String> imageUrls = processMedia(businessPosts, postData.getImages(), postData.getId(), true);
		businessPosts.setImageUrls(String.join(",", imageUrls));
		if (!businessPosts.getSourceId().equals(SocialChannel.TIKTOK.getId())) {
			List<String> videoUrls = processMedia(businessPosts, postData.getVideos(), postData.getId(), false);
			businessPosts.setVideoUrls(String.join(",", videoUrls));
		}

		businessPostsRepository.saveAndFlush(businessPosts);
	}

	private List<String> processMedia(BusinessPosts businessPosts, List<String> mediaList, Integer postId, boolean isImage) {
		List<String> uploadedUrls = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(mediaList)) {
			for (String mediaUrl : mediaList) {
				try {
					PicturesqueMediaUploadRequest uploadRequest = preparePicturesqueUploadRequest(mediaUrl, businessPosts.getBusinessId());
					String mediaS3Url = picturesqueGen.uploadMedia(uploadRequest);

					BusinessPostsAssets businessPostsAssets = new BusinessPostsAssets();
					businessPostsAssets.setBusinessPostsId(postId);
					businessPostsAssets.setIsImage(isImage ? 1 : 0);
					businessPostsAssets.setMediaUrl(mediaUrl);
					businessPostsAssets.setCdnUrl(mediaS3Url);

					if (isImage) {
						businessPosts.setImageUrls(mediaS3Url);
					} else {
						businessPosts.setVideoUrls(mediaS3Url);
					}

					businessPostsAssetsRepo.saveAndFlush(businessPostsAssets);
					uploadedUrls.add(mediaS3Url);
				} catch (Exception e) {
					LOGGER.info("Failed to upload media to Picturesque for media URL {} and postId {}", mediaUrl, postId, e);
				}
			}
		}
		return uploadedUrls;
	}



	private PicturesqueMediaUploadRequest preparePicturesqueUploadRequest(String mediaUrl, Integer businessId) {
		return PicturesqueMediaUploadRequest.builder()
				.imgPath(mediaUrl)
				.businessNumber(String.valueOf(businessId))
				.source("other")
				.mimeType("")
				.build();
	}

	@Override
	public boolean checkSamayEventFlag(Integer enterpriseId) {
		String flaggedEnterprises = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSamayEventCheckFlag();
		if(StringUtils.isEmpty(flaggedEnterprises)) {
			return false;
		}
		if(flaggedEnterprises.equalsIgnoreCase("1")) {
			return true;
		}
		List<Integer> flaggedEnterpriseList = Arrays.stream(flaggedEnterprises.split(","))
				.map(String::trim)
				.filter(s -> !s.isEmpty())
				.map(Integer::parseInt)
				.collect(Collectors.toList());
		return flaggedEnterpriseList.contains(enterpriseId);
	}

	// VAPT check : Authorization validations - BIRD-82945
	@Override
	public boolean checkRequestFromAuthorizedSourceUsingBusinessNumber(Long businessNumberStored, Long businessNumberExternal){
		if(Objects.nonNull(businessNumberStored) && Objects.nonNull(businessNumberExternal) && !Objects.equals(businessNumberStored, businessNumberExternal)) {
			LOGGER.error("Unauthorized access : Stored business number {} and external business number {}", businessNumberStored, businessNumberExternal);
			throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_ACCESS, UNAUTHORIZED_ACCESS_MSG);
		}else{
			return true;
		}
	}

	// VAPT check : Authorization validations - BIRD-82945
	@Override
	public boolean checkRequestFromAuthorizedSourceUsingLongResellerID(Long resellerIdStored, Long resellerIdExternal){
		if(!Objects.equals(resellerIdStored, resellerIdExternal)) {
			LOGGER.error("Unauthorized access : Stored reseller id {} and external reseller id {}", resellerIdStored, resellerIdExternal);
			throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_ACCESS, UNAUTHORIZED_ACCESS_MSG);
		}
		return true;
	}

	// VAPT check : Authorization validations - BIRD-82945
	@Override
	public boolean checkRequestFromAuthorizedSourceUsingResellerIdsList(List<Long> resellerIdsStored, Long resellerIdExternal){
		if(resellerIdsStored.stream().noneMatch(resellerIdStored -> Objects.equals(resellerIdStored, resellerIdExternal))) {
				LOGGER.error("Unauthorized access : Stored reseller ids {} and external reseller id {}", resellerIdsStored, resellerIdExternal);
				throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_ACCESS, UNAUTHORIZED_ACCESS_MSG);
		}
		return true;
	}

	// VAPT check : Authorization validations - BIRD-82945
	@Override
	public boolean checkRequestFromAuthorizedSourceUsingBusinessId(Integer businessIdStored, Integer businessIdExternal){
		if(Objects.isNull(businessIdStored) || Objects.isNull(businessIdExternal) || Objects.equals(businessIdStored, businessIdExternal)) {
			return true;
		}
		List<Integer> businessHierarchyList = businessCoreService.getBusinessHierarchyList(businessIdExternal);
		Set<Integer> businessHierarchySet = new HashSet<>(businessHierarchyList);
		return businessHierarchySet.contains(businessIdStored);
	}

	// VAPT check : Authorization validations - BIRD-82945
	@Override
	public boolean checkRequestFromAuthorizedSourceForReseller(Integer resellerId, Integer enterpriseIdStored){
		if(!Objects.equals(resellerId, enterpriseIdStored)){
			LOGGER.error("Unauthorized access : Stored enterprise id {} and external reseller id {}", enterpriseIdStored, resellerId);
			throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_ACCESS, UNAUTHORIZED_ACCESS_MSG);
		}
		return true;
	}

	@Override
	public Integer getResellerIdFromEnterpriseId(Integer enterpriseId){
		BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest("businessId", enterpriseId, false);
		BusinessLiteDTO business = businessCoreService.getBusinessLite(businessLiteRequest);
		if(Objects.isNull(business)){
			LOGGER.info("Business not found for enterpriseId : {}",enterpriseId);
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND);
		}
		return business.getResellerId();
	}

	/**
	 * businessId comparison with "1" means all the business are allowed to sync posts
	 * @param businessId
	 * @return
	 */
	@Override
	public boolean isBusinessAllowedToSyncBusinessPosts(Integer businessId) {
		LOGGER.info("[isBusinessAllowedToSyncBusinessPosts] checking if business is allowed for syncing business posts, businessId: {}", businessId);

		String businessIdsString = CacheManager.getInstance().getCache(SystemPropertiesCache.class)
				.getBusinessIdsForSyncBusinessPosts();

		boolean flag = false;

		if (Objects.nonNull(businessIdsString) && Objects.nonNull(businessId)) {
			if ("1".equals(businessIdsString.trim())) {
				flag = true;
			} else {
				String[] businessIds = businessIdsString.split(",");
				List<String> businessIdList = Arrays.asList(businessIds);
				if (businessIdList.contains(businessId.toString())) {
					flag = true;
				}
			}
		}

		LOGGER.info("[isBusinessAllowedToSyncBusinessPosts] sync business posts flag is: {} for businessId: {}", flag, businessId);
		return flag;
	}

	@Override
	public void retryForAsyncRequest(AsyncRetryRequestWrapper<?> request, String kafkaTopic, TooManyRequestException e) {
		String retryRequestId = request.getObject().getRetryRequestId();
		Integer retryCounter = request.getObject().getRetryCounter();

		LOGGER.info("Retrying with request ID :{}", retryRequestId);

		Integer ASYNC_LIMIT_COUNTER = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getAsyncRateLimitRetryCounter();
		if(Objects.nonNull(retryRequestId) && retryCounter > ASYNC_LIMIT_COUNTER) {
			throw new TooManyRequestException(e.getCode(), e.getMessage(), e.getRetryAfterSeconds());
		}
		request.getObject().setRetryAfterSeconds(e.getRetryAfterSeconds());
		request.getObject().setRetryRequestId(Objects.nonNull(retryRequestId) ? retryRequestId : "RETRY_" + UUID.randomUUID());
		request.getObject().setRetryCounter(retryCounter + 1);
		kafkaProducer.sendObjectV1(kafkaTopic, request.getObject());
	}

	public Boolean getRateLimitingCheckEnabled() {
		Boolean rateLimitEnabled = false;
		try {
		Optional<Object> data = redisExternalService.get("social.api.rate.limiting.enabled");
		if(data.isPresent()) {
			LOGGER.info("Social Rate Limit Data :{}", data);
			rateLimitEnabled = Boolean.parseBoolean((String) data.get());

		}
		} catch (Exception exe) {
				LOGGER.error("Error {} while parsing getRateLimitingCheckEnabled", exe);
		}
		return rateLimitEnabled;
	}

	public boolean checkSocialPostScheduleDateIsSame(Integer masterPostId) {
		boolean isScheduleDateSameForAllSocialPost = true;
		List<Integer> socialPostIds = socialPostRepository.findIdByMasterPostId(masterPostId);
		boolean isScheduleDateFetched = false;
		Date scheduleDate = null;
		if(socialPostIds.size() == 1) {
			log.info("Single social post: {} found for master post: {}", socialPostIds, masterPostId);
			return false;
		}
		for(Integer socialPostId : socialPostIds) {
			if (!isScheduleDateFetched) {
				scheduleDate = socialPostScheduleInfoRepository.getPublishDateBySocialPostId(socialPostId);
				isScheduleDateFetched = true;
				continue;
			}
			Date currentScheduleDate = socialPostScheduleInfoRepository.getPublishDateBySocialPostId(socialPostId);
			if (Objects.nonNull(scheduleDate) && !scheduleDate.equals(currentScheduleDate)) {
				isScheduleDateSameForAllSocialPost = false;
				break;
			}
		}
		return isScheduleDateSameForAllSocialPost;
}

	public boolean isEligibleForChunkMediaUpload(SocialPostsAssets asset, MediaUploadRequest request) {
		if(Objects.isNull(asset) || StringUtils.isEmpty(asset.getAssetMetaData())){
			LOGGER.info("Asset is null or Asset meta data is null");
			return false;
		}
		PostAssetMetaData postAssetMetaData = JSONUtils.fromJSON(asset.getAssetMetaData(),PostAssetMetaData.class);
		if(Objects.isNull(postAssetMetaData)){
			LOGGER.info("PostAssetMetaData is null for asset id : {}",asset.getId());
			return false;
		}
		request.setFileSize(postAssetMetaData.getSize());
		return postAssetMetaData.getSize() >
				Long.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(SystemPropertiesCache.MEDIA_UPLOAD_VIA_CHUNK_SIZE_GREATER_THAN));
	}

	public boolean isTwitterVideoEligibleForChunkMediaUpload(SocialPostsAssets asset, MediaUploadRequest request) {
		if(Objects.isNull(asset) || StringUtils.isEmpty(asset.getAssetMetaData())){
			LOGGER.info("Asset is null or Asset meta data is null");
			return false;
		}
		PostAssetMetaData postAssetMetaData = JSONUtils.fromJSON(asset.getAssetMetaData(),PostAssetMetaData.class);
		if(Objects.isNull(postAssetMetaData)){
			LOGGER.info("PostAssetMetaData is null for asset id : {}",asset.getId());
			return false;
		}
		Long allowedTwitterChunkValue = Long.valueOf(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(SystemPropertiesCache.TWITTER_MEDIA_UPLOAD_VIA_CHUNK_SIZE_GREATER_THAN));
		log.info("postAssetMetaData Size() : {}", postAssetMetaData.getSize());
		log.info("allowedTwitterChunkValue : {}", allowedTwitterChunkValue);
		request.setFileSize(postAssetMetaData.getSize());
		return postAssetMetaData.getSize() > allowedTwitterChunkValue;

	}

	@Override
	public Boolean checkEligibilityForBulkApprovalEvents(Integer businessId) {
		String value = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty("eligible.business.bulkApproval", "ALL");
		if("ALL".equalsIgnoreCase(value) || value.contains(String.valueOf(businessId))) {
			return true;
		}
		return false;
	}
}
