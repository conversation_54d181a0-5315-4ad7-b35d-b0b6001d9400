package com.birdeye.social.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.MediaUploadStatusEnum;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialTagOperation;
import com.birdeye.social.constant.btp.BTPCategory;
import com.birdeye.social.constant.btp.BTPChannel;
import com.birdeye.social.constant.btp.BTPCountry;
import com.birdeye.social.constant.btp.BTPReportType;
import com.birdeye.social.dto.EsMentionDataPoint;
import com.birdeye.social.dto.EsMentionMetaData;
import com.birdeye.social.dto.EsMentionReviewerDataPoint;
import com.birdeye.social.dto.MentionEsRequest;
import com.birdeye.social.entities.*;
import com.birdeye.social.entities.btp.BTPDayWiseData;
import com.birdeye.social.entities.btp.BTPHeatMapData;
import com.birdeye.social.entities.btp.BTPPriorityLookup;
import com.birdeye.social.entities.mediaupload.SocialAssetChunkInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.request.btprequest.BTPPriorityRequest;
import com.birdeye.social.external.request.btprequest.DayWiseDataPoints;
import com.birdeye.social.external.request.btprequest.SocialAIRequest;
import com.birdeye.social.external.request.mediaupload.MediaUploadChunkRequest;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequest;
import com.birdeye.social.external.service.SocialRawPageDetail;
import com.birdeye.social.facebook.ReviewerData;
import com.birdeye.social.google.Actor;
import com.birdeye.social.model.GNIPActivity;
import com.birdeye.social.model.SocialPostInputMessage;
import com.birdeye.social.model.SocialPostMetadata;
import com.birdeye.social.model.TwitterActivity;
import com.birdeye.social.model.*;
import com.birdeye.social.model.listen.UserInfo;
import com.birdeye.social.model.listen.YoutubeMedia;
import com.birdeye.social.model.listen.LinkedinMedia;
import com.birdeye.social.model.listen.MediaInfo;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.CoreUtils;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.SOCIAL_TEMPLATE;
import static com.birdeye.social.constant.btp.BTPCountry.getCoutryByString;
import static com.birdeye.social.utils.JSONUtils.*;

public class DtoToEntityConverter {

	private static final Logger LOGGER = LoggerFactory
			.getLogger(DtoToEntityConverter.class);



	/*
	 * Creates social post tuple in social_post table for displaying review in
	 * publish tab
	 *
	 * @param socialPostInputMessage
	 * 
	 * @return
	 */
	public static SocialPost createSocialPostObject(
			SocialPostInputMessage socialPostInputMessage) {
		SocialPost socialPost = new SocialPost();
		socialPost.setPostText(socialPostInputMessage.getPostText());
		if (StringUtils.isNotEmpty(socialPostInputMessage.getVideos())) {
			SocialPostsAssets videoAsset = new SocialPostsAssets();
			videoAsset.setVideoUrl(socialPostInputMessage.getVideos());
			// videoAsset = assetsRepository.saveAndFlush(videoAsset);
			socialPost.setVideoIds(String.valueOf(videoAsset.getId()));
		} else if (CollectionUtils
				.isNotEmpty(socialPostInputMessage.getLinks())) {
			StringBuilder postText = new StringBuilder(
					socialPost.getPostText());
			postText.append("\n");
			postText.append(
					String.join("\n", socialPostInputMessage.getLinks()));
			socialPost.setPostText(postText.toString());
		}
		if (socialPostInputMessage.getReviewMetaData() != null) {
			socialPost.setCreatedBy(
					socialPostInputMessage.getReviewMetaData().getUserId());
		}
		SocialPostMetadata metadata = new SocialPostMetadata();
		metadata.setPostType(SocialPostMetadata.PostType.REVIEW.getName());
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			String metadataJson = objectMapper.writeValueAsString(metadata);
			socialPost.setPostMetadata(metadataJson);
		} catch (JsonProcessingException e) {
			LOGGER.error(
					"[Social ReviewShare] Exception in converting post metadata object to json ",
					e);
		}
		return socialPost;
	}

	public static Mention forTwitter(GNIPActivity socialActivity) {
		Mention mention = new Mention();
		mention.setChannelType(SocialChannel.TWITTER.getName());
		mention.setSourceId(SocialChannel.TWITTER.getId());
		mention.setCreated(new Date());
		mention.setRuleId(socialActivity.getLinkedRule());
		if (socialActivity.getActivity() != null) {
			TwitterActivity activity = fromJSON(socialActivity.getActivity(),
					TwitterActivity.class);
			if (activity != null) {
				if (activity.getBody() != null) {
					if (activity.getBody().startsWith("RT @")) {
						LOGGER.warn("Skipping retweet Activity: {}",
								activity.getBody());
						return null;
					} else {
						mention.setComments(activity.getBody());
					}
				}
				String businessNumber = JSONUtils.nodeValue(activity.getGnip(),
						"/matching_rules/0/tag");
				mention.setAccountId(Long.parseLong(businessNumber));
				// Rule --> ID, BUSINESS_GNIP_RULE#gnip_rule_id id
				// Tweet info
				mention.setMentionDate(activity.getPostedTime());
				mention.setExternalId(activity.getId());
				mention.setMentionUrl(activity.getLink());
				// mention author information
				UserInfo userInfo = new UserInfo();
				if (activity.getActor() != null) {
					// TODO: JSONPointers are not cached
					userInfo.setProfileId(nodeValue(activity.getActor(), "/preferredUsername"));
					userInfo.setName(nodeValue(activity.getActor(), "/displayName"));
					userInfo.setProfileUrl(nodeValue(activity.getActor(), "/link"));
					userInfo.setProfileImage(nodeValue(activity.getActor(), "/image"));
					userInfo.setFollowerCount(nodeValue(activity.getActor(),"/followersCount"));
					userInfo.setPostCount(nodeValue(activity.getActor(),"/statusesCount"));
					mention.setUserInfo(toJSON(userInfo));
				}
				// activity ID in current review table is reference to
				// gnip_activity
				// table and is not required.
				mention.setMediaInfo(toJSON(activity.getTwitter_entities()));
			}
		}
		return mention;
	}

	public static Mention forFB(GNIPActivity socialActivity, Long businessNumber,Integer businessId) {
		Mention mention = new Mention();
		mention.setChannelType(SocialChannel.FACEBOOK.getName());
		mention.setBusinessId(businessId);
		mention.setAccountId(businessNumber);
		mention.setSourceId(SocialChannel.FACEBOOK.getId());
		mention.setCreated(new Date());
		mention.setRuleId(socialActivity.getLinkedRule());
		if (socialActivity.getPost() != null) {
			mention.setMentionDate(socialActivity.getPost().getPostDate());
			String content = socialActivity.getPost().getMessage();
			if (content != null) {
				// TODO: Review it.
				content = content.replaceAll("<br />", "\n")
						.replaceAll("<br/>", "\n").replaceAll("<.*?>", "");
			}
			mention.setComments(content);
			String externalId = socialActivity.getPost().getId();
			mention.setExternalId(externalId);
			String mentionUrl = "https://www.facebook.com/"+externalId.replace("_", "/posts/");
			mention.setMentionUrl(mentionUrl);
			// mention author information
			UserInfo userInfo = new UserInfo();
			ReviewerData from = socialActivity.getPost().getFrom();
			// FB API doesn't provide data for disabled users.
			if (Objects.nonNull(from)) {
				userInfo.setProfileId(from.getId());
				userInfo.setName(from.getName());
				userInfo.setProfileUrl("https://www.facebook.com/" + from.getId());
			}
			mention.setUserInfo(toJSON(userInfo));
			// activity ID in current review table is reference to gnip_activity
			// table and is not required.
		}
		return mention;
	}

	public static Mention forYoutube(GNIPActivity socialActivity,Long businessNumber,Integer businessId, Map<?, ?> channelInfo) {
		Mention mention = new Mention();
		mention.setChannelType(SocialChannel.YOUTUBE.getName());
		mention.setSourceId(SocialChannel.YOUTUBE.getId());
		mention.setBusinessId(businessId);
		mention.setAccountId(businessNumber);
		mention.setCreated(new Date());
		mention.setRuleId(socialActivity.getLinkedRule());
		if (socialActivity.getYoutubeActivity() != null) {
			mention.setMentionDate(socialActivity.getYoutubeActivity()
					.getSnippet().getActivityDate());
			String content = socialActivity.getYoutubeActivity().getSnippet()
					.getDescription();
			if (content != null) {
				// TODO: Review it.
				content = content.replaceAll("<br />", "\n")
						.replaceAll("<br/>", "\n").replaceAll("\\<.*?\\>", "");
			}
			mention.setComments(content);
			String externalId = socialActivity.getYoutubeActivity().getId().getVideoId();
			mention.setExternalId(externalId);
			String mentionUrl = socialActivity.getYoutubeActivity().getUrl();
			mention.setMentionUrl(mentionUrl);
			// mention author information
			//Map<String, Object> userInfo = new HashMap<>();
			UserInfo userInfo = new UserInfo();
			String ytChannelId = socialActivity.getYoutubeActivity().getSnippet().getChannelId();
			if (ytChannelId != null) {
				userInfo.setProfileId(ytChannelId);
				userInfo.setProfileUrl("https://www.youtube.com/channel/" + ytChannelId);
				userInfo.setName((String)channelInfo.get("title"));
				mention.setUserInfo(toJSON(userInfo));
			}
			YoutubeMedia youtubeMedia = new YoutubeMedia();
			youtubeMedia.
					setThumbnailUrl(socialActivity.getYoutubeActivity().getSnippet().getThumbnails().getHigh().getUrl());
			youtubeMedia.setVideoUrl(socialActivity.getYoutubeActivity().getUrl());
			// Media information. Thumbnail details.
			mention.setMediaInfo(toJSON(youtubeMedia));

		}
		return mention;
	}
	//@formatter:off	
		/*
		 
		 {
	  "source": "google",
	  "post": null,
	  "linkedRule": 12403,
	  "gnipAct": {
	      "id": 25682607,
	      "activityId": "z12ggdjpwqbxfjopq04cdxij4tbvctsxeyg0k",
	      "sourceId": 2,
	      "createdAt": 1550420515732
	  },
	  "googleActivity": {
	      "id": "z12ggdjpwqbxfjopq04cdxij4tbvctsxeyg0k",
	      "url": "https://plus.google.com/117382324388884950057/posts/XYsooDmckNK",
	      "object": {
	          "plusoners": {
	              "totalItems": 0
	          },
	          "resharers": {
	              "totalItems": 0
	          },
	          "content": "Travel Advice is a Free travel booking adviser that allows the customer to compare millions of cheap flights, hotels, car hire, airport taxi, train, and provide travel advice,<a href=\"https://traveladvice24.com/\" class=\"ot-anchor\">https://traveladvice24.com/</a>",
	          "attachments": [{
	              "objectType": "photo",
	              "displayName": null,
	              "id": "117382324388884950057.6659005359838265346",
	              "content": "hongkong.jpg",
	              "url": "https://plus.google.com/photos/117382324388884950057/albums/6659005361684722049/6659005359838265346"
	          }]
	      },
	      "published": "2019-02-17T16:21:45.027Z",
	      "activityDate": *************,
	      "actor": {
	          "id": "117382324388884950057",
	          "displayName": "Md Alamin"
	      }
	  }
	} 
		 */
		//@formatter:on
	public static Mention forGPlus(GNIPActivity socialActivity, Long businessNumber,Integer businessId) {
		Mention mention = new Mention();
		mention.setChannelType(SocialChannel.GMB.getName());
		mention.setSourceId(SocialChannel.GMB.getId());
		mention.setBusinessId(businessId);
		mention.setAccountId(businessNumber);
		mention.setRuleId(socialActivity.getLinkedRule());
		mention.setCreated(new Date());
		if (socialActivity.getGoogleActivity() != null) {
			mention.setMentionDate(
					socialActivity.getGoogleActivity().getActivityDate());
			String content = socialActivity.getGoogleActivity().getObject()
					.getContent();
			if (content != null) {
				// TODO: Review it.
				content = content.replaceAll("<br />", "\n")
						.replaceAll("<br/>", "\n").replaceAll("<.*?>", "");
			}
			mention.setComments(content);
			String externalId = socialActivity.getGoogleActivity().getId();
			mention.setExternalId(externalId);
			String mentionUrl = socialActivity.getGoogleActivity().getUrl();
			mention.setMentionUrl(mentionUrl);

			// mention author information
			UserInfo userInfo = new UserInfo();
			Actor actor = socialActivity.getGoogleActivity().getActor();
			if (Objects.nonNull(actor)) {
				userInfo.setProfileId(actor.getId());
				userInfo.setProfileUrl(actor.getUrl());
				userInfo.setName(actor.getDisplayName());
				mention.setUserInfo(toJSON(userInfo));
			} else {
				userInfo.setName("Anonymous");
			}
			// Media information. Thumbnail details.
			mention.setMediaInfo(toJSON(socialActivity.getGoogleActivity()
					.getObject().getAttachments()));

		}
		return mention;
	}

	public static MentionResponse getMentionFromEs(EsMentionDataPoint dataPoint) {
		MentionResponse mentionResponse = new MentionResponse();
		if(Objects.isNull(dataPoint)){
			return mentionResponse;
		}
		mentionResponse.setId(Math.toIntExact(dataPoint.getR_id()));
		mentionResponse.setBusinessId(Math.toIntExact(dataPoint.getE_id()));

		mentionResponse.setComments(StringUtils.decode(dataPoint.getCmnt().toString(), "UTF-8"));
		mentionResponse.setSourceId(dataPoint.getS_id());
		setMentionDate(dataPoint.getR_date(), mentionResponse);
		mentionResponse.setReviewUrl(StringUtils.decode(dataPoint.getR_url().toString(),"UTF-8"));
		mentionResponse.setVideoUrl(dataPoint.getVideo());
		String imgUrl = StringUtils.decode(dataPoint.getImg()!=null?dataPoint.getImg().toString():null, "UTF-8");
		if(StringUtils.isNotEmpty(imgUrl)) {
			List<String> images = Arrays.asList(imgUrl.split(","));
			mentionResponse.setMentionImages(images);
		}
		if(StringUtils.isNotEmpty(dataPoint.getMedia_seq())) {
			List<String> media = Arrays.asList(dataPoint.getMedia_seq().split(","));
			mentionResponse.setMediaList(media);
		}
		if(Objects.nonNull(dataPoint.getRwr())) {
			Author author = getAuthor(dataPoint.getRwr());
			mentionResponse.setReviewer(author);
		}
		mentionResponse.setLocation(new MentionResponse.BusinessLocation(Math.toIntExact(dataPoint.getE_id())));
		mentionResponse.setSourceType(dataPoint.getS_type());
		return mentionResponse;
	}

	public static MentionListResponse fromESResponse(String json) {
		MentionListResponse response = new MentionListResponse();
		String total = JSONUtils.nodeValue(json, "/hits/total");
		if (StringUtils.isNotEmpty(total)) {
			response.setCount(Integer.parseInt(total));
		}
		// Process mention data.
		JSONUtils.nodeValueAsArray(json, "/hits/hits", (m) -> {
			Object source = m.get("_source");
			if (source instanceof Map) {
				response.addMention(fromMap((Map<?, ?>) source));
			}
		});
		return response;
	}
	
	private static MentionResponse fromMap(Map<?,?> sourceMap){
		MentionResponse mention = new MentionResponse();
		mention.setId(Integer.parseInt(sourceMap.get("r_id").toString()));
		mention.setBusinessId(Integer.parseInt(sourceMap.get("e_id").toString()));
		mention.setSourceId(Integer.parseInt(sourceMap.get("s_id").toString()));
		setMentionDate(sourceMap.get("r_date").toString(),mention);
		mention.setComments(StringUtils.decode(sourceMap.get("cmnt").toString(), "UTF-8"));
		mention.setReviewUrl(StringUtils.decode(sourceMap.get("r_url").toString(), "UTF-8"));
		String imgUrl = StringUtils.decode(sourceMap.get("img")!=null?sourceMap.get("img").toString():null, "UTF-8");
		if(org.apache.commons.lang3.StringUtils.isNotBlank(imgUrl)){
			String[] imgUrls = imgUrl.split(",");
			mention.setMentionImages(Arrays.stream(imgUrls).collect(Collectors.toList()));
		}
		if(mention.getSourceId() == 140){
			mention.setVideoUrl(StringUtils.decode(sourceMap.get("video").toString(), "UTF-8"));
			mention.setVideoThumbnailUrl(StringUtils.decode(sourceMap.get("video_thumb").toString(), "UTF-8"));
		}
		Map reviewerMap = (Map) sourceMap.get("rwr");
		if(MapUtils.isNotEmpty(reviewerMap)){
			Author author = getAuthor((EsMentionReviewerDataPoint)reviewerMap);
			mention.setReviewer(author);
		}
        mention.setLocation(new MentionResponse.BusinessLocation(Integer.parseInt(sourceMap.get("e_id").toString())));

		return mention;
	}

	private static void setMentionDate(String r_date, MentionResponse mention) {
		try {
			Date mentionDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(r_date);
			String mentionDateStr = new SimpleDateFormat("MMM dd, yyyy").format(CoreUtils.convertToPacificTimeZone(mentionDate).getTime());
			mention.setReviewDate(mentionDateStr);
		} catch (ParseException e) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,"Type cast error : "+e);
		}
	}

	private static Author getAuthor(EsMentionReviewerDataPoint reviewerMap) {
		Author author = new Author();
		if (Objects.nonNull(reviewerMap.getFoll_cnt())) {
			author.setFollowerCount(StringUtils.decode(reviewerMap.getFoll_cnt().toString(), "UTF-8"));
		}
		if (Objects.nonNull(reviewerMap.getPost_cnt())) {
			author.setPostCount(StringUtils.decode(reviewerMap.getPost_cnt().toString(), "UTF-8"));
		}
		if (StringUtils.isNotEmpty(reviewerMap.getImg())) {
			author.setThumbnailUrl(StringUtils.decode(reviewerMap.getImg(), "UTF-8"));
		}
		if (StringUtils.isNotEmpty(reviewerMap.getNick())) {
			author.setNickName(StringUtils.decode(reviewerMap.getNick(), "UTF-8"));
		}
		if (StringUtils.isNotEmpty(reviewerMap.getProfile())) {
			author.setFacebookId(StringUtils.decode(reviewerMap.getProfile(), "UTF-8"));
		}
		return author;
	}

	public static Mention forWebhose(WebhoseRaw post) {
		Mention mention = new Mention();
		String aggregationSource = post.getThreadSiteType();
		mention.setChannelType(post.getThreadSiteType());
		mention.setCreated(new Date());
		mention.setComments(cleanContent(post.getText()));
		
		 String author = (Objects.nonNull(post.getAuthor()) && post.getAuthor().length() > 100) ? post.getAuthor().substring(0, 100)
				: post.getAuthor();
		mention.setNickName(author);
		mention.setTitle(post.getTitle());
		Integer sourceId = null;
		if("news".equals(aggregationSource)) {
			sourceId = 318;
		}else if("blogs".equals(aggregationSource)){
			sourceId = 319;
		}else if("discussions".equals(aggregationSource)){
			sourceId = 320;
		}
		mention.setSourceId(sourceId);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
		try {
			Date formattedDate = formatter.parse(post.getPublished() );
			mention.setMentionDate(formattedDate);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		mention.setExternalId(post.getThreadUuid());
		mention.setMentionUrl(post.getThreadUrl());
		mention.setBusinessId(post.getBusinessId());
		mention.setStagingId(post.getId());
		//mention.setSignature(post.getSignature());
		return mention;
	}

	public static Mention forInstagram(MentionEsRequest request, Date postDate, Integer businessId) {
		Mention mention = new Mention();
		mention.setAccountId(request.getEnterpriseId());
		mention.setComments(request.getText());
		mention.setMentionUrl(request.getUrl());
		mention.setChannel(SocialChannel.INSTAGRAM.getName());
		mention.setExternalId(request.getPostId());
		mention.setPageId(request.getPageId());
		mention.setType(request.getType());
		mention.setMentionDate(postDate);
		MediaInfo mediaInfo = new MediaInfo();
		mediaInfo.setMediaSequence(request.getMediaSequence());
		mention.setMediaInfo(toJSON(mediaInfo));
		UserInfo userInfo = new UserInfo();
		userInfo.setName(request.getReviewerData().getProfile());
		mention.setUserInfo(toJSON(userInfo));
		mention.setSourceId(request.getSourceId());
		mention.setCreated(new Date());
		EsMentionMetaData mentionMetaData = request.getMentionMetaData();
		mention.setMetadata(toJSON(mentionMetaData));
		mention.setBusinessId(businessId);
		return  mention;
	}


	public static Mention forLinkedin(MentionEsRequest request,Integer businessId) throws ParseException {

		final String dateFormatterString = "yyyy-MM-dd HH:mm:ss";
		Mention mention = new Mention();
		mention.setAccountId(request.getEnterpriseId());
		mention.setComments(cleanContent(request.getText()));
		mention.setMentionUrl(request.getUrl());
		mention.setChannel(SocialChannel.LINKEDIN.getName());
		mention.setExternalId(request.getPostId());
		mention.setPageId(request.getPageId());
		mention.setType(request.getType());
		mention.setNotificationId(request.getNotificationId());
		mention.setMentionDate(new SimpleDateFormat(dateFormatterString).parse(request.getDate()));
		MediaInfo mediaInfo = new MediaInfo();
		LinkedinMedia linkedinMedia= new LinkedinMedia();
		linkedinMedia.setImageUrl(request.getImagesList());
		linkedinMedia.setVideoUrl(request.getVideo());
		mediaInfo.setLinkedinMedia(linkedinMedia);
		mention.setMediaInfo(toJSON(mediaInfo));
		UserInfo userInfo = new UserInfo();
		userInfo.setName(request.getReviewerData().getNick());
		userInfo.setProfileUrl(request.getReviewerData().getProfile());
		userInfo.setProfileImage(request.getReviewerData().getImg());
		mention.setUserInfo(toJSON(userInfo));
		mention.setSourceId(request.getSourceId());
		mention.setCreated(new Date());
		EsMentionMetaData mentionMetaData = request.getMentionMetaData();
		mention.setMetadata(toJSON(mentionMetaData));
		mention.setBusinessId(businessId);
		return  mention;
	}
	
	 private static String cleanContent(String content) {
	        String contentInput = content;
	        if (contentInput != null) {
	            contentInput = contentInput.replaceAll("<br />", "\n").replaceAll("<br/>", "\n");
	            contentInput = contentInput.replaceAll("\\<.*?\\>", "");
	        }
	        return contentInput;
	    }

    public static void forDraftPageInfoForMasterPost(Integer masterPostId,Integer enterpriseId, SocialDraftPagesInfo socialDraftPagesInfo) {
		socialDraftPagesInfo.setMasterPostId(masterPostId);
		Integer undefined = Constants.UNDEFINED;
		socialDraftPagesInfo.setAccountPageIdentifier(String.valueOf(enterpriseId));
		socialDraftPagesInfo.setSourceId(undefined);
    }

	public static void forDraftPageInfoForChannels(Integer sourceId, SocialDraftPagesInfo socialDraftPagesInfo,
												   String pageId, Integer masterPostId,boolean isRestrictedForMobile) {
		socialDraftPagesInfo.setSourceId(sourceId);
		socialDraftPagesInfo.setRestrictedForMobile(isRestrictedForMobile ? 1 : 0);
		socialDraftPagesInfo.setMasterPostId(masterPostId);
		socialDraftPagesInfo.setAccountPageIdentifier(pageId);
	}

	public static void convertToSocialDraftResponse(Map<Integer, SocialPostsAssets> socialPostsAssetsMap, Long businessNumber,
													SocialMasterPost socialMasterPost, SocialDraftPostResponse socialDraftPostResponse,
													Map<Integer, SocialDraftChannelsDTO> mapOfMasterPostIdAndSocialChannel) {
		socialDraftPostResponse.setId(socialMasterPost.getId());
		socialDraftPostResponse.setPostText(socialMasterPost.getPostText());
		socialDraftPostResponse.setApprovalEnabled(Objects.nonNull(socialMasterPost.getApprovalWorkflowId()));
		socialDraftPostResponse.setApprovalWorkflowId(socialMasterPost.getApprovalWorkflowId());
		socialDraftPostResponse.setLinkPreviewUrl(socialMasterPost.getLinkPreviewUrl());
		socialDraftPostResponse.setMentions(StringUtils.isEmpty(socialMasterPost.getMentions()) ? null
				: JSONUtils.collectionFromJSON(socialMasterPost.getMentions(), MentionData.class));
		socialDraftPostResponse.setAiPost((Objects.nonNull(socialMasterPost.getAiPost()) && socialMasterPost.getAiPost()==1)?true:false);
		if (StringUtils.isNotEmpty(socialMasterPost.getImageIds())) {
			PostAssetsData postAssetsData = getPostsAssetsById(socialPostsAssetsMap, socialMasterPost.getImageIds(), businessNumber);
			socialDraftPostResponse.setImages(postAssetsData.getImages());
		}
		if (StringUtils.isNotEmpty(socialMasterPost.getVideoIds())) {
			PostAssetsData postAssetsData = getPostsAssetsById(socialPostsAssetsMap, socialMasterPost.getVideoIds(), businessNumber);
			socialDraftPostResponse.setVideoUrl(postAssetsData.getVideos());
			socialDraftPostResponse.setVideoThumbnails(postAssetsData.getVideoThumbnailUrls());
		}
		if(StringUtils.isNotEmpty(socialMasterPost.getCompressedImageIds())){
			PostAssetsData postAssetsData = getPostsAssetsById(socialPostsAssetsMap, socialMasterPost.getCompressedImageIds(), businessNumber);
			socialDraftPostResponse.setCompressedImages(postAssetsData.getCompressedImages());
		}
		prepareMetaData(socialMasterPost.getPostMetadata(),socialDraftPostResponse,businessNumber);
		checkForMasterPost(mapOfMasterPostIdAndSocialChannel,socialMasterPost.getId(),socialDraftPostResponse);
	}

	private static PostAssetsData getPostsAssetsById(Map<Integer, SocialPostsAssets> socialPostsAssetsMap, String ids, Long businessNumber) {
		PostAssetsData postAssetsData = new PostAssetsData();
		List<MediaData> videoDataList = new ArrayList<>();
		List<String> compressedImageList = new ArrayList<>();
		List<MediaData> imageDataList = new ArrayList<>();
		List<String> thumbnailUrls = new ArrayList<>();
		if(StringUtils.isEmpty(ids)){
			return postAssetsData;
		}
		String[] assetIds = ids.split(",");
		String cdnUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCdnImageBaseUrl();
		for(String id : assetIds){
			SocialPostsAssets media = socialPostsAssetsMap.get(Integer.valueOf(id));
			boolean isImage = StringUtils.isNotEmpty(media.getImageUrl());
			String url = !isImage ? media.getVideoUrl() : media.getImageUrl();
			String fullUrl;
			if(url.contains(SOCIAL_TEMPLATE)){
				fullUrl = cdnUrl + "/" + url;
			} else {
				fullUrl = cdnUrl + "/" + Optional.ofNullable(media.getBucketId()).orElse(Long.toString(businessNumber)) + "/" + url;
			}
			if(isImage) {
				imageDataList.add(new MediaData(fullUrl, media.getAssetMetaData()));
				compressedImageList.add(fullUrl);
			} else {
				videoDataList.add(new MediaData(fullUrl, media.getAssetMetaData()));
				thumbnailUrls.add(media.getVideoThumbnail());
			}
		}
		postAssetsData.setImages(imageDataList);
		postAssetsData.setVideos(videoDataList);
		postAssetsData.setVideoThumbnailUrls(thumbnailUrls);
		postAssetsData.setCompressedImages(compressedImageList);
		return postAssetsData;
	}

	private static void prepareMetaData(String postMetadata, SocialDraftPostResponse socialDraftPostResponse, Long businessNumber) {
		if(StringUtils.isEmpty(postMetadata)){
			return;
		}
		SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(postMetadata, SocialPostSchedulerMetadata.class);
		if(Objects.isNull(metadata)){
			return;
		}
		if (StringUtils.isNotEmpty(metadata.getIgPostMetadata())) {
			IgPostMetadata igPostMetadata = JSONUtils.fromJSON(metadata.getIgPostMetadata(), IgPostMetadata.class);
			socialDraftPostResponse.setType(Objects.nonNull(igPostMetadata) ? igPostMetadata.getType() : null);
		} else if(StringUtils.isNotEmpty(metadata.getFbPostMetadata())) {
			FbPostMetadata fbPostMetadata = JSONUtils.fromJSON(metadata.getFbPostMetadata(), FbPostMetadata.class);
			socialDraftPostResponse.setType(Objects.nonNull(fbPostMetadata) ? fbPostMetadata.getType() : null);
		}
		if(StringUtils.isNotEmpty(metadata.getMediaSequence())){
			List<String> mediaSequence = JSONUtils.collectionFromJSON(metadata.getMediaSequence(),String.class);
			socialDraftPostResponse.setMediaSequence(getMediaUrl(mediaSequence,businessNumber));
		}
	}

	private static List<String> getMediaUrl(List<String> mediaUrl, Long businessNumber) {
		if (CollectionUtils.isEmpty(mediaUrl)) {
			return Collections.emptyList();
		}
		List<String> listOfUrls = new ArrayList<>();
		String cdnUrl = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getCdnImageBaseUrl();
		for (String url : mediaUrl) {
			String fullUrl;
			if(url.contains(SOCIAL_TEMPLATE)){
				fullUrl = cdnUrl + "/" + url;
			} else {
				fullUrl = cdnUrl + "/" + url;
//				fullUrl = cdnUrl + "/" + businessNumber + "/" + url;
			}
			listOfUrls.add(fullUrl);
		}
		return listOfUrls;
	}

	private static void checkForMasterPost(Map<Integer, SocialDraftChannelsDTO> mapOfMasterPostIdAndSocialChannel, Integer masterPostId,
										   SocialDraftPostResponse socialDraftPostResponse) {
		SocialDraftChannelsDTO socialDraftChannelsDTO = mapOfMasterPostIdAndSocialChannel.get(masterPostId);
		if(Objects.isNull(socialDraftChannelsDTO)){
			return;
		}
		socialDraftPostResponse.setIsValidDraft(socialDraftChannelsDTO.isValidDraft());
		socialDraftPostResponse.setPostingSites(new ArrayList<>(socialDraftChannelsDTO.getPostingSites()));
		socialDraftPostResponse.setIncompleteChannel(new ArrayList<>(socialDraftChannelsDTO.getInCompleteChannels()));
		socialDraftPostResponse.setHasAccess(CollectionUtils.isEmpty(socialDraftChannelsDTO.getInCompleteChannels()));
	}



	/**
	 {
            "_index": "mention",
            "_type": "analytics",
            "_id": "*********",
            "_score": null,
            "_routing": "237547",
            "_source": {
                "b_id": 237547,
                "e_id": 237547,
                "cmnt": "Back+when+I+was+thinking+of+buying+Yahoo+u00A0this+self-importantu00A0toothpaste+salesmanu00A0journalist+showhost+eyebrowu00A0raiser+interviewed+me+and+someu00A0guy+from+Crisco+Systemsu00A0together%2C+boy%2C+was+he+annoying%21+Iu2019m+pretty+good+at+being+evasiveu00A0but+this+guy+kept+onu2026",
                "r_date": "2018-06-29 11:08:06",
                "r_url": "https%3A%2F%2Fplus.google.com%2F109185071163935645937%2Fposts%2F6o3GBekedJz",
                "rtng": 0.0,
                "rwr": {
                    "id": ********,
                    "img": "https%3A%2F%2Flh3.googleusercontent.com%2F-XdUIqdMkCWA%2FAAAAAAAAAAI%2FAAAAAAAAAAA%2F4252rscbv5M%2Fphoto.jpg%3Fsz%3D50",
                    "profile": "109185071163935645937",
                    "first": null,
                    "last": null,
                    "nick": "stephanos+ballmerfeld",
                    "phone": null,
                    "email": null,
                    "foll_cnt": 110,
                    "post_cnt": 0,
                    "loc": null
                },
                "s_id": 2,
                "s_type": "mention",
                "shrd_on": [],
                "status": 4,
                "tag": [],
                "gnip_Id": [8971],
                "r_id": *********
            },
            "sort": [1530270486000]

	 */
	public static void convertPostLibDtoToEntity(SocialPostInputMessageRequest socialPost, PostLib postLib) throws JsonProcessingException {
		SocialPostSchedulerMetadata postMetadata = new SocialPostSchedulerMetadata();
		if (Objects.nonNull(socialPost)) {
			postLib.setPostText(socialPost.getPostText());
			if (CollectionUtils.isNotEmpty(socialPost.getLinks()) && org.apache.commons.lang3.StringUtils.isNotBlank(socialPost.getLinks().get(0))) {
				postLib.setLinkPreviewUrl(socialPost.getLinks().get(0));
			} else {
				postLib.setLinkPreviewUrl(null);
			}
			postLib.setCreatedBy(socialPost.getCreatedBy());

			if (CollectionUtils.isNotEmpty(socialPost.getMentions())) {
				postLib.setMentions(JSONUtils.toJSON(socialPost.getMentions()));
			} else {
				postLib.setMentions(null);
			}
			if (MapUtils.isNotEmpty(socialPost.getPostingSites()) && Objects.nonNull(socialPost.getPostingSites().get(SocialChannel.TWITTER.getName()))) {
				postMetadata.setLocationTagMetaData(JSONUtils.toJSON(socialPost.getPostingSites().get(SocialChannel.TWITTER.getName()).getLocationTagMetaData()));
			}
			postMetadata.setMediaSequence(socialPost.getMediaSequence());
			if (org.apache.commons.lang3.StringUtils.isNotEmpty(socialPost.getVideoThumbnailMetadata())) {
				postMetadata.setVideoThumbnailMetadata(socialPost.getVideoThumbnailMetadata());
			}
			if (MapUtils.isNotEmpty(socialPost.getPostingSites()) && Objects.nonNull(socialPost.getPostingSites().get("google"))) {
				postMetadata.setGmbPostMetaData(socialPost.getPostingSites().get("google").getGmbPostMetaData());
			}
			if (MapUtils.isNotEmpty(socialPost.getPostingSites()) && Objects.nonNull(socialPost.getPostingSites().get("linkedin"))) {
				postMetadata.setLinkedinPostMetaData(socialPost.getPostingSites().get("linkedin").getLinkedinPostMetaData());
			}
			if (MapUtils.isNotEmpty(socialPost.getPostingSites()) && Objects.nonNull(socialPost.getPostingSites().get(SocialChannel.YOUTUBE.getName()))) {
				postMetadata.setYoutubePostMetaData(JSONUtils.toJSON(socialPost.getPostingSites().get(SocialChannel.YOUTUBE.getName()).getYoutubeMetaDataRequest()));
			}
			if(Objects.nonNull(socialPost.getPostingSites().get(SocialChannel.TIKTOK.getName()))) {
				postMetadata.setTiktokPostMetaData(JSONUtils.toJSON(socialPost.getPostingSites().get(SocialChannel.TIKTOK.getName()).getTiktokPostMetaData()));
			}
			if (Objects.nonNull(socialPost.getTemplateData())) {
				postMetadata.setTemplateData(socialPost.getTemplateData());
			}
			if (Objects.nonNull(socialPost.getContentGenerated())) {
				postMetadata.setContentGenerated(socialPost.getContentGenerated());
			}
			if (Objects.nonNull(socialPost.getPostingSites().get(SocialChannel.INSTAGRAM.getName()))) {
				postMetadata.setIgPostMetadata(JSONUtils.toJSON(socialPost.getPostingSites().get(SocialChannel.INSTAGRAM.getName()).getIgPostMetadata()));
			}
			if (Objects.nonNull(socialPost.getPostingSites().get(SocialChannel.FACEBOOK.getName()))) {
				postMetadata.setFbPostMetadata(JSONUtils.toJSON(socialPost.getPostingSites().get(SocialChannel.FACEBOOK.getName()).getFbPostMetadata()));
			}
			if (Objects.nonNull(socialPost.getValidPreview())) {
				postMetadata.setValidPreview(socialPost.getValidPreview());
			}
			if (Objects.nonNull(socialPost.getPostingSites().get(SocialChannel.INSTAGRAM.getName()))
					&& CollectionUtils.isNotEmpty(socialPost.getPostingSites().get(SocialChannel.INSTAGRAM.getName()).getLinkInBioDetails())) {
				postMetadata.setLinkInBioDetails(JSONUtils.toJSON(socialPost.getPostingSites().get(SocialChannel.INSTAGRAM.getName()).getLinkInBioDetails()));
			}
			postLib.setPostMetadata(new ObjectMapper().writeValueAsString(postMetadata));
		}
		LOGGER.info("Social Post lib: social post lib object {}", postLib);
	}

	private static boolean isTaggedCheck(Set<SocialTagMappingOperationRequest> tagMappings) {
		for (SocialTagMappingOperationRequest tagMapping : tagMappings) {
			SocialTagOperation tagOperation = tagMapping.getOperation();
			if(tagOperation.equals(SocialTagOperation.CREATE)) {
				return CollectionUtils.isNotEmpty(tagMapping.getTagIds());
			}
		}
		return false;
	}

	public static void convertPostLibMasterDtoToEntity(SocialPostInputMessageRequest socialPost, PostLibMaster postLibMaster,
													   Set<SocialTagMappingOperationRequest> tagMappings) throws JsonProcessingException {
		SocialPostSchedulerMetadata postMetadata = new SocialPostSchedulerMetadata();
		if (Objects.nonNull(socialPost)) {
			postLibMaster.setEnterpriseId(socialPost.getBusinessId());
			postLibMaster.setApprovalWorkflowId(socialPost.getApprovalWorkflowId());
			postLibMaster.setPostText(socialPost.getPostText());
			if (CollectionUtils.isNotEmpty(socialPost.getLinks()) && StringUtils.isNotBlank(socialPost.getLinks().get(0))) {
				postLibMaster.setLinkPreviewUrl(socialPost.getLinks().get(0));
			} else {
				postLibMaster.setLinkPreviewUrl(null);
			}
			if(Objects.nonNull(socialPost.getCreatedBy())) {
				postLibMaster.setCreatedBy(socialPost.getCreatedBy());
			}
			postLibMaster.setEditedBy(socialPost.getEditedBy());
			postLibMaster.setLastEditedAt(new Date());
			postLibMaster.setApprovalWorkflowId(socialPost.getApprovalWorkflowId());
			if (MapUtils.isNotEmpty(socialPost.getPostingSites()) && Objects.nonNull(socialPost.getPostingSites().get(SocialChannel.TWITTER.getName()))) {
				postMetadata.setLocationTagMetaData(JSONUtils.toJSON(socialPost.getPostingSites().get(SocialChannel.TWITTER.getName()).getLocationTagMetaData()));
			}
			postMetadata.setMediaSequence(socialPost.getMediaSequence());

			if (Objects.nonNull(socialPost.getPostingSites().get("google"))) {
				postMetadata.setGmbPostMetaData(socialPost.getPostingSites().get("google").getGmbPostMetaData());
			}
			if (Objects.nonNull(socialPost.getPostingSites().get("linkedin"))) {
				postMetadata.setLinkedinPostMetaData(socialPost.getPostingSites().get("linkedin").getLinkedinPostMetaData());
			}
			if (Objects.nonNull(socialPost.getPostingSites().get(SocialChannel.YOUTUBE.getName()))) {
				postMetadata.setYoutubePostMetaData(JSONUtils.toJSON(socialPost.getPostingSites().get(SocialChannel.YOUTUBE.getName()).getYoutubeMetaDataRequest()));
			}
			if (Objects.nonNull(socialPost.getTemplateData())) {
				postMetadata.setTemplateData(socialPost.getTemplateData());
			}
			if (Objects.nonNull(socialPost.getContentGenerated())) {
				postMetadata.setContentGenerated(socialPost.getContentGenerated());
			}
			if (Objects.nonNull(socialPost.getPostingSites().get(SocialChannel.INSTAGRAM.getName()))) {
				postMetadata.setIgPostMetadata(JSONUtils.toJSON(socialPost.getPostingSites().get(SocialChannel.INSTAGRAM.getName()).getIgPostMetadata()));
			}
			if (Objects.nonNull(socialPost.getPostingSites().get(SocialChannel.FACEBOOK.getName()))) {
				postMetadata.setFbPostMetadata(JSONUtils.toJSON(socialPost.getPostingSites().get(SocialChannel.FACEBOOK.getName()).getFbPostMetadata()));
			}
			if(Objects.nonNull(socialPost.getPostingSites().get(SocialChannel.TIKTOK.getName()))) {
				postMetadata.setTiktokPostMetaData(JSONUtils.toJSON(socialPost.getPostingSites().get(SocialChannel.TIKTOK.getName()).getTiktokPostMetaData()));
			}
			postLibMaster.setPostMetadata(new ObjectMapper().writeValueAsString(postMetadata));
			if(CollectionUtils.isNotEmpty(tagMappings) && isTaggedCheck(tagMappings)) {
				postLibMaster.setIsTagged(1);
			} else {
				postLibMaster.setIsTagged(0);
			}
		}
		LOGGER.info("Social Post lib master: social post object {}", postLibMaster);
	}

    public static void createOrUpdateSocialMediaUploadRequest(SocialMediaUploadRequest socialMediaUploadRequest,
																				  MediaUploadRequest mediaInitiateRequest,
																				  MediaUploadStatusEnum mediaUploadStatusEnum,
																				  SocialRawPageDetail socialRawPageDetail) {
		if(Objects.isNull(socialMediaUploadRequest)) socialMediaUploadRequest = new SocialMediaUploadRequest();
		socialMediaUploadRequest.setUploadStatus(mediaUploadStatusEnum.name());
		socialMediaUploadRequest.setPageId(socialRawPageDetail.getPageId());
		socialMediaUploadRequest.setAssetId(mediaInitiateRequest.getAssetId());
		socialMediaUploadRequest.setAssetType(Constants.VIDEO);
		socialMediaUploadRequest.setPublishInfoId(mediaInitiateRequest.getPublishInfoId());
		socialMediaUploadRequest.setSourceId(mediaInitiateRequest.getSourceId());
		MediaUploadMetaData mediaUploadMetaData = getMediaUploadMetaData(mediaInitiateRequest);
		socialMediaUploadRequest.setMediaUploadMetaData(mediaUploadMetaData);
		socialMediaUploadRequest.setTotalPartsCount(mediaInitiateRequest.getTotalParts());
		socialMediaUploadRequest.setVideoId(mediaInitiateRequest.getMediaId());
    }

	public static MediaUploadMetaData getMediaUploadMetaData(MediaUploadRequest mediaInitiateRequest) {
		MediaUploadMetaData mediaUploadMetaData = new MediaUploadMetaData();
		mediaUploadMetaData.setCaptionUrl(mediaInitiateRequest.getCaptionUrl());
		mediaUploadMetaData.setUploadUrlsExpireAt(mediaInitiateRequest.getUrlsExpiresAt());
		mediaUploadMetaData.setThumbnailUrl(mediaInitiateRequest.getThumbnailUrl());
		mediaUploadMetaData.setMediaUploadUrl(mediaInitiateRequest.getMediaUploadUrl());
		return mediaUploadMetaData;
	}

    public static SocialMediaUploadInfo convertToSocialMediaInfoEntity(MediaUploadRequest mediaInitiateRequest,
                                                                       Integer requestId) {
		SocialMediaUploadInfo socialMediaUploadInfo = new SocialMediaUploadInfo();
		socialMediaUploadInfo.setPublishInfoId(mediaInitiateRequest.getPublishInfoId());
		socialMediaUploadInfo.setAssetId(mediaInitiateRequest.getAssetId());
		socialMediaUploadInfo.setUploadRequestId(requestId);
		return socialMediaUploadInfo;
	}

    public static SocialAssetChunkInfo convertToSocialAssetChunkInfo(MediaUploadChunkRequest mediaUploadChunkRequest, Integer assetId,
																	 Integer sourceId) {
		SocialAssetChunkInfo socialAssetChunkInfo = new SocialAssetChunkInfo();
		Integer sequenceId;
		socialAssetChunkInfo.setAssetId(assetId);
		socialAssetChunkInfo.setSourceId(sourceId);
		if (SocialChannel.TWITTER.getId() == sourceId) {
			sequenceId = mediaUploadChunkRequest.getPartNumber() - 1;
		} else {
			sequenceId = mediaUploadChunkRequest.getPartNumber();
		}
		socialAssetChunkInfo.setSequenceId(sequenceId);
		if(Objects.isNull(mediaUploadChunkRequest.getChunk()) && org.apache.commons.lang3.StringUtils.isEmpty(mediaUploadChunkRequest.getChunkUrl())){
			return socialAssetChunkInfo;
		}
		if(Objects.nonNull(mediaUploadChunkRequest.getChunk()) && Objects.nonNull(mediaUploadChunkRequest.getChunk().getData())) {
			ByteArrayMetaData byteArrayMetaData = new ByteArrayMetaData();
			byteArrayMetaData.setChunk(Base64.encodeBase64String(mediaUploadChunkRequest.getChunk().getData()));
			socialAssetChunkInfo.setByteArrayMetaData(byteArrayMetaData);
		}
		socialAssetChunkInfo.setChunkUrl(mediaUploadChunkRequest.getChunkUrl());
		return socialAssetChunkInfo;
    }

    public static BTPHeatMapData convertAiDataToBTPHeatMapEntity(SocialAIRequest socialAIRequest) {
		BTPHeatMapData btpHeatMapData = new BTPHeatMapData();
		btpHeatMapData.setBtpDataPointsMetaData(convertToBtpDataPointsMetaData(socialAIRequest.getDataPoints()));
		String channel = StringUtils.isNotEmpty(socialAIRequest.getChannel()) ?
				BTPChannel.valueOf(socialAIRequest.getChannel().toUpperCase()).name() :  BTPChannel.GLOBAL.name();
		btpHeatMapData.setChannel(channel);
		BTPCategory category = StringUtils.isEmpty(socialAIRequest.getTypeOfEvent()) ||
				BTPCategory.GLOBAL.name().equalsIgnoreCase(socialAIRequest.getTypeOfEvent()) ?
				BTPCategory.GLOBAL: BTPCategory.valueOf(socialAIRequest.getCategory().toUpperCase());
		btpHeatMapData.setCategory(category.name());
		String report = "engagement".equalsIgnoreCase(socialAIRequest.getReportType()) ?
				BTPReportType.INCREASE_ENGAGEMENT.name() : BTPReportType.BUILD_AWARENESS.name() ;
		btpHeatMapData.setReportType(report);
		String country = StringUtils.isEmpty(getCoutryByString(socialAIRequest.getCountry()))
				? BTPCountry.GLOBAL.name() : getCoutryByString(socialAIRequest.getCountry());
		btpHeatMapData.setCountry(country);
		Integer noOfWeeks = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getBTPSetWeekCount();
		btpHeatMapData.setWeekStartDate(DateUtils.addWeeks(socialAIRequest.getWeekStartDate(),noOfWeeks));
		btpHeatMapData.setWeekEndDate(DateUtils.addDays(btpHeatMapData.getWeekStartDate(),7));
		return btpHeatMapData;
    }

	private static BTPDataPointsMetaData convertToBtpDataPointsMetaData(Map<String, DayWiseDataPoints> dataPoints) {
		BTPDataPointsMetaData metaData = new BTPDataPointsMetaData();
		if(MapUtils.isEmpty(dataPoints)){
			LOGGER.info("Data points are null or empty");
			return metaData;
		}
		metaData.setDataPoints(convertToDataPoints(dataPoints));
		return metaData;
	}

	private static Map<String, BTPDataPointsMetaData.DayWiseDataPoints> convertToDataPoints(Map<String, DayWiseDataPoints> dataPoints) {
		Map<String, BTPDataPointsMetaData.DayWiseDataPoints> dayWiseDataPointsMap = new HashMap<>();
		dataPoints.forEach((key,value) -> {
			BTPDataPointsMetaData.DayWiseDataPoints dayWiseDataPoints = new BTPDataPointsMetaData.DayWiseDataPoints();
			dayWiseDataPoints.setMON(value.getMON());
			dayWiseDataPoints.setTUE(value.getTUE());
			dayWiseDataPoints.setWED(value.getWED());
			dayWiseDataPoints.setTHU(value.getTHU());
			dayWiseDataPoints.setFRI(value.getFRI());
			dayWiseDataPoints.setSAT(value.getSAT());
			dayWiseDataPoints.setSUN(value.getSUN());
			dayWiseDataPointsMap.put(key,dayWiseDataPoints);
		});
		return dayWiseDataPointsMap;
	}

	public static BTPDayWiseData convertAIDataToBTPDayWiseData(SocialAIRequest socialAIRequest) {
		BTPDayWiseData btpDayWiseData = new BTPDayWiseData();
		String channel = StringUtils.isNotEmpty(socialAIRequest.getChannel()) ?
				BTPChannel.valueOf(socialAIRequest.getChannel().toUpperCase()).name() : BTPChannel.GLOBAL.name();
		btpDayWiseData.setChannel(channel);
		BTPCategory category = StringUtils.isEmpty(socialAIRequest.getTypeOfEvent()) ||
				BTPCategory.GLOBAL.name().equalsIgnoreCase(socialAIRequest.getTypeOfEvent()) ?
				BTPCategory.GLOBAL: BTPCategory.valueOf(socialAIRequest.getCategory().toUpperCase());
		btpDayWiseData.setCategory(category.name());
		String report = "engagement".equalsIgnoreCase(socialAIRequest.getReportType()) ?
				BTPReportType.INCREASE_ENGAGEMENT.name() : BTPReportType.BUILD_AWARENESS.name() ;
		btpDayWiseData.setReportType(report);
		BTPCountry country = BTPCountry.valueOf(StringUtils.isNotEmpty(socialAIRequest.getCountry()) ?
				socialAIRequest.getCountry().toUpperCase() : BTPCountry.GLOBAL.name());
		btpDayWiseData.setCountry(country.name());
		return btpDayWiseData;
	}

	public static void convertToBTpPriority(BTPPriorityLookup btpPriorityLookup, BTPPriorityRequest btpPriorityRequest) {
		btpPriorityLookup.setCategory(btpPriorityRequest.getCategory());
		btpPriorityLookup.setCountry(btpPriorityRequest.getCountry());
		btpPriorityLookup.setPriority(btpPriorityRequest.getPriority());
		btpPriorityRequest.setReportType(btpPriorityLookup.getReportType());
		btpPriorityRequest.setChannel(btpPriorityRequest.getChannel());
	}
}
