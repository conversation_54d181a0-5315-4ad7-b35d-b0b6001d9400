package com.birdeye.social.service;

import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessGMBLocationRawRepository;
import com.birdeye.social.dao.BusinessGetPageOpenUrlReqRepo;
import com.birdeye.social.dao.BusinessGetPageReqRepo;
import com.birdeye.social.dto.*;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.response.CoreVOMResponse;
import com.birdeye.social.external.response.google.GMBLocationPageUrl;
import com.birdeye.social.external.response.google.GMBLocationState;
import com.birdeye.social.external.response.google.GMBPageLocation;
import com.birdeye.social.external.response.google.GMBPageLocationResponse;
import com.birdeye.social.external.service.IGoogleService;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.googleplus.IGMBService;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.gmb.GMBServiceArea;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.service.impl.GMBLocationDetailServiceImpl;
import com.birdeye.social.service.impl.GMBPageSpecification;
import com.birdeye.social.specification.GMBSpecification;
import com.birdeye.social.sro.GMBAccountSyncRequest;
import com.birdeye.social.sro.GoogleAuthToken;
import com.birdeye.social.utils.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import javax.validation.Valid;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.birdeye.social.constant.Constants.*;

@Service("googleMyBusinessPageServce")
public class GoogleMyBusinessPageServiceImpl extends SocialAccountSetupCommonService implements GoogleMyBusinessPageService {
	
	@Autowired
	private BusinessGMBLocationRawRepository	socialGmbRepo;

	@Autowired
	private GMBSpecification gmbSpecification;
	
	@Autowired
	private IGMBService							gmbService;
	
	@Autowired
	private BusinessGetPageReqRepo				businessGetReqRepo;

	@Autowired
	private IBusinessGetPageService businessGetPageService;

	@Autowired
	private IGoogleAccountService googleAccountService;

	@Autowired
	private BusinessGetPageOpenUrlReqRepo	    businessGetPageOpenUrlReqRepo;

	@Autowired
	private KafkaProducerService	producer;

	@Autowired
	private IRedisExternalService redisExternalService;
	
	@Autowired
	private NexusService	nexusService;

	@Autowired
	private IRedisLockService redisService;

	@Autowired
	private IBrokenIntegrationService brokenIntegrationService;

	@Autowired
	private IGoogleService googleService;

	@Autowired
	private CommonService commonService;

	@Autowired
	private IPermissionMappingService permissionMappingService;

	private static final String CACHE_VALUE = "google.pages";


	private static final Logger					LOGGER	= LoggerFactory.getLogger(GoogleMyBusinessPageServiceImpl.class);
	
	@Override
	public Map<String, BusinessGoogleMyBusinessLocation> getExistingGMBPages(List<String> locationIds) {
		List<BusinessGoogleMyBusinessLocation> pages = getGMBPagesByLocationIds(locationIds);
		Map<String, BusinessGoogleMyBusinessLocation> existingPages = pages.stream().collect(Collectors.toMap(p -> p.getLocationId(), p -> p, (x, y) -> x));
		return existingPages;
	}
	
	@Override
	public List<BusinessGoogleMyBusinessLocation> getGMBPagesByLocationIds(Collection<String> locationIds) {
		return socialGmbRepo.findByLocationIdIn(locationIds);
	}
	
	@Override
	public List<BusinessGoogleMyBusinessLocation> getGMBPagesByRefreshTokenId(Integer refreshToken) {
		return socialGmbRepo.findByRefreshTokenId(refreshToken);
	}
	
	@Override
	public void updateGMBPagesByLocationIds(List<String> locationIds, Long enterpriseId, Integer accountId) {
		List<BusinessGoogleMyBusinessLocation> gmbPages = getGMBPagesByLocationIds(locationIds);
		gmbPages.stream().forEach(page -> {
			page.setIsSelected(1);
			page.setEnterpriseId(enterpriseId);
			page.setShortAccountId(accountId);
			socialGmbRepo.saveAndFlush(page);
		});
	}
	@Override
	public void updateGMBPagesByLocationIdsForReseller(List<String> locationIds, Long resellerId) {
		List<BusinessGoogleMyBusinessLocation> gmbPages = getGMBPagesByLocationIds(locationIds);
		gmbPages.stream().forEach(page -> {
			page.setIsSelected(1);
			page.setResellerId(resellerId);
			socialGmbRepo.saveAndFlush(page);
		});
	}

	@Override
	public Page<BusinessGoogleMyBusinessLocation> connectedGmbPagesForAnReseller(Long resellerId, Integer isSelected ,Pageable page) {
		return socialGmbRepo.findByResellerIdAndIsSelected(resellerId, isSelected,page);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> connectedGmbPagesForAnEnterprise(Long enterpriseId) {
		return socialGmbRepo.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
	}
	
	@Async
	@Override
	public void saveNewGoogleMyBusinessPages(List<GoogleMyBusinessPagesDTO> myBusinessPages) {
		myBusinessPages.parallelStream().forEach(this::savePages);
	}
	
	private void savePages(GoogleMyBusinessPagesDTO page) {
		BusinessGoogleMyBusinessLocation businessPage = prepareBusinessGoogleMyBusinessPages(page);
		if (businessPage != null) {
			LOGGER.info("Saving GMB location {} for account {}", businessPage.getLocationId(), businessPage.getAccountId());
			saveGMBRowPage(businessPage);
		}
		
	}
	
	@Override
	public void saveGMBRowPage(BusinessGoogleMyBusinessLocation businessPage) {
		socialGmbRepo.saveAndFlush(businessPage);
	}
	
	@Override
	public void saveOrUpdateGMBRowPage(BusinessGoogleMyBusinessLocation businessLocation) {
		try {
			BusinessGoogleMyBusinessLocation existingLoc = socialGmbRepo.findGMBByLocationId(businessLocation.getLocationId());
			if (existingLoc == null || existingLoc.getEnterpriseId() == null) {
				// if location does not exists then save
				LOGGER.info("Saving the location {}", businessLocation.getLocationId());
				socialGmbRepo.saveAndFlush(businessLocation);
			} else {
				// if location already exists then save
				LOGGER.info("location {} is already mapped to enterprise {}", businessLocation.getLocationId(), existingLoc.getEnterpriseId());
			}
		} catch (Exception exe) {
			LOGGER.error("Error while saving the location {}   and error {}", businessLocation.getLocationId(), exe.getMessage());
		}
		
	}

	@Override
	public void saveOrUpdateGMBRowPage(List<BusinessGoogleMyBusinessLocation> businessLocation) {
		socialGmbRepo.save(businessLocation);
	}

	private BusinessGoogleMyBusinessLocation prepareBusinessGoogleMyBusinessPages(GoogleMyBusinessPagesDTO page) {
		BusinessGoogleMyBusinessLocation businessPage = null;
		if (page != null) {
			businessPage = new BusinessGoogleMyBusinessLocation();
			businessPage.setAccountId(page.getAccountId());
			businessPage.setAccountName(page.getAccountName());
			businessPage.setAccountStatus(page.getAccountStatus());
			businessPage.setAccountType(page.getAccountType());
			businessPage.setCoverImageUrl(page.getCoverImageUrl());
			businessPage.setGooglePlusId(page.getGooglePlusId());
			businessPage.setIsSelected(0);
			businessPage.setIsValid(1);
			businessPage.setIsVerified(page.getIsVerified());
			businessPage.setLocationId(page.getLocationId());
			businessPage.setLocationName(page.getLocationName());
			businessPage.setLocationState(page.getLocationState());
			businessPage.setLocationUrl(page.getLocationUrl());
			businessPage.setLocationMapUrl(page.getLocationMapUrl());
			businessPage.setPictureUrl(page.getPictureUrl());
			businessPage.setPlaceId(page.getPlaceId());
			businessPage.setPrimaryPhone(page.getPrimaryPhone());
			businessPage.setRefreshTokenId(page.getRefreshTokenId());
			businessPage.setSingleLineAddress(page.getSingleLineAddress());
			businessPage.setUserId(page.getUserId());
			businessPage.setUserName(businessPage.getUserName());
			businessPage.setWebsiteUrl(page.getWebsiteUrl());
			businessPage.setIsValid(isPageValid(businessPage) ? 1 : 0);
			if(StringUtils.isNotBlank(page.getScope()))
				businessPage.setPermissions(commonService.extractGMBPermission( page.getScope()));
		}
		return businessPage;
	}

	@Override
	public void pushToKafkaForValidity(String channel, String locationId) {
		ValidityRequestDTO validityRequestDTO = new ValidityRequestDTO();
		validityRequestDTO.setChannel(channel);
		validityRequestDTO.setLocationId(locationId);
		producer.sendObject(Constants.CHECK_VALIDITY,validityRequestDTO);
	}

	@Override
	public Page<BusinessGoogleMyBusinessLocation> getDisconnectedPages(Long resellerId,List<Integer> validType,Pageable page,Integer isSelected) {
		return socialGmbRepo.findByResellerIdAndValidityType(resellerId,validType,page,isSelected);
	}




	@Override
	public List<BusinessGoogleMyBusinessLocation> getGMBPagesByEnterpriseAndLocationIds(List<String> locationIds, Long enterpriseId) {
		return socialGmbRepo.findByEnterpriseIdAndLocationIdIn(enterpriseId, locationIds);
	}
	
	@Override
	public void deleteGMBLocation(List<String> locationIds) {
		int delete  = socialGmbRepo.deleteByLocationIdIn(locationIds);
		socialGmbRepo.flush();
		
	}
	
	@Override
	public List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsSelected(Long enterpriseId, int isSelected) {
		return socialGmbRepo.findByEnterpriseIdAndIsSelected(enterpriseId, isSelected);
	}

	@Override
	public Map<String, BusinessGoogleMyBusinessLocation> getGMBPagesMap(Long enterpriseId, int isSelected) {
		if ( enterpriseId != null ) {
			List<BusinessGoogleMyBusinessLocation> gmbPages = findByEnterpriseIdAndIsSelected(enterpriseId, isSelected);
			if ( CollectionUtils.isNotEmpty(gmbPages) ) {
				final Map<String, BusinessGoogleMyBusinessLocation> gmbPagesMap = gmbPages
						.stream()
						.collect(Collectors.toMap(BusinessGoogleMyBusinessLocation::getLocationId, page -> page, (x, y) -> x));
				return gmbPagesMap;
			}
		}
		return null;
	}

	@Override
	public int updateGMBPagesForRefreshToken(List<Integer> refreshTokenIds) {
		return socialGmbRepo.updateGMBPagesForRefreshToken(refreshTokenIds);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> getGMBPagesByLocationId(List<String> locationIds) {
		return socialGmbRepo.findByLocationIdIn(locationIds);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> getGMBPagesByLocationIdWithLimit(List<String> locationIds, Pageable page) {
		return socialGmbRepo.findAllByLocationIdIn(locationIds,page);
	}

	@Override
	public BusinessGMBNotificationDTO getGMBPagesByLocationId(String locationId) {
		return socialGmbRepo.getBusinessGMBNotificationByLocationId(locationId);
	}
	
	
	@Override
	public void updateGMBLocationIsValidStatus(String locationId, Integer isValid) {
		try {
			List<BusinessGoogleMyBusinessLocation> gmbPages = getGMBPagesByLocationIds(new ArrayList<>(Collections.singleton(locationId)));
			LOGGER.info("For GMB location {} existing GMB locations in DB {}", locationId, gmbPages != null ? gmbPages.size() : 0);
			gmbPages.stream().forEach(page -> {
				page.setIsValid(isValid);
				if(isValid.equals(0)) {
					brokenIntegrationService.pushValidIntegrationStatus(page.getEnterpriseId(),SocialChannel.GMB.getName(),page.getId(),isValid,page.getLocationId());
					commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(page), page.getUserId(),page.getBusinessId(), page.getEnterpriseId());
				}
				page.setUpdatedAt(new Date());
				socialGmbRepo.saveAndFlush(page);
			});
		} catch (Exception exe) {
			LOGGER.error("For location {} while updating the status {} error {} occurrs", locationId, isValid, exe.getMessage());
		}
		
	}

	@Override
	public void postFetchPageProcess(Map<String, GoogleMyBusinessPagesDTO> fetchedPagesMap,String businessGetPageRequestId,String accountId){
		Integer pageCount = 0;
		Integer totalCount = 0;
		Set<String> oldRequestIds = new HashSet<>();
		BusinessGetPageRequest request = businessGetPageService.findById(businessGetPageRequestId);
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(request.getEnterpriseId()));
		GoogleAccount googleAccount = null;
		List<GoogleAccount> googleAccountList = googleAccountService.findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(accountId,businessGetPageRequestId);
		if(CollectionUtils.isNotEmpty(googleAccountList)) {
			googleAccount = googleAccountList.get(0);
		}
		if(MapUtils.isNotEmpty(fetchedPagesMap)){
			List<BusinessGoogleMyBusinessLocation> pages = getGMBPagesByLocationIds(new ArrayList<>(fetchedPagesMap.keySet()));
			LOGGER.info("Found existing pages from total in account : {} {} {}", CollectionUtils.size(pages),fetchedPagesMap.keySet().size(),accountId);
			Map<String, BusinessGoogleMyBusinessLocation> existingPagesMap = pages.stream()
					.collect(Collectors.toMap(BusinessGoogleMyBusinessLocation::getLocationId, p -> p, (x, y) -> x));
			GoogleAccount finalGoogleAccount = googleAccount;
			pages.forEach(page -> {
				oldRequestIds.add(page.getRequestId());
				page.setRequestId(request.getId().toString());
				page.setUpdatedBy(request.getBirdeyeUserId());
				page.setGmbAccountId(finalGoogleAccount !=null? finalGoogleAccount.getId():null);
				LOGGER.info("Scope of page :{}",fetchedPagesMap.get(page.getLocationId()).getScope());
				updateExistingPageData(fetchedPagesMap,page,request.getEnterpriseId(),request.getEmail());
				commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(page),
						String.valueOf(request.getBirdeyeUserId()), page.getBusinessId(), page.getEnterpriseId());

			});
			socialGmbRepo.save(pages);
			pages.forEach(page->commonService.uploadPageImageToCDN(page));
			socialGmbRepo.flush();
			List<GoogleMyBusinessPagesDTO> newMyBusinessPages = filterNewPages(fetchedPagesMap, existingPagesMap);
			if (CollectionUtils.isNotEmpty(newMyBusinessPages)) {
				// Save to DB
				pageCount = newMyBusinessPages.size();
				totalCount = newMyBusinessPages.size();
				saveGMBLocations(newMyBusinessPages, request.getId().toString(), request.getBirdeyeUserId(),googleAccount!=null?googleAccount.getId():null,request.getEmail());
			}
			// Google messaging is deprecated
			/*
			List<String> placeIds = newMyBusinessPages.stream().map(GoogleMyBusinessPagesDTO::getPlaceId).collect(Collectors.toList());
			producer.sendWithKey(Constants.UPDATE_GMSG_LOCATION_STATE,request.getEnterpriseId().toString(),
					new UpdateLocationStateRequest(request.getEnterpriseId(),placeIds,false));

			 */
			if(CollectionUtils.isNotEmpty(pages))
				totalCount += pages.size();

		}else{
			LOGGER.info("GMB Setup no pages fetched for businessGetPageReq and account id :  {} {}",businessGetPageRequestId,accountId);
		}

		if(showAccount(request.getEnterpriseId())){
			request.setPageCount(pageCount);
			request.setTotalPages(totalCount);
		}else{
			request.setPageCount(request.getPageCount()!=null?request.getPageCount()+pageCount:pageCount);
			request.setTotalPages(request.getTotalPages()!=null?request.getTotalPages()+totalCount:totalCount);
		}
		LOGGER.info("GMB Setup check if all account data has been processed to close the request for businessGetPageReq and account id :  {} {}",businessGetPageRequestId,accountId);

		if(Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0){
			request.setStatus(Status.NO_PAGES_FOUND.getName());
				redisService.release(key);
		}else{
			request.setStatus(Status.FETCHED.getName());
		}
		pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),request.getStatus(), request.getEnterpriseId());
		businessGetPageService.saveAndFlush(request);

		if(CollectionUtils.isNotEmpty(oldRequestIds)) {// send event to check invalid get page requests and mark them as cancelled
			producer.sendObjectV1(VALIDATE_PAGE_REQUEST, new CheckInvalidGetPageState(Constants.GMB, oldRequestIds));
		}
	}

	public void postFetchPageProcessForReseller(Map<String, GoogleMyBusinessPagesDTO> fetchedPagesMap,String businessGetPageRequestId,String accountId){
		Integer pageCount = 0;
		Integer totalCount = 0;
		Set<String> oldRequestIds = new HashSet<>();
		BusinessGetPageRequest request = businessGetPageService.findById(businessGetPageRequestId);
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(request.getResellerId()));
		GoogleAccount googleAccount = null;
		List<GoogleAccount> googleAccountList = googleAccountService.findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(accountId,businessGetPageRequestId);
		if(CollectionUtils.isNotEmpty(googleAccountList)) {
			googleAccount = googleAccountList.get(0);
		}
		if(MapUtils.isNotEmpty(fetchedPagesMap)){
			List<BusinessGoogleMyBusinessLocation> pages = getGMBPagesByLocationIds(new ArrayList<>(fetchedPagesMap.keySet()));
			LOGGER.info("Found existing pages from total in account : {} {} {}", CollectionUtils.size(pages),fetchedPagesMap.keySet().size(),accountId);
			Map<String, BusinessGoogleMyBusinessLocation> existingPagesMap = pages.stream().collect(Collectors.toMap(p -> p.getLocationId(), p -> p, (x, y) -> x));
			GoogleAccount finalGoogleAccount = googleAccount;
			pages.forEach(page -> {
				oldRequestIds.add(page.getRequestId());
				page.setRequestId(request.getId().toString());
				if(Objects.isNull(page.getCreatedBy())) {
					page.setCreatedBy(request.getBirdeyeUserId());
				}
				page.setUpdatedBy(request.getBirdeyeUserId());
				page.setGmbAccountId(finalGoogleAccount !=null? finalGoogleAccount.getId():null);
				LOGGER.info("Scope of page :{}",fetchedPagesMap.get(page.getLocationId()).getScope());
				updateExistingPageData(fetchedPagesMap,page,request.getResellerId(),request.getEmail());
				commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(page),
						String.valueOf(request.getBirdeyeUserId()), page.getBusinessId(), page.getEnterpriseId());

			});
			socialGmbRepo.save(pages);
			pages.forEach(page->commonService.uploadPageImageToCDN(page));
			socialGmbRepo.flush();
			List<GoogleMyBusinessPagesDTO> newMyBusinessPages = filterNewPages(fetchedPagesMap, existingPagesMap);
			if (CollectionUtils.isNotEmpty(newMyBusinessPages)) {
				// Save to DB
				pageCount = newMyBusinessPages.size();
				totalCount = newMyBusinessPages.size();
				saveGMBLocations(newMyBusinessPages, request.getId().toString(), request.getBirdeyeUserId(),googleAccount!=null?googleAccount.getId():null,request.getEmail());
			}
			// TODO: 09/01/23 @Navroj -- not required as messaging is not supported on reseller level
//			List<String> placeIds = newMyBusinessPages.stream().map(GoogleMyBusinessPagesDTO::getPlaceId).collect(Collectors.toList());
//
//			producer.sendWithKey(Constants.UPDATE_GMSG_LOCATION_STATE,request.getResellerId().toString(),
//					new UpdateLocationStateRequest(null,placeIds));
			if(CollectionUtils.isNotEmpty(pages))
				totalCount += pages.size();
		}else{
			LOGGER.info("GMB Setup no pages fetched for businessGetPageReq and account id :  {} {}",businessGetPageRequestId,accountId);
		}

		if(showAccount(request.getResellerId())){
			request.setPageCount(pageCount);
			request.setTotalPages(totalCount);
		}else{
			request.setPageCount(request.getPageCount()!=null?request.getPageCount()+pageCount:pageCount);
			request.setTotalPages(request.getTotalPages()!=null?request.getTotalPages()+totalCount:totalCount);
		}
		LOGGER.info("GMB Setup check if all account data has been processed to close the request for businessGetPageReq and account id :  {} {}",businessGetPageRequestId,accountId);
		LOGGER.info("GMB Setup All account data has been processed for businessGetPageReq and account id :  {} {}",businessGetPageRequestId,accountId);
		if(Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0){
			request.setStatus(Status.NO_PAGES_FOUND.getName());
			redisService.release(key);
		}else{
			request.setStatus(Status.FETCHED.getName());
		}
		pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),request.getStatus(), request.getResellerId());
		businessGetPageService.saveAndFlush(request);
		if(CollectionUtils.isNotEmpty(oldRequestIds)) {// send event to check invalid get page requests and mark them as cancelled
			producer.sendObjectV1(VALIDATE_PAGE_REQUEST, new CheckInvalidGetPageState(Constants.GMB, oldRequestIds));
		}
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> getGMBPagesByResellerAndLocationIds(List<String> locationIds, Long resellerId) {
		return socialGmbRepo.findByResellerIdAndLocationIdIn(resellerId, locationIds);
	}

	@Override
	public List<Long> getEnterpriseIdByResellerId(Long parentId) {
		return socialGmbRepo.findEnterpriseIds(parentId);
	}

	@Override
	public Set<Integer> getDistinctAgentIdByResellerId(Long parentId) {
		return socialGmbRepo.findDistinctAgentIdByResellerId(parentId);
	}

	@Override
	public Set<Integer> getDistinctAgentIdByEnterpriseId(Long parentId) {
		return socialGmbRepo.findDistinctAgentIdByEnterpriseId(parentId);
	}

	@Override
	public void updateGoogleMyBusiness(Integer agentId, Long enterpriseId) {
		socialGmbRepo.updateAgentIdByEnterpriseId(agentId,enterpriseId);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> findByAgentIdAndIsValidAndIsSelected(Integer agentId, int isValid, int isSelected) {
		return socialGmbRepo.findByAgentIdAndIsValidAndIsSelected(agentId,isValid,isSelected);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> getGMBPagesByLocationIdsAndIsSelected(Collection<String> locationIds, Integer isSelected) {
		return socialGmbRepo.findByLocationIdInAndIsSelected(locationIds, isSelected);
	}




	@Override
	public List<String> getGMBLocationIdsByRequestId(Integer requestId) {
		LOGGER.info("Get locationIds for request Id :{}",requestId);
		return socialGmbRepo.findAllLocationIdsByRequestId(requestId.toString());
	}

	@Override
	public List<String> getGMBLocationIdsByLocationName(String searchStr, Integer requestId) {
		LOGGER.info("Get locationIds for request Id :{} and location name matched with",requestId, searchStr);
		return socialGmbRepo.findAllByGMBPageName(searchStr, searchStr, requestId.toString());
	}

	@Override
	public void getVoiceOfMerchant(String accessToken, BusinessGoogleMyBusinessLocation page,boolean isViaNotification) {
		Object response = googleService.getVoiceOfMerchant(page.getLocationId(), accessToken);
		LOGGER.info("Response from google : {}",response);
		ObjectMapper mapper = new ObjectMapper();
		JsonNode node = mapper.convertValue(response, JsonNode.class);
		GMBLocationState gmbLocationState = JSONUtils.fromJSON(page.getLocationState(), GMBLocationState.class);
		Objects.requireNonNull(gmbLocationState).setGetVoiceOfMerchant(JSONUtils.toJSON(response));
		for (Iterator<String> it = node.fieldNames(); it.hasNext(); ) {
			String s = it.next();
				switch (s){
					case Constants.VERIFY:
						gmbLocationState.setIsVerified(false);
						page.setIsValid(0);
						page.setIsVerified(0);
						break;

					case Constants.RESOLVE_OWNERSHIP_CONFLICT:
						gmbLocationState.setIsDuplicate(true);
						page.setIsValid(0);
						page.setIsVerified(0);
						break;

					case Constants.COMPLY_WITH_GUIDELINES:
						JsonNode reason = node.get(s);
						String res = reason.get(Constants.RECOMMENDED_REASON).toString();
						StringBuilder sb = new StringBuilder(res);
						sb.deleteCharAt(0);
						sb.deleteCharAt(sb.length() - 1);

						switch (sb.toString()) {
							case Constants.RECOMMENDATION_REASON_UNSPECIFIED:
								gmbLocationState.setIsSuspended(true);
								gmbLocationState.setIsDisabled(true);
								break;
							case Constants.BUSINESS_LOCATION_DISABLED:
								gmbLocationState.setIsDisabled(true);
								break;
							case Constants.BUSINESS_LOCATION_SUSPENDED:
								gmbLocationState.setIsSuspended(true);
								break;
						}
						page.setIsVerified(1);
						page.setIsValid(0);
						break;

					case Constants.WAIT_FOR_VOICE_OF_MERCHANT:
						gmbLocationState.setWaitForVoiceOfMerchant(true);
						page.setIsVerified(0);
						page.setIsValid(0);
						break;

					case Constants.HAS_VOICE_OF_MERCHANT:
						Objects.requireNonNull(gmbLocationState).setHasVoiceOfMerchant(true);
						page.setIsVerified(1);
						page.setIsValid(1);
						break;

				}
		}
		page.setLocationState(JSONUtils.toJSON(gmbLocationState));
		if(isViaNotification) {
			CoreVOMResponse coreVOMResponse = ConversionUtils.convertToCoreVomResponse(gmbLocationState,page);
			producer.sendObjectWithKeyV1(page.getLocationId(), KafkaTopicEnum.SOCIAL_VOM_EVENT_SEND.getName(), coreVOMResponse);
		}
	}

	// TODO: 09/01/23 @Navroj -- done
	@Override
	public List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsValidAndIsSelectedAndAgentIdAndBusinessIdIsNotNull(Long enterpriseId,Integer agentId) {
		return socialGmbRepo.findByEnterpriseIdAndIsValidAndIsSelectedAndAgentIdAndBusinessIdIsNotNull(enterpriseId,1,1,agentId);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsValidAndIsSelectedAndBusinessIdIsNotNull(Long enterpriseId) {
		return socialGmbRepo.findByEnterpriseIdAndIsValidAndIsSelectedAndBusinessIdIsNotNull(enterpriseId,1,1);
	}

	private void updateExistingPageData(Map<String, GoogleMyBusinessPagesDTO> pagesMap, BusinessGoogleMyBusinessLocation page,long enterpriseId,String email) {
		
		if(Objects.isNull(page.getEnterpriseId()) && Objects.isNull(page.getResellerId())){
			GoogleMyBusinessPagesDTO dto = pagesMap.get(page.getLocationId());
			LOGGER.info("GMB page received from Google {}", dto);
			page.setAccountId(dto.getAccountId());
			page.setAccountName(dto.getAccountName());
			page.setPlaceId(dto.getPlaceId());
			page.setSingleLineAddress(dto.getSingleLineAddress());
			page.setCoverImageUrl(dto.getCoverImageUrl());
			page.setGooglePlusId(dto.getGooglePlusId());
			page.setLocationMapUrl(dto.getLocationMapUrl());
			page.setPrimaryPhone(dto.getPrimaryPhone());
			page.setRefreshTokenId(dto.getRefreshTokenId());
			page.setWebsiteUrl(dto.getWebsiteUrl());
			page.setLocationState(dto.getLocationState());
			page.setIsValid(isPageValid(page) ? 1 : 0);
			page.setLocationUrl(dto.getLocationUrl());
			page.setLocationName(dto.getLocationName());
			page.setEmailId(email);
			if(StringUtils.isNotBlank(dto.getScope()))
				page.setPermissions(commonService.extractGMBPermission(dto.getScope()));
		}
	}

	public boolean isPageValid(BusinessGoogleMyBusinessLocation gmbPage) {
		if ( gmbPage != null && gmbPage.getLocationState() != null && !gmbPage.getLocationState().isEmpty() ) {
			GMBLocationState locationStateObj = JSONUtils.fromJSON(gmbPage.getLocationState(), GMBLocationState.class);
			if ( locationStateObj == null || (locationStateObj.getIsSuspended() != null && locationStateObj.getIsSuspended()) ||
					(locationStateObj.getIsDuplicate() != null && locationStateObj.getIsDuplicate()) ||
					(locationStateObj.getHasVoiceOfMerchant() != null && !(locationStateObj.getHasVoiceOfMerchant())) ||
					(locationStateObj.getIsDisabled() != null && locationStateObj.getIsDisabled()) ||
					gmbPage.getPlaceId() == null ) {
				return false;
			}
			return true;
		}
		return false;
	}
	
	@Override
	public void postFetchPageProcessForOpenUrl(Map<String, GoogleMyBusinessPagesDTO> pagesMap,String businessGetPageRequestId,String accountId){
		Integer pageCount = 0;
		Integer totalCount = 0;
		Set<String> oldRequestIds = new HashSet<>();
		BusinessGetPageOpenUrlRequest request = businessGetPageOpenUrlReqRepo.findOne(businessGetPageRequestId);
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(request.getFirebaseKey()));
		if(MapUtils.isNotEmpty(pagesMap)){
			List<BusinessGoogleMyBusinessLocation> pages = getGMBPagesByLocationIds(new ArrayList<>(pagesMap.keySet()));
			Map<String, BusinessGoogleMyBusinessLocation> existingPages = pages.stream().collect(Collectors.toMap(p -> p.getLocationId(), p -> p, (x, y) -> x));
			pages.forEach(page -> {
				oldRequestIds.add(page.getRequestId());
				page.setRequestId(request.getId());
				//page.setUpdatedBy(request.getSocialUserId());
			    updateExistingPageData(pagesMap,page,request.getEnterpriseId(),request.getEmail());
				commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(page), page.getUserId(), page.getBusinessId(), page.getEnterpriseId());
			});
			socialGmbRepo.save(pages);
			pages.forEach(page->commonService.uploadPageImageToCDN(page));
			socialGmbRepo.flush();
			List<GoogleMyBusinessPagesDTO> newMyBusinessPages = filterNewPages(pagesMap, existingPages);
			if (CollectionUtils.isNotEmpty(newMyBusinessPages)) {
				// Save to DB
				pageCount = newMyBusinessPages.size();
				totalCount = newMyBusinessPages.size();
				
				saveGMBLocations(newMyBusinessPages, request.getId(), null,null,null);
			}
			// Google messaging is deprecated
			/*
			List<String> placeIds = newMyBusinessPages.stream().map(GoogleMyBusinessPagesDTO::getPlaceId).collect(Collectors.toList());
			producer.sendWithKey(Constants.UPDATE_GMSG_LOCATION_STATE,request.getEnterpriseId().toString(),
					new UpdateLocationStateRequest(request.getEnterpriseId(),placeIds,false));
			 */
			if(CollectionUtils.isNotEmpty(pages))
				totalCount += pages.size();
		}else{
			LOGGER.info("no pages fetched for businessGetPageReq and account id :  {} {}",businessGetPageRequestId,accountId);
		}

		request.setPageCount(request.getPageCount()!=null?request.getPageCount()+pageCount:pageCount);
		request.setTotalPages(request.getTotalPages()!=null?request.getTotalPages()+totalCount:totalCount);
		LOGGER.info("check if all account data has been processed to close the request for businessGetPageReq and account id :  {} {}",businessGetPageRequestId,accountId);
		Boolean isEligible = redisExternalService.checkIfAllProcessedAfterCurrentForGMB(businessGetPageRequestId, accountId);
		if(isEligible){
			LOGGER.info("All account data has been processed for businessGetPageReq and account id :  {} {}",businessGetPageRequestId,accountId);
			if(Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0){
				request.setStatus(Status.NO_PAGES_FOUND.getName());
				nexusService.updateMapInFirebase("socialOpenUrl/"+ request.getFirebaseKey(),request.getFirebaseKey(),Status.NO_PAGES_FOUND.getName());
			}else{
			request.setStatus(Status.FETCHED.getName());
			nexusService.updateMapInFirebase("socialOpenUrl/"+ request.getFirebaseKey(),request.getFirebaseKey(),Status.FETCHED.getName());

			}			
			redisService.release(key);

		}
		businessGetPageOpenUrlReqRepo.saveAndFlush(request);
		if(CollectionUtils.isNotEmpty(oldRequestIds)) {// send event to check invalid get page requests and mark them as cancelled
			producer.sendObjectV1(VALIDATE_PAGE_REQUEST, new CheckInvalidGetPageState(Constants.GMB, oldRequestIds));
		}
	}

	@Override
	public void postFetchPageProcessForOpenUrlV1(Map<String, GoogleMyBusinessPagesDTO> pagesMap, String businessGetPageRequestId, String accountId) {
		Integer pageCount = 0;
		Integer totalCount = 0;
		Set<String> oldRequestIds = new HashSet<>();
		BusinessGetPageOpenUrlRequest request = businessGetPageOpenUrlReqRepo.findOne(businessGetPageRequestId);
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(request.getFirebaseKey()));
		GoogleAccount googleAccount = null;
		List<GoogleAccount> googleAccountList = googleAccountService.findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(accountId,businessGetPageRequestId);
		if(CollectionUtils.isNotEmpty(googleAccountList)) {
			googleAccount = googleAccountList.get(0);
		}
		if(MapUtils.isNotEmpty(pagesMap)){
			List<BusinessGoogleMyBusinessLocation> pages = getGMBPagesByLocationIds(new ArrayList<>(pagesMap.keySet()));
			Map<String, BusinessGoogleMyBusinessLocation> existingPages = pages.stream().collect(Collectors.toMap(p -> p.getLocationId(), p -> p, (x, y) -> x));
			GoogleAccount finalGoogleAccount = googleAccount;
			pages.forEach(page -> {
				oldRequestIds.add(page.getRequestId());
				page.setRequestId(request.getId());
				page.setGmbAccountId(finalGoogleAccount !=null? finalGoogleAccount.getId():null);
				//page.setUpdatedBy(request.getSocialUserId());
				updateExistingPageData(pagesMap,page,request.getEnterpriseId(),request.getEmail());
				commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(page), page.getUserId(), page.getBusinessId(), page.getEnterpriseId());
			});
			socialGmbRepo.save(pages);
			socialGmbRepo.flush();
			List<GoogleMyBusinessPagesDTO> newMyBusinessPages = filterNewPages(pagesMap, existingPages);
			if (CollectionUtils.isNotEmpty(newMyBusinessPages)) {
				// Save to DB
				pageCount = newMyBusinessPages.size();
				totalCount = newMyBusinessPages.size();

				saveGMBLocations(newMyBusinessPages, request.getId(), null,Objects.isNull(googleAccount)?null:googleAccount.getId(),null);
			}
			// Google messaging is deprecated
			/*
			List<String> placeIds = newMyBusinessPages.stream().map(GoogleMyBusinessPagesDTO::getPlaceId).collect(Collectors.toList());
			producer.sendWithKey(Constants.UPDATE_GMSG_LOCATION_STATE,request.getEnterpriseId().toString(),
					new UpdateLocationStateRequest(request.getEnterpriseId(),placeIds,false));
			 */
			if(CollectionUtils.isNotEmpty(pages))
				totalCount += pages.size();
		}else{
			LOGGER.info("no pages fetched for businessGetPageReq and account id :  {} {}",businessGetPageRequestId,accountId);
		}

		request.setPageCount(request.getPageCount()!=null?request.getPageCount()+pageCount:pageCount);
		request.setTotalPages(request.getTotalPages()!=null?request.getTotalPages()+totalCount:totalCount);
		LOGGER.info("check if all account data has been processed to close the request for businessGetPageReq and account id :  {} {}",businessGetPageRequestId,accountId);
		Boolean isEligible = redisExternalService.checkIfAllProcessedAfterCurrentForGMB(businessGetPageRequestId, accountId);
		LOGGER.info("All account data has been processed for businessGetPageReq and account id :  {} {}",businessGetPageRequestId,accountId);
		if(Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0){
			request.setStatus(Status.NO_PAGES_FOUND.getName());
			nexusService.updateMapInFirebase("socialOpenUrl/"+ request.getFirebaseKey(),request.getFirebaseKey(),Status.NO_PAGES_FOUND.getName());
		}else{
			request.setStatus(Status.FETCHED.getName());
			nexusService.updateMapInFirebase("socialOpenUrl/"+ request.getFirebaseKey(),request.getFirebaseKey(),Status.FETCHED.getName());

		}
		redisService.release(key);
		businessGetPageOpenUrlReqRepo.saveAndFlush(request);
		if(CollectionUtils.isNotEmpty(oldRequestIds)) {// send event to check invalid get page requests and mark them as cancelled
			producer.sendObjectV1(VALIDATE_PAGE_REQUEST, new CheckInvalidGetPageState(Constants.GMB, oldRequestIds));
		}
	}

	private List<GoogleMyBusinessPagesDTO> filterNewPages(Map<String, GoogleMyBusinessPagesDTO> pagesMap,
														  Map<String, BusinessGoogleMyBusinessLocation> existingPages) {
		List<GoogleMyBusinessPagesDTO> newMyBusinessPages = new ArrayList<>();
		pagesMap.values().stream().forEach(page -> {
			// new Pages
			if (existingPages.get(page.getLocationId()) == null) {
				newMyBusinessPages.add(page);
			}
		});
		return newMyBusinessPages;
	}

	@Override
	@Async
	public void fetchGmbAccounts(BusinessGetPageRequest businessGetPageRequest, GoogleAuthToken googleAuthToken,String type,Long parentAccountId) {
		LOGGER.info("Fetch gmb groups request for request id :{}",businessGetPageRequest.getId());
		Integer newAccountCount = 0;
		Integer totalCount = 0;
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat("_group_").concat(String.valueOf(parentAccountId));
		LOGGER.info("[Redis Lock] (Google Account) Lock acquired for account id : {}",parentAccountId);
		try{
			List<GMBAccount> gmbAccountList;
			if("db".equalsIgnoreCase(type)){
				List<GoogleAccount> googleAccounts = googleAccountService.findByUserAndActive(googleAuthToken.getEmail(),1);
				LOGGER.info("Google groups found for request id: {} and account: {}",businessGetPageRequest.getId(),googleAccounts);
				if(CollectionUtils.isNotEmpty(googleAccounts)){
					googleAccounts.forEach(googleAccount -> {
						googleAccount.setRefreshToken(googleAuthToken.getRefreshTokenId());
						googleAccount.setBusinessGetPageReqId(businessGetPageRequest.getId().toString());
					});
					googleAccountService.saveOrUpdate(googleAccounts);
				}
				totalCount = CollectionUtils.isNotEmpty(googleAccounts)?googleAccounts.size():0;
				LOGGER.info("Total count for gmb groups: {}",totalCount);
			}
			if(totalCount == 0 || totalCount == 1){
				LOGGER.info("Account contains {} group",totalCount);
				businessGetPageRequest.setType("API");
				gmbAccountList = gmbService.getAllGmbAccounts(googleAuthToken.getAccess_token());
				if(CollectionUtils.isNotEmpty(gmbAccountList)){
					LOGGER.info("List of google groups: {}",gmbAccountList);
					List<GMBAccountDTO> accounts = new ArrayList<>();
					gmbAccountList.stream().forEach(gmbAcc -> accounts.add(prepareAccountDTO(gmbAcc)));
					Map<String, GMBAccountDTO> newAccountsMap = accounts.stream().collect(Collectors.toMap(GMBAccountDTO :: getAccountId, Function.identity()));
					List<GoogleAccount> existingAccounts = googleAccountService.findByUser(googleAuthToken.getEmail());
					if(CollectionUtils.isNotEmpty(existingAccounts)){
						Map<String, GoogleAccount> existingAccountsMap = existingAccounts.stream().collect(Collectors.toMap(GoogleAccount :: getAccountId, Function.identity()));
						updateExistingAccountData(existingAccounts,businessGetPageRequest,newAccountsMap,googleAuthToken);
						googleAccountService.saveOrUpdate(existingAccounts);
						removeExistingAccountFromNew(accounts,existingAccountsMap);

						totalCount = (int)existingAccounts.stream().filter(googleAccount -> {
							if(googleAccount.getActive()==1){
								return true;
							}
							return false;
						}).count() + accounts.size();
					}else{
						totalCount = accounts.size();
					}
					newAccountCount = accounts.size();
					if(CollectionUtils.isNotEmpty(accounts)){
						Map<String,Object> params = new HashMap<>();
						params.put("requestId",businessGetPageRequest.getId());
						params.put("refreshToken",googleAuthToken.getRefreshTokenId());
						params.put("email",googleAuthToken.getEmail());
						List<GoogleAccount> googleAccounts = GoogleAccountUtil.covertDtoToEntity(accounts,params);
						googleAccountService.saveOrUpdate(googleAccounts);
					}
				}
			}else{
				businessGetPageRequest.setType("DB");
			}
			businessGetPageRequest.setTotalAccount(totalCount);
			businessGetPageRequest.setAccountCount(newAccountCount);
			businessGetPageRequest.setStatus(Status.ACCOUNT_FETCHED.getName());
			pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageRequest.getRequestType(),businessGetPageRequest.getStatus(),parentAccountId);
			businessGetPageService.save(businessGetPageRequest);
		}catch (Exception ex){
			LOGGER.error("[Redis Lock] (Google Account) Lock released for business with exception {} ", parentAccountId,ex);
			businessGetPageRequest.setType(type);
			businessGetPageRequest.setStatus(Status.CANCEL.getName());
			businessGetPageRequest.setErrorLog(ex.getMessage()!=null?ex.getMessage().substring(0,Math.min(ex.getMessage().length(), 4000)):null);
			businessGetPageService.save(businessGetPageRequest);
			pushCheckStatusInFirebase(SocialChannel.GMB.getName(),businessGetPageRequest.getRequestType(),Status.COMPLETE.getName(), parentAccountId,true);
		}finally {
			LOGGER.info("Lock release for key :{}",key);
			redisService.release(key);
		}
	}

	@Override
	public void fetchGmbAccountsForOpenUrl(BusinessGetPageOpenUrlRequest businessGetPageOpenUrlRequest, GoogleAuthToken googleAuthToken, String type, Long parentAccountId, String firebaseKey) {
		LOGGER.info("Fetch gmb groups request for open url request id :{}",businessGetPageOpenUrlRequest.getId());
		Integer newAccountCount = 0;
		Integer totalCount = 0;
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(firebaseKey));
		LOGGER.info("[Redis Lock] (Google Account) Lock acquired for account id : {}",parentAccountId);
		try{
			List<GMBAccount> gmbAccountList;
			if("db".equalsIgnoreCase(type)){
				List<GoogleAccount> googleAccounts = googleAccountService.findByUserAndActive(googleAuthToken.getEmail(),1);
				LOGGER.info("Google groups found for open url request id: {} and account: {}",businessGetPageOpenUrlRequest.getId(),googleAccounts);
				if(CollectionUtils.isNotEmpty(googleAccounts)){
					googleAccounts.forEach(googleAccount -> {
						googleAccount.setRefreshToken(googleAuthToken.getRefreshTokenId());
						googleAccount.setBusinessGetPageReqId(businessGetPageOpenUrlRequest.getId().toString());
					});
					googleAccountService.saveOrUpdate(googleAccounts);
				}
				totalCount = CollectionUtils.isNotEmpty(googleAccounts)?googleAccounts.size():0;
				LOGGER.info("Total count for gmb groups: {}",totalCount);
			}
			if(totalCount == 0 || totalCount == 1){
				LOGGER.info("Account contains {} group",totalCount);
				businessGetPageOpenUrlRequest.setType("API");
				gmbAccountList = gmbService.getAllGmbAccounts(googleAuthToken.getAccess_token());
				if(CollectionUtils.isNotEmpty(gmbAccountList)){
					LOGGER.info("List of google groups: {}",gmbAccountList);
					List<GMBAccountDTO> accounts = new ArrayList<>();
					gmbAccountList.stream().forEach(gmbAcc -> accounts.add(prepareAccountDTO(gmbAcc)));
					Map<String, GMBAccountDTO> newAccountsMap = accounts.stream().collect(Collectors.toMap(GMBAccountDTO :: getAccountId, Function.identity()));
					List<GoogleAccount> existingAccounts = googleAccountService.findByUser(googleAuthToken.getEmail());
					if(CollectionUtils.isNotEmpty(existingAccounts)){
						Map<String, GoogleAccount> existingAccountsMap = existingAccounts.stream().collect(Collectors.toMap(GoogleAccount :: getAccountId, Function.identity()));
						updateExistingAccountDataForOpenUrl(existingAccounts,businessGetPageOpenUrlRequest,newAccountsMap,googleAuthToken);
						googleAccountService.saveOrUpdate(existingAccounts);
						removeExistingAccountFromNew(accounts,existingAccountsMap);

						totalCount = (int)existingAccounts.stream().filter(googleAccount -> {
							if(googleAccount.getActive()==1){
								return true;
							}
							return false;
						}).count() + accounts.size();
					}else{
						totalCount = accounts.size();
					}
					newAccountCount = accounts.size();
					if(CollectionUtils.isNotEmpty(accounts)){
						Map<String,Object> params = new HashMap<>();
						params.put("requestId",businessGetPageOpenUrlRequest.getId());
						params.put("refreshToken",googleAuthToken.getRefreshTokenId());
						params.put("email",googleAuthToken.getEmail());
						List<GoogleAccount> googleAccounts = GoogleAccountUtil.covertDtoToEntity(accounts,params);
						googleAccountService.saveOrUpdate(googleAccounts);
					}
				}
			}else{
				businessGetPageOpenUrlRequest.setType("DB");
			}
			businessGetPageOpenUrlRequest.setTotalAccount(totalCount);
			businessGetPageOpenUrlRequest.setAccountCount(newAccountCount);
			businessGetPageOpenUrlRequest.setStatus(Status.ACCOUNT_FETCHED.getName());
			//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageOpenUrlRequest.getRequestType(),businessGetPageOpenUrlRequest.getStatus(),parentAccountId);
			nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,businessGetPageOpenUrlRequest.getStatus());
			businessGetPageOpenUrlReqRepo.save(businessGetPageOpenUrlRequest);
		}catch (Exception ex){
			LOGGER.error("[Redis Lock] (Google Account) Lock released for business with exception {} {}", parentAccountId,ex);
			businessGetPageOpenUrlRequest.setType(type);
			businessGetPageOpenUrlRequest.setStatus(Status.CANCEL.getName());
			Integer logLength = ex.getMessage().length()>=4000?4000:ex.getMessage().length();
			businessGetPageOpenUrlRequest.setErrorLog(ex.getMessage()!=null?ex.getMessage().substring(0,logLength):null);
			businessGetPageOpenUrlReqRepo.save(businessGetPageOpenUrlRequest);
			//pushCheckStatusInFirebase(SocialChannel.GMB.getName(),businessGetPageOpenUrlRequest.getRequestType(),Status.COMPLETE.getName(), parentAccountId,true);
			nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,Status.CANCEL.getName());
		}finally {
			LOGGER.info("Lock release for key :{}",key);
			redisService.release(key);
		}
	}

	@Override
	public List<GMBAccountDTO> fetchGmbAccountForFreemium(BusinessGetPageRequest businessGetPageRequest, GoogleAuthToken googleAuthToken){
		List<GMBAccount> gmbAccountList;
		List<GMBAccountDTO> accounts = new ArrayList<>();
		gmbAccountList = gmbService.getAllGmbAccounts(googleAuthToken.getAccess_token());
		if(CollectionUtils.isNotEmpty(gmbAccountList)) {
			LOGGER.info("List of google groups: {}", gmbAccountList);
			gmbAccountList.stream().forEach(gmbAcc -> accounts.add(prepareAccountDTO(gmbAcc)));
//			if(CollectionUtils.isNotEmpty(accounts)){
//				Map<String,Object> params = new HashMap<>();
//				params.put("requestId",businessGetPageRequest.getId());
//				params.put("refreshToken",googleAuthToken.getRefreshTokenId());
//				params.put("email",googleAuthToken.getEmail());
//				List<GoogleAccount> googleAccounts = GoogleAccountUtil.covertDtoToEntity(accounts,params);
//				googleAccountService.saveOrUpdate(googleAccounts);
//			}
			businessGetPageRequest.setTotalAccount(gmbAccountList.size());
			businessGetPageRequest.setAccountCount(gmbAccountList.size());
			businessGetPageRequest.setStatus(Status.ACCOUNT_FETCHED.getName());
			//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageRequest.getRequestType(),businessGetPageRequest.getStatus(),parentAccountId);
//			businessGetPageService.save(businessGetPageRequest);
		}
		return accounts;
	}

	private void updateExistingAccountData(List<GoogleAccount> existingAccounts, BusinessGetPageRequest businessGetPageRequest, Map<String, GMBAccountDTO> newAccountsMap, GoogleAuthToken googleAuthToken) {
		existingAccounts.forEach(googleAccount -> {
			GMBAccountDTO gmbAccountDTO = newAccountsMap.get(googleAccount.getAccountId());
			if(Objects.nonNull(gmbAccountDTO)){
				googleAccount.setBusinessGetPageReqId(businessGetPageRequest.getId().toString());
				googleAccount.setAccountType(gmbAccountDTO.getType());
				googleAccount.setAccountName(gmbAccountDTO.getAccountName());
				googleAccount.setRefreshToken(googleAuthToken.getRefreshTokenId());
				googleAccount.setActive(1);
			}else{
				googleAccount.setActive(0);
			}
		});
	}

	private void updateExistingAccountDataForOpenUrl(List<GoogleAccount> existingAccounts, BusinessGetPageOpenUrlRequest businessGetPageOpenUrlRequest, Map<String, GMBAccountDTO> newAccountsMap, GoogleAuthToken googleAuthToken) {
		existingAccounts.forEach(googleAccount -> {
			GMBAccountDTO gmbAccountDTO = newAccountsMap.get(googleAccount.getAccountId());
			if(Objects.nonNull(gmbAccountDTO)){
				googleAccount.setBusinessGetPageReqId(businessGetPageOpenUrlRequest.getId().toString());
				googleAccount.setAccountType(gmbAccountDTO.getType());
				googleAccount.setAccountName(gmbAccountDTO.getAccountName());
				googleAccount.setRefreshToken(googleAuthToken.getRefreshTokenId());
				googleAccount.setActive(1);
			}else{
				googleAccount.setActive(0);
			}
		});
	}

	private void removeExistingAccountFromNew(List<GMBAccountDTO> accounts,Map<String, GoogleAccount> existingAccountsMap){
		Iterator<GMBAccountDTO> itr = accounts.iterator();
		while (itr.hasNext()){
			GMBAccountDTO gmbAccountDTO = itr.next();
			if(existingAccountsMap.get(gmbAccountDTO.getAccountId()) !=null){
				itr.remove();
			}
		}
	}

	@Async
	@Override
	public void fetchGmbLocations(BusinessGetPageRequest request, GoogleAuthToken token) {
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(request.getEnterpriseId()));
		try {
			submitConnectFetchPageRequest(token, request);
		} catch (Exception exe) {
			LOGGER.error("Error while getting GMB locations {}", exe.getLocalizedMessage());
			BusinessGetPageRequest businessGetPageRequest = businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(request.getEnterpriseId(),SocialChannel.GMB.getName(), "connect");
			redisService.release(key);
			if (businessGetPageRequest != null && businessGetPageRequest.getStatus().equalsIgnoreCase(Status.INITIAL.getName())) {
				businessGetPageRequest.setStatus(Status.CANCEL.getName());
				businessGetPageService.saveAndFlush(businessGetPageRequest);
			}
		}
	}

	public void submitConnectFetchPageRequest(GoogleAuthToken gmbAuth, BusinessGetPageRequest request) {
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(request.getEnterpriseId()));
		try {
			List<GMBAccount> gmbAccountList = gmbService.getAllGmbAccounts(gmbAuth.getAccess_token());
			if (CollectionUtils.isNotEmpty(gmbAccountList)) {
				List<GMBAccountDTO> accounts = new ArrayList<>();
				gmbAccountList.stream().forEach(gmbacc -> accounts.add(prepareAccountDTO(gmbacc)));
				LOGGER.info("[GMB Setup] GMB Accounts for refresh token: {}, are: {}", gmbAuth.getRefresh_token(), gmbAccountList);
				saveToRedisByBusinessGetPageRequestId(accounts,request.getId().toString());
				for (GMBAccountDTO gmbAccount : accounts) {
					GMBAccountSyncRequest gmbAccountCheckRequest = new GMBAccountSyncRequest();
					gmbAccountCheckRequest.setGmbAccount(gmbAccount);
					gmbAccountCheckRequest.setGoogleAuthToken(gmbAuth);
					gmbAccountCheckRequest.setBusinessGetPageRequestId(request.getId().toString());
					producer.sendWithKey(Constants.GMB_ACCOUNT_FETCH_PAGE,gmbAccount.getAccountId(), gmbAccountCheckRequest);
					LOGGER.info("[GMB Setup] GMB Account data pushed to kafka for businessGetpageReqId and account id  {} {}",gmbAccount.getAccountId(),request.getId());
				}
			}else{
				LOGGER.info("No account received from gmb for businessId and requestId {} {}",request.getEnterpriseId(),request.getId());
				redisService.release(key);
				request.setStatus(Status.CANCEL.getName());
				request.setErrorLog("No account received from gmb");
				businessGetPageService.saveAndFlush(request);
				pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),Status.COMPLETE.getName(), request.getEnterpriseId());
			}
		} catch (Exception exe) {
			redisService.release(key);
			request.setStatus(Status.CANCEL.getName());
			request.setErrorLog("No account received from gmb.");
			businessGetPageService.saveAndFlush(request);
			LOGGER.error("Error while pushing GMB account location data", exe.getLocalizedMessage());
			pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),Status.COMPLETE.getName(),request.getEnterpriseId(),true);
		}
	}
    
	public Map<String, GoogleMyBusinessPagesDTO> submitFetchPageRequestForOpenURL(GoogleAuthToken gmbAuth, String requestId) {
		Map<String, GoogleMyBusinessPagesDTO> pagesMap = new HashMap<>();
		try {
			List<GMBAccount> gmbAccountList = gmbService.getAllGmbAccounts(gmbAuth.getAccess_token());
			if (CollectionUtils.isNotEmpty(gmbAccountList)) {
				List<GMBAccountDTO> accounts = new ArrayList<>();
				gmbAccountList.stream().forEach(gmbacc -> accounts.add(prepareAccountDTO(gmbacc)));
				LOGGER.info("[GMB Setup] GMB Accounts for refresh token: {}, are: {}", gmbAuth.getRefresh_token(), gmbAccountList);
				saveToRedisByBusinessGetPageRequestId(accounts,requestId);
				for (GMBAccountDTO gmbAccount : accounts) {
					GMBAccountSyncRequest gmbAccountCheckRequest = new GMBAccountSyncRequest();
					gmbAccountCheckRequest.setGmbAccount(gmbAccount);
					gmbAccountCheckRequest.setGoogleAuthToken(gmbAuth);
					gmbAccountCheckRequest.setBusinessGetPageRequestId(requestId);
					producer.sendWithKey(Constants.OPEN_URL_GMB_ACCOUNT_FETCH_PAGE,gmbAccount.getAccountId(), gmbAccountCheckRequest);
					LOGGER.info("[GMB Setup] GMB Account data pushed to kafka for businessGetpageReqId and account id  {} {}",gmbAccount.getAccountId(),requestId);
				}
			}
		} catch (Exception exe) {
			LOGGER.error("Error while pushing GMB account location data", exe.getLocalizedMessage());
		}
		return pagesMap;
	}
	
	public void saveToRedisByBusinessGetPageRequestId(List<GMBAccountDTO> gmbAccountList, String requestId) {
		Set<String> accountSet = gmbAccountList.stream().map(ba -> ba.getAccountId()).collect(Collectors.toSet());
		Boolean isSaved = redisExternalService.saveToRedisByBusinessGetPageRequestId(requestId,accountSet);
		if (isSaved){
			LOGGER.info("Account saved successfully to Redis for id:{}, size:{}",requestId,accountSet.size());
		}else {
			LOGGER.info("Exception while saving Account to Redis for id:{}",requestId);
		}
	}

	@Async
	@Override
	public void fetchGmbLocations(BusinessGetPageOpenUrlRequest request, GoogleAuthToken token) {
		try {		
			submitFetchPageRequestForOpenURL(token, request.getId()); 		
		} catch (Exception exe) {
			LOGGER.error("Error {} while getting GMB locations", exe.getLocalizedMessage());
		}
	}
	
	//TODO: Accounts should be processed in parallel.
	@Deprecated
	public Map<String, GoogleMyBusinessPagesDTO> getGoogleMyBusinessPagesDTO(GoogleAuthToken gmbAuth, Integer requestId) {
		Map<String, GoogleMyBusinessPagesDTO> pagesMap = new HashMap<>();
		try {
			List<GMBAccount> gmbAccountList = gmbService.getAllGmbAccounts(gmbAuth.getAccess_token());
			if (CollectionUtils.isNotEmpty(gmbAccountList)) {
				// prepare Account DTO
				List<GMBAccountDTO> accounts = new ArrayList<>();
				gmbAccountList.stream().forEach(gmbacc -> accounts.add(prepareAccountDTO(gmbacc)));
				LOGGER.info("[GMB Setup] GMB Accounts for refresh token: {}, are: {}", gmbAuth.getRefresh_token(), gmbAccountList);
				//GMBNotificationRequest notificationSubmit = new GMBNotificationRequest();
				List<String> accountIds = new ArrayList<>();
				for (GMBAccountDTO gmbAccount : accounts) {
					accountIds.add(gmbAccount.getAccountId());
					Map<String, GoogleMyBusinessPagesDTO> data = processAllGMBLocationsForAccount(gmbAccount, gmbAuth);
					if (data != null && !data.isEmpty()) {
						pagesMap.putAll(data);
					} else {
						LOGGER.error("Account : {} has no locations ", gmbAccount);
					}
				}
				//notificationSubmit.setAccountId(accountIds);
				//notificationSubmit.setAccessToken(gmbAuth.getAccess_token());
				//notificationSubmit.setRequestId(requestId.toString());

				//push to kafka - enable notification request
				//producer.sendObject(Constants.ENABLE_NOTIFICATION_TOPIC, notificationSubmit);
			}
		} catch (Exception exe) {
			LOGGER.error("Error {} while getting GMB locations", exe.getLocalizedMessage());
		}
		return pagesMap;
	}

	
	private GMBAccountDTO prepareAccountDTO(GMBAccount gmbaccnt) {
		GMBAccountDTO account = new GMBAccountDTO();
		account.setAccountId(CoreUtils.getAccountOrLocationId(gmbaccnt.getName(), 1));
		account.setName(gmbaccnt.getName());
		account.setAccountName(gmbaccnt.getAccountName());
		account.setStatus(gmbaccnt.getVerificationState() != null ? gmbaccnt.getVerificationState() : null);
		account.setType(gmbaccnt.getType());
		return account;
	}
	
	private GMBAccountDTO prepareAccountDTO(BusinessGoogleMyBusinessLocation gmbacc) {
		GMBAccountDTO account = new GMBAccountDTO();
		account.setAccountId(gmbacc.getAccountId());
		account.setName(String.format("accounts/%s",gmbacc.getAccountId()));
		account.setAccountName(gmbacc.getAccountName());
		account.setStatus(gmbacc.getAccountStatus());
		account.setType(gmbacc.getAccountType());
		return account;
	}
	
	public Map<String, GoogleMyBusinessPagesDTO> processAllGMBLocationsForAccount(GMBAccountDTO gmbAccount,
			GoogleAuthToken gmbAuth) {
		LOGGER.info("For GMB Account: {}, Fetching GMB locations", gmbAccount);
		String nextPageToken = null;
		Map<String, GoogleMyBusinessPagesDTO> gmbLocations = new HashMap<>();
		do {
			GMBPageLocationResponse locationResponse = gmbService.getGMBLocations(gmbAuth.getAccess_token(), gmbAccount.getName(), nextPageToken);
			if (Objects.isNull(locationResponse) || CollectionUtils.isEmpty(locationResponse.getLocations())) {
				// if we got no locations
				gmbLocations = null;
				break;
			}
			// prepare GMBPageLocation data
			gmbLocations.putAll(preapreGoogleMyBusinessPagesDTO(gmbAccount, locationResponse.getLocations(), gmbAuth));
			nextPageToken = locationResponse.getNextPageToken();
		} while (StringUtils.isNotBlank(nextPageToken));
		LOGGER.info("For GMB Account: {}, Total GMB locations: {}.", gmbAccount, (gmbLocations != null ? gmbLocations.size() : 0));
		return gmbLocations;
	}
	
	private Map<String, GoogleMyBusinessPagesDTO> preapreGoogleMyBusinessPagesDTO(GMBAccountDTO gmbAccount, List<GMBPageLocation> gmbLocations, GoogleAuthToken gmbAuth) {
		Map<String, GoogleMyBusinessPagesDTO> businessPages = new HashMap<>();
		gmbLocations.forEach(location -> {
			GoogleMyBusinessPagesDTO businessPage = prepareLocationData(location);
			businessPage.setAccountId(gmbAccount.getAccountId());
			businessPage.setAccountStatus(gmbAccount.getStatus());
			businessPage.setAccountType(gmbAccount.getType());
			businessPage.setAccountName(gmbAccount.getAccountName());
			businessPage.setUserId(gmbAuth.getUserId());
			businessPage.setRefreshTokenId(gmbAuth.getRefreshTokenId());
			businessPage.setScope(gmbAuth.getScope());
			businessPage.setLocationUrl(Constants.ACCOUNTS+gmbAccount.getAccountId()+"/"+location.getName());
			businessPage.setStorefrontAddress(location.getStorefrontAddress());
			businessPage.setRegularHours(location.getRegularHours());
			businessPage.setDescription(Objects.nonNull(location.getProfile())&&StringUtils.isNotEmpty(location.getProfile().getDescription())?
					location.getProfile().getDescription():null);
			businessPage.setCategories(location.getCategories());
			businessPages.put(businessPage.getLocationId(), businessPage);
		});
		return businessPages;
	}


	private GoogleMyBusinessPagesDTO prepareLocationData(GMBPageLocation location) {
		
		GoogleMyBusinessPagesDTO businessPage = new GoogleMyBusinessPagesDTO();
		businessPage.setLocationId(CoreUtils.getAccountOrLocationId(location.getName(), 1));
		businessPage.setLocationName(location.getTitle());
		businessPage.setWebsiteUrl(location.getWebsiteUri());
		businessPage.setPrimaryPhone(location.getPhoneNumbers().getPrimaryPhone());
		businessPage.setSingleLineAddress(location.getStorefrontAddress() != null ? location.getStorefrontAddress().getAddressInSingleLine() : null);
		businessPage.setPlaceId(location.getMetadata() != null ? location.getMetadata().getPlaceId() : null);
//		businessPage.setGooglePlusId(location.getLocationKey() != null ? location.getLocationKey().getPlusPageId() : null);
		businessPage.setLocationMapUrl(location.getMetadata() != null ? location.getMetadata().getMapsUri() : null);
		businessPage.setIsVerified(isLocationVerified(location.getMetadata()));
//		businessPage.setPictureUrl(location.getPhotos() != null ? location.getPhotos().getProfilePhotoUrl() : null);
//		businessPage.setCoverImageUrl(location.getPhotos() != null ? location.getPhotos().getCoverPhotoUrl() : null);

		businessPage.setLocationState(prepareStringFromJson(location.getMetadata()));
		businessPage.setServiceArea(prepareJsonFromString(location.getServiceArea()));
		return businessPage;
	}

	private String prepareJsonFromString(GMBServiceArea serviceArea){
		try {
			ObjectMapper mapper = new ObjectMapper();
			return mapper.writeValueAsString(serviceArea);
		}catch (Exception exe){
			LOGGER.error("Error {} occurs while preparing location state {}", exe.getLocalizedMessage(), serviceArea);
		}
		return null;
	}

	private int isLocationVerified(GMBLocationPageUrl state) {
		if (state != null && state.getHasVoiceOfMerchant() != null && state.getHasVoiceOfMerchant()) {
			return 1;
		}
		return 0;
	}
	private String prepareStringFromJson(GMBLocationPageUrl metaData) {
		GMBLocationState state = new GMBLocationState();
		try {
			ObjectMapper mapper = new ObjectMapper();
            GMBLocationDetailServiceImpl.createLocationState(metaData, state);
            return mapper.writeValueAsString(state);
		} catch (Exception exe) {
			LOGGER.error("Error {} occurs while preparing location state {}", exe.getLocalizedMessage(), state);
		}
		return null;
	}
	
	private void saveGMBLocations(List<GoogleMyBusinessPagesDTO> myBusinessPages, String requestId, Integer userId,Integer gmbAccountId,String email) {
		myBusinessPages.forEach(page -> saveLocation(page, requestId, userId,gmbAccountId,email));
	}
	
	private void saveLocation(GoogleMyBusinessPagesDTO page, String requestId, Integer userId,Integer gmbAccountId,String email) {
		BusinessGoogleMyBusinessLocation businessPage = prepareBusinessGoogleMyBusinessPages(page, requestId);
		businessPage.setCreatedBy(userId);
		businessPage.setUpdatedBy(userId);
		businessPage.setEmailId(email);
		businessPage.setGmbAccountId(gmbAccountId);
		if (businessPage != null) {
			LOGGER.info("Saving GMB location {} for account {}", businessPage.getLocationId(), businessPage.getAccountId());
			saveGMBRowPage(businessPage);
			commonService.uploadPageImageToCDN(businessPage);
		}
		LOGGER.info(String.format("Sending validity check for location {} from %s","saveLocation"),businessPage.getLocationId());
		pushToKafkaForValidity(Constants.GMB, businessPage.getLocationId());
		commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.ADD_PAGES.name(), Arrays.asList(businessPage),
				String.valueOf(userId), businessPage.getBusinessId(), businessPage.getEnterpriseId());

	}
	
	private BusinessGoogleMyBusinessLocation prepareBusinessGoogleMyBusinessPages(GoogleMyBusinessPagesDTO page, String requestId) {
		BusinessGoogleMyBusinessLocation businessPage = prepareBusinessGoogleMyBusinessPages(page);
		businessPage.setRequestId(requestId);
		return businessPage;
	}
	
	@Override
	public List<BusinessGoogleMyBusinessLocation> getGMBPagesByRequestId(String requestId) {
		return socialGmbRepo.findByRequestId(requestId);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> getGMBPagesByRequestIdAndGmbAccountId(String requestId,Integer accountId) {
		return socialGmbRepo.getGMBPagesByRequestIdAndGmbAccountId(requestId,accountId);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> getGMBPagesByRequestIdAndAccountId(String requestId,String accountId) {
		return socialGmbRepo.getGMBPagesByRequestIdAndAccountId(requestId,accountId);
	}



	@Override
	public List<BusinessGMBNotificationDTO> findDistinctAccountIdsByEnterpriseId(Long businessId, int isValid) {
		return socialGmbRepo.findDistinctAccountIdsByEnterpriseId(businessId,isValid);
	}

	@Override
	public List<BusinessGMBNotificationDTO> getAllDistinctAccountIds() {
		List<BusinessGMBNotificationDTO> businessGMBNotificationDTOList= socialGmbRepo.getDistinctAccountId();
		return businessGMBNotificationDTOList;
	}

	@Override
	public Boolean isAccountIdAvailableonOtherEnterprise(String accountId, Long businessId) {
		List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = socialGmbRepo.findByAccountIdExcludingEnterprise(accountId,businessId);
		return !CollectionUtils.isEmpty(businessGoogleMyBusinessLocations);

	}
	
	@Override
	public Boolean isAccountIdUsedForOtherLocation(String accountId, String locationId,int isValid) {
		List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = socialGmbRepo.findByAccountIdAndLocationIdNotAndIsValid(accountId, locationId, isValid);
		return !CollectionUtils.isEmpty(businessGoogleMyBusinessLocations);

	}
	
	@Override
	public List<Integer> fetchRefreshTokens(int count) {
		LOGGER.info(Constants.GMB_LOCATION_CHECK_JOB_PREFIX,"fetching executable tokens");

		List<Integer> executableRefreshTokenIdList = socialGmbRepo.findExecutableRefreshTokenIds(new PageRequest(0, count));
		return executableRefreshTokenIdList;
	}
	
	@Override
	public void updateSyncStatusForLocation(Integer refreshTokenId,String locationId,GMBLocationJobStatus locationCheckStatus) {
		 socialGmbRepo.updateGMBPageForLocation(refreshTokenId,locationId, locationCheckStatus);
		 return;
	}

	@Override
	@Transactional
	public void updateGMBLocationBatches(Integer refreshTokenId, List<String> locationIds, GMBLocationJobStatus gmbLocationJobStatus) {
		int batchSize = 100;
		
		IntStream.range(0, (locationIds.size() + batchSize-1) /batchSize)
        	.mapToObj(i -> locationIds.subList(i*batchSize, Math.min(locationIds.size(), (i+1)*batchSize)))
        	.forEach(batchOflocationIds -> {
        		LocalDateTime nextTime = LocalDateTime.now().plusHours(24);
        		socialGmbRepo.updateStatusNextSyncDateByLocationId(refreshTokenId, batchOflocationIds, gmbLocationJobStatus, Timestamp.valueOf(nextTime));
        	});
	}
	
	
	@Override
	@Transactional
	public void updateGMBAccountLocationBatches(Integer refreshTokenId, String accountId,
			GMBLocationJobStatus gmbLocationJobStatus) {
        List<String> locationIds = socialGmbRepo.findLocationIdByRefreshTokenIdAndAccountId(refreshTokenId,accountId);
        updateGMBLocationBatches(refreshTokenId,locationIds,gmbLocationJobStatus);
	}

	@Override
	public void updateMessagingEnabledForEnterpriseId(Long enterpriseId, Integer isMessengerEnabled) {
		socialGmbRepo.updateMessengerEnabledForEnterpriseId(enterpriseId, isMessengerEnabled);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> findLastUpdatedWithDistinctRefreshTokenId(Long enterpriseId, Integer limit) {
		return socialGmbRepo.findLastUpdatedWithDistinctRefreshTokenId(enterpriseId, 1, 1, limit);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> findLastUpdatedWithDistinctRefreshTokenIdWithAgentId(Integer agentId, Integer limit) {
		return socialGmbRepo.findLastUpdatedWithDistinctRefreshTokenIdWithAgentId(agentId, 1, 1, limit);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> findByEnterpriseIdAndIsValidAndIsSelected(Long enterpriseId) {
		return socialGmbRepo.findByEnterpriseIdAndIsValidAndIsSelected(enterpriseId, 1,1);
	}

	@Override
	public BusinessGoogleMyBusinessLocation findFirstByLocationId(String locationId) {
		return socialGmbRepo.findFirstByLocationId(locationId);
	}

	@Override
	public BusinessGoogleMyBusinessLocation findByBusinessId(Integer businessId) {
		return socialGmbRepo.findByBusinessId(businessId);
	}

	public List<BusinessGoogleMyBusinessLocation> findByBusinessIdIn(List<Integer> businessIds){
		return socialGmbRepo.findByBusinessIdIn(businessIds);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> findByAgentIds(Collection<Integer> agentId){
		return socialGmbRepo.findByAgentIds(agentId);
	}

	@Override
	public 	List<BusinessGoogleMyBusinessLocation> findByAgentId(Integer agentID){
		return socialGmbRepo.findByAgentId(agentID);
	}

	@Override
	public void updateAgentIds(Integer agentId){
		socialGmbRepo.updateAgentIdByBusinessIdsNull(agentId);
	}

	@Override
	public void updateAgentIdsByBusinessIds(Integer agentId,List<Integer> businessIds){
		socialGmbRepo.updateAgentIdByBusinessIdsIn(agentId,businessIds);
	}

	@Override
	public int addAgentIdToLocations(Integer agentId, List<Integer> businessIds) {
		LOGGER.info("Saving adding agentId {} for businessIds {}", agentId, businessIds);
		return socialGmbRepo.updateAgentIdByBusinessIds(agentId, businessIds);
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> findByBusinessIds(Collection<Integer> businessIds, Integer isValid, Integer isSelected) {
		return socialGmbRepo.findByBusinessIdInAndIsValidAndIsSelected(businessIds, isValid, isSelected);
	}
	@Override
	public List<BusinessGoogleMyBusinessLocation> findByBusinessIds(Integer accountId,Collection<Integer> businessIds , String gmsgLocationStatus) {
		return socialGmbRepo.findByShortAccountIdAndBusinessIdInAndGMsgLocationStatus(accountId,businessIds,gmsgLocationStatus);
	}

	@Override
	public PaginatedGMBResponse getGMBResellerPagesByRequestIdAndGmbAccountId(String requestId, Integer accountId, PageRequest pageRequest) {
		Page<BusinessGMBLocationRawRepository.RL> pages = socialGmbRepo.getGMBPagesByRequestIdAndGmbAccountIdLite(requestId,accountId,pageRequest);
		LOGGER.info("Size of gmb pages for request id: {} is :{}",requestId,pages.getSize());
		PaginatedGMBResponse gmbPages = getResult(pages.getContent(),pages.getTotalPages(),pages.getTotalElements());
		return gmbPages;
	}

	public PaginatedGMBResponse getResult(List<BusinessGMBLocationRawRepository.RL> pages,Integer pageSize,Long elements){
		LOGGER.info("Converting list to paginated response");
		List<BusinessGoogleMyBusinessLocation> listPages = new ArrayList<>();
		for (BusinessGMBLocationRawRepository.RL page : pages){
			BusinessGoogleMyBusinessLocation businessLocation = convertLiteToGMBObject(page);
			listPages.add(businessLocation);
		}
		PaginatedGMBResponse gmbPages = new PaginatedGMBResponse();
		gmbPages.setPages(listPages);
		gmbPages.setTotalPages(pageSize);
		gmbPages.setTotalElements(elements);
		return gmbPages;
	}

	public PaginatedGMBResponse getGMBConnectPages(String requestId,Long resellerId,PageRequest pageRequest) {
		Page<BusinessGMBLocationRawRepository.RL> pages = socialGmbRepo.findByResellerIdAndIsSelectedAndRequestIdAndIsAdded(resellerId, requestId,pageRequest);
		LOGGER.info("Size of gmb pages for request id: {} is :{}",requestId,pages.getSize());
		PaginatedGMBResponse gmbPages = getResult(pages.getContent(),pages.getTotalPages(),pages.getTotalElements());
		return gmbPages;
	}

	@Override
	public PaginatedGMBResponse findByRequestId(String requestId, PageRequest pageRequest) {
		Page<BusinessGMBLocationRawRepository.RL> pages = socialGmbRepo.getPagesByRequestId(requestId,pageRequest);
		LOGGER.info("Size of gmb pages for request id: {} is :{}",requestId,pages.getSize());
		PaginatedGMBResponse gmbPages = getResult(pages.getContent(),pages.getTotalPages(),pages.getTotalElements());
		return gmbPages;
	}

	@Override
	public PaginatedGMBResponse findByRequestIdAndSearchStr(String requestId, PageRequest pageRequest, String search) {
		Page<BusinessGoogleMyBusinessLocation> locations = searchObject(search,pageRequest,requestId);
		PaginatedGMBResponse paginatedGMBResponse = new PaginatedGMBResponse();
		if(Objects.nonNull(locations)){
			paginatedGMBResponse.setPages(locations.getContent());
			paginatedGMBResponse.setTotalPages(locations.getTotalPages());
			paginatedGMBResponse.setTotalElements(locations.getTotalElements());
		}
		return paginatedGMBResponse;
	}

	public Page<BusinessGoogleMyBusinessLocation> searchObject(String search, Pageable page,String requestId) {
		Specification<BusinessGoogleMyBusinessLocation> gmb =
				Specifications.where((gmbSpecification.hasLocationName(search))).
						or(gmbSpecification.hasSingleLineAddress(search)).
						and(gmbSpecification.hasRequestId(requestId));
		return socialGmbRepo.findAll(gmb,page);
	}

	private BusinessGoogleMyBusinessLocation convertLiteToGMBObject(BusinessGMBLocationRawRepository.RL page) {
		LOGGER.info("Converting gmb Lite object to BusinessGoogleMyBusinessLocation for location id :{}",page.getLocationId());
		BusinessGoogleMyBusinessLocation gmbPage = new BusinessGoogleMyBusinessLocation();
		if(Objects.nonNull(page)){
			gmbPage.setLocationId(page.getLocationId());
			gmbPage.setAccountName(page.getAccountName());
			gmbPage.setLocationName(page.getLocationName());
			gmbPage.setIsValid(page.getIsValid());
			gmbPage.setgMsgLocationComment(page.getGmsgComment());
			gmbPage.setPermissions(page.getPermissions());
			gmbPage.setgMsgLocationStatus(page.getGmsgStatus());
			gmbPage.setLocationState(page.getLocationState());
			gmbPage.setLocationMapUrl(page.getMapUrl());
			gmbPage.setIsSelected(page.getIsSelected());
			gmbPage.setSingleLineAddress(page.getSingleLineAddress());
			gmbPage.setAccountType(page.getAccountType());
		}
		return gmbPage;
	}


	@Override
	public List<BusinessGoogleMyBusinessLocation> getPagesByResellerId(Long resellerId) {
		List<BusinessGoogleMyBusinessLocation> gmbPages = new ArrayList<>();
		List<BusinessGMBLocationRawRepository.RL> pages = socialGmbRepo.getBusinessGoogleMyBusinessLocation(resellerId);
		for (BusinessGMBLocationRawRepository.RL page : pages) {
			BusinessGoogleMyBusinessLocation businessLocation = convertLiteToGMBObject(page);
			gmbPages.add(businessLocation);
		}
		return gmbPages;
	}

	@Cacheable(value = CACHE_VALUE, key = "#filterPage.socialId == null ?  #filterPage.channel +':'+ #filterPage.businessId.toString() + ':' + #filterPage.placeId  :  #filterPage.channel +':'+ #filterPage.socialId + ':' + #filterPage.placeId" ,unless = "#result == null")
	public SocialPageDTO getGMBPageDTO(@Valid FilterPageRequest filterPage) {
		Specification<BusinessGoogleMyBusinessLocation> specification = GMBPageSpecification.getGMBPage(filterPage);

		List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = socialGmbRepo.findAll(specification);

		if(Objects.isNull(businessGoogleMyBusinessLocations) || businessGoogleMyBusinessLocations.isEmpty())
			throw new BirdeyeSocialException(ErrorCodes.GMB_PAGE_NOT_FOUND,String.format("No page data found for %s",filterPage));

		SocialPageDTO socialPageDTO = GoogleBusinessUtils.getGMBLocationPageData(businessGoogleMyBusinessLocations.get(0));
		LOGGER.info("getGooglePageMapByFilter: Returning pageDTO{}", socialPageDTO);
		return socialPageDTO;
	}

	@Override
		public void clearGMBPageByFilter(FilterPageRequest filterPage) {
		LOGGER.info("BusinessGMBPage clearFbPageCache for filter {}", filterPage);
	}
}
