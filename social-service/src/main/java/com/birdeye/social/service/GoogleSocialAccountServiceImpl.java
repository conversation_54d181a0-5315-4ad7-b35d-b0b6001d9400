/**
 *
 */
package com.birdeye.social.service;

import com.birdeye.social.bam.BAMAggregationService;
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.*;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.exception.TooManyRequestException;
import com.birdeye.social.executor.services.ExecutorCommonService;
import com.birdeye.social.external.exception.ExternalAPIException;
import com.birdeye.social.external.request.business.BusinessLiteRequest;
import com.birdeye.social.external.request.google.EnableGMBNotificationRequest;
import com.birdeye.social.external.request.google.GMBNotificationResponse;
import com.birdeye.social.external.request.google.OpenInfo;
import com.birdeye.social.external.request.google.SocialAuditRequest;
import com.birdeye.social.external.request.nexus.CheckStatusRequest;
import com.birdeye.social.external.response.google.*;
import com.birdeye.social.external.service.IGoogleService;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.google.AgentLaunchState;
import com.birdeye.social.google.AgentVerificationState;
import com.birdeye.social.google.GoogleBusinessCommService;
import com.birdeye.social.googleplus.GooglePlusService;
import com.birdeye.social.googleplus.GoogleProfileResponse;
import com.birdeye.social.googleplus.IGMBService;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.gmb.GMBBusinessHour;
import com.birdeye.social.model.gmb.GMBPageCategories;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.model.usage.GoogleLocationResponseStatus;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.dao.*;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.BusinessGMBLocation;
import com.birdeye.social.specification.GMBSpecification;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.googlemessage.LocationBulkUnlaunchRequest;
import com.birdeye.social.utils.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.googleapis.json.GoogleJsonError;
import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.api.services.businesscommunications.v1.model.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.net.URI;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;
import static java.util.Comparator.nullsFirst;
import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 *
 */
@Service
public class GoogleSocialAccountServiceImpl extends SocialAccountSetupCommonService implements GoogleSocialAccountService {

	private static final String CONNECT = "connect";

	private static final String RECONNECT = "reconnect";

	private static final String AUTOMAPPING = "automapping/";

	private static final String LOCATIONLAUNCH = "locationLaunch";

	private static final String EXCEPTION_IN_GENERATING_ACCESS_TOKEN = "Exception in generating access token.";

	private static final String	TOPIC_INIT	= "auto-mapping-init";

	private static final String	TOPIC_MATCHED	= "auto-mapping-matched";

	private static final Logger					LOGGER	= LoggerFactory.getLogger(GoogleSocialAccountServiceImpl.class);

	private static final String TOPIC_LOCATION_SETUP = "init_setup_location";

	private static final String TOPIC_AGENT_UNLAUNCH = "init_unlaunch_agent";

	private static final String TOPIC_RESELLER_AGENT_LAUNCH = "init_reseller_agents_launch";

	private static final String GOOGLE_MESSAGES_LOCATION_ALREADY_CREATED = "A location with the same place id only exists under this agent";

	private static final String GOOGLE_MESSAGES_LOCATION_ALREADY_LAUNCHED = "Location is already launched";

	private static final String GOOGLE_MESSAGES_LOCATION_ALREADY_VERIFIED = "Location is already verified";
	private static final String CACHE_VALUE = "google.enterpriseId";

	@Autowired
	private GoogleAuthenticationService			googleAuthenticationService;

	@Autowired
	private AutoMappingService autoMappingService;

	@Autowired
	private CommonService						commonService;

	@Autowired
	private CacheService cacheService;

	@Autowired
	private GooglePlusService					googlePlusService;

	@Autowired
	private IGoogleService googleService;

	@Autowired
	private IBusinessCoreService businessCoreService;

	@Autowired
	private AutoMappingRepo autoMappingRepo;

	@Autowired
	private SocialPostGooglePlusService			socialPostGooglePlusService;

	@Autowired
	private GoogleRefreshTokenRepo	googleRefreshTokenRepo;

	@Autowired
	private BusinessRepository					businessRepo;

	@Autowired
	private LocationRepository 					locationRepo;

	@Autowired
	private BusinessUtilsService				businessUtilService;

	@Autowired
	private BusinessUserRepository				businessUserRepository;

	@Autowired
	private IBusinessGetPageService businessGetPageService;

	@Autowired
	private IGoogleAccountService googleAccountService;

	@Autowired
	private GoogleLocationService googleLocationService;

	@Autowired
	private IGMBService							gmbService;

	@Autowired
	private GoogleMyBusinessPageService googleMyBusinessPageService;

	@Autowired
	private SocialFreemiumRepository socialFreemiumRepository;

	@Autowired
	private BusinessGMBLocationRepository		businessGMBPageRepo;

	@Autowired
	private IPermissionMappingService permissionMappingService;

	@Autowired
	private GoogleAccessTokenCache gAccessToken;

	@Autowired
	private ExecutorCommonService				executorCommonService;

	@Autowired
	private BusinessGetPageReqRepo				businessGetPageReqRepo;

	@Autowired
	private BusinessGetPageOpenUrlReqRepo		businessGetPageOpenUrlReqRepo;

	@Autowired
	private GoogleMessagesAgentService googleMsgAgentService;

	@Autowired
	private IBusinessCachedService businessService;

	@Autowired
	private ISocialAppService socialAppService;

	@Autowired
	private NexusService	nexusService;

	@Autowired
	private IRedisLockService redisService;

	@Autowired
	private IRedisExternalService redisExternalService;

	@Autowired
	private KafkaProducerService	kafkaProducer;

	@Autowired
	private BAMAggregationService	bamAggregationService;

	@Autowired
	private GoogleAccessTokenCache googleAccessTokenCache;

	@Autowired
	private BusinessGMBLocationRawRepository socialGMBRepo;

	@Autowired
	private SocialPagesAuditRepo socialPagesAuditRepo;

	@Autowired
	private KafkaProducerService	producer;

	@Autowired
	private SocialSetupAuditRepository setupAuditRepo;

	@Autowired
	private GoogleBusinessCommService googleBizCommService;

	@Autowired
	private IBrokenIntegrationService brokenIntegrationService;

//	@Autowired
//	private GoogleService googleReviewsService;

	@Autowired
	private GMBSpecification gmbSpecification;

	@Autowired
	private IGMBService igmbService;

	@Autowired
	private GoogleMessagesAgentRepository agentRepo;

	@Autowired
	private ISocialModulePermissionService socialModulePermissionService;

	@Autowired
	private SocialBusinessPropertyService socialBusinessPropertyService;
	private static final String	GMB_NOTIFICATION_TOPIC	= "gmb.notification.topic";

	@Autowired
	private SocialErrorMessagePageService socialErrorMessageService;

	@Autowired
	private GoogleRedirectUrlRepo googleRedirectUrlRepo;
	
	@Autowired
	private IBusinessCoreService iBusinessCoreService;

	@Autowired
	private BusinessService coreBusinessService;

//	@Autowired
//	private GoogleSocialAccountService		googleSocialAccountService;

	@Deprecated //TODO "Remove if /{channel}/all is not required"
	public ChannelPageDetails getGMBPageInfo(List<BusinessGoogleMyBusinessLocation> gmbPages, Map<String, BusinessEntity> gmbToBusinessMap) {
		List<ChannelPages> pageInfo = new ArrayList<>();
		Boolean googleMessageEnabled= false;
		if(CollectionUtils.isNotEmpty(gmbPages) && gmbPages.get(0).getEnterpriseId() != null) {
			googleMessageEnabled = isGoogleMessageEnabled(null,gmbPages.get(0).getEnterpriseId());
		}
		Boolean finalGoogleMessageEnabled = googleMessageEnabled;
		/*List<Integer> refreshIds = gmbPages.stream().map(p ->p.getRefreshTokenId()).distinct()
				.collect(Collectors.toList());
		List<GoogleRefreshTokenSocial> refreshTokenList = googleRefreshTokenRepository.findByIdIn(refreshIds);
		Map<Integer,GoogleRefreshTokenSocial> refreshTokenMap = refreshTokenList.stream().collect(Collectors.toMap(p->p.getId(),p->p, (x, y) -> x));
		*/
		gmbPages.stream().forEach(page -> pageInfo.add(getPageInfo(page, gmbToBusinessMap.get(page.getLocationId()), finalGoogleMessageEnabled)));
		return sortInvalidAndValidPage(pageInfo);
	}

	@Override
	public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId) {
		List<BusinessGoogleMyBusinessLocation> gmbPages = googleMyBusinessPageService.connectedGmbPagesForAnEnterprise(enterpriseId);
		if ( gmbPages.isEmpty() ) {
			return ChannelSetupStatus.PageSetupStatus.NO_PAGES_FOUND;
		} else {
			for (BusinessGoogleMyBusinessLocation mappedGmbPage : gmbPages){
				if ( mappedGmbPage.getIsValid().equals(0) ) {
					return ChannelSetupStatus.PageSetupStatus.DISCONNECTED_PAGES_FOUND;
				}
			}
		}
		return ChannelSetupStatus.PageSetupStatus.OK;
	}

	@Override
	public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId) {
		List<BusinessGoogleMyBusinessLocation> gmbPages = googleMyBusinessPageService.connectedGmbPagesForAnEnterprise(enterpriseId);
		if ( gmbPages.isEmpty() ) {
			return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
		} else {
			for (BusinessGoogleMyBusinessLocation mappedGmbPage : gmbPages){
				if ( Objects.nonNull(mappedGmbPage.getBusinessId()) && mappedGmbPage.getIsValid().equals(1) ) {
					return ChannelSetupStatus.PageSetupStatus.VALID_INTEGRATION;
				}
			}
			return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
		}
	}

	@Override
	public BusinessIntegrationStatus.ChannelIntegrationInfo getPageIntegrationStatus(Integer businessId) {
		LOGGER.info("getPageIntegrationStatus {}", businessId);
		if (businessId != null) {
			// Get GMB mapping
			final BusinessGoogleMyBusinessLocation gmbPage = socialGMBRepo.findByBusinessId(businessId);
			if (Objects.isNull(gmbPage)) {
				return null;
			}
			// Prepare response
			BusinessIntegrationStatus.ChannelIntegrationInfo channelInfo = new BusinessIntegrationStatus.ChannelIntegrationInfo();
			channelInfo.setPageId(gmbPage.getLocationId());
			channelInfo.setValid(gmbPage.getIsValid());
			channelInfo.setPageName(gmbPage.getLocationName());

			// Get emailId from get page request table
			if (StringUtils.isNotEmpty(gmbPage.getRequestId())) {
				final String requestId = gmbPage.getRequestId();
				LOGGER.info("getPageIntegrationStatus: requestId {}", requestId);
				if (requestId.startsWith(OPEN_URL_REQUEST_PREFIX)) {
					final BusinessGetPageOpenUrlRequest openUrlRequest = businessGetPageOpenUrlReqRepo.findOne(requestId);
					if (openUrlRequest != null) {
						channelInfo.setEmail(openUrlRequest.getEmail());
					}
				} else {
					final BusinessGetPageRequest getPageRequest = businessGetPageReqRepo.findOne(Integer.valueOf(requestId));
					if (getPageRequest != null) {
						channelInfo.setEmail(getPageRequest.getEmail());
					}
				}
			}

			// Get firstName & lastName
			channelInfo.setUserName(gmbPage.getAccountName());
			channelInfo.setPageUrl(gmbPage.getLocationMapUrl());
			return channelInfo;
		}
		return null;
	}

	@Override
	public ConnectedPages getPages(Long enterpriseId, PageConnectionStatus pageConnectionStatus) {
		ConnectedPages connectedPage = new ConnectedPages();
		Map<String, ChannelPageDetails> pageTypes = new HashMap<>();
		List<BusinessGoogleMyBusinessLocation> connectedGmbPages = googleMyBusinessPageService.connectedGmbPagesForAnEnterprise(enterpriseId);
		LOGGER.info("getpages : Found {} pages", CollectionUtils.size(connectedGmbPages));
		ChannelPageDetails gmbAccountInfo = getGMBPageInfo(connectedGmbPages, pageConnectionStatus,enterpriseId);
		pageTypes.put(SocialChannel.GMB.getName(), gmbAccountInfo);
		connectedPage.setPageTypes(pageTypes);
		return connectedPage;
	}

	@Override
	public ConnectedPages getPagesForPostReconnect(Long enterpriseId, PageConnectionStatus pageConnectionStatus, SocialPostPageConnectRequest request) {
		ConnectedPages connectedPage = new ConnectedPages();
		Map<String, ChannelPageDetails> pageTypes = new HashMap<>();
		List<BusinessGoogleMyBusinessLocation> connectedGmbPages = googleMyBusinessPageService.getGMBPagesByLocationIdsAndIsSelected(request.getPageIds(), 1);

		LOGGER.info("getpages : Found {} pages", CollectionUtils.size(connectedGmbPages));
		ChannelPageDetails gmbAccountInfo = getGMBPageInfo(connectedGmbPages, pageConnectionStatus,enterpriseId);
		pageTypes.put(SocialChannel.GMB.getName(), gmbAccountInfo);
		connectedPage.setPageTypes(pageTypes);
		return connectedPage;
	}

	private boolean isPageInfoDisconnected(ChannelPages page) {
		return page.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName()) ||
				page.getValidType().equalsIgnoreCase(ValidTypeEnum.INVALID.getName());
	}


	public ChannelPageDetails getGMBPageInfo(List<BusinessGoogleMyBusinessLocation> gmbPages, PageConnectionStatus pageConnectionStatus,Long parentId) {
		List<ChannelPages> pageInfo = new ArrayList<>();
		Boolean googleMessageEnabled = false;
		if(CollectionUtils.isNotEmpty(gmbPages) && parentId != null) {
			googleMessageEnabled = isGoogleMessageEnabled(null,parentId);
		}
		Boolean finalGoogleMessageEnabled = googleMessageEnabled;
		gmbPages.stream().forEach(page -> {
			ChannelPages completePageInfo = getPageInfo(page, null, finalGoogleMessageEnabled);
			if ( pageConnectionStatus == PageConnectionStatus.CONNECTED && !isPageInfoDisconnected(completePageInfo) ) {
				pageInfo.add(completePageInfo);
			} else if ( pageConnectionStatus == PageConnectionStatus.DISCONNECTED && isPageInfoDisconnected(completePageInfo) ) {
				pageInfo.add(completePageInfo);
			} else if ( pageConnectionStatus == PageConnectionStatus.ALL ) {
				pageInfo.add(completePageInfo);
			}
		});
		return sortInvalidAndValidPage(pageInfo);
	}

	public ChannelPageDetails getGMBPageInfoForReseller(List<BusinessGoogleMyBusinessLocation> gmbPages,Long parentId) throws Exception {
		LOGGER.info("Details for mapped and unmapped pages for reseller id :{}",parentId);
		List<ChannelPages> pageInfo = new ArrayList<>();
		List<Integer> businessIds = new ArrayList<>();
		List<Integer> userIds = new ArrayList<>();
		gmbPages.forEach(x->{
			if(Objects.nonNull(x.getBusinessId())) businessIds.add(x.getBusinessId());
			if(Objects.nonNull(x.getCreatedBy())) userIds.add(x.getCreatedBy());
		});

		Map<String, Object> businessLocations = null;
		CompletableFuture<Map<String, Object>> businessLocationsFuture = CompletableFuture.supplyAsync(() -> {
			try {
				return businessCoreService.getBusinessesInBulkByBusinessIds(businessIds,true);
			} catch (Exception e) {
				LOGGER.info("exception while executing business location future, error: {}", e.getMessage());
				return new HashMap<>();
			}
		});
		CompletableFuture<Map<Integer, BusinessCoreUser>> userDetailsFuture = CompletableFuture.supplyAsync(() -> {
			try {
				return coreBusinessService.getBusinessUserForUserId(userIds);
			} catch (Exception e) {
				LOGGER.info("exception while executing user details future, error: {}", e.getMessage());
				return new HashMap<>();
			}
		});

		CompletableFuture<Void> allCompletableFuture = CompletableFuture.allOf(businessLocationsFuture, userDetailsFuture);
		allCompletableFuture.get(10, TimeUnit.SECONDS);
		businessLocations = businessLocationsFuture.get();
		Map<Integer, BusinessCoreUser> userIdVsInfoMap= userDetailsFuture.get();
		Map<String, Object> finalBusinessLocations = businessLocations;

		gmbPages.stream().forEach(page -> {
			BusinessLocationLiteEntity locationLite = null;
			if(Objects.nonNull(finalBusinessLocations) && Objects.nonNull(page.getBusinessId())){
				LOGGER.info("Prepare data for mapped page :{}",page);
				Map<String ,Object> locationData = (Map<String, Object>) finalBusinessLocations.get(page.getBusinessId().toString());
				locationLite = commonService.getMappedLocationInfo(locationData, page.getBusinessId(), page.getLocationName());
			}
			BusinessCoreUser userDetail = null;
			if(Objects.nonNull(page.getCreatedBy()) && MapUtils.isNotEmpty(userIdVsInfoMap) &&
					userIdVsInfoMap.containsKey(page.getCreatedBy())) {
				userDetail = userIdVsInfoMap.get(page.getCreatedBy());
			}
			ChannelPages completePageInfo = getPageInfoForReseller(page,null,locationLite, userDetail);
			pageInfo.add(completePageInfo);
		});
		return commonService.sortInvalidAndValidPage(pageInfo);
	}

	private String fetchInvalidTypeForPage(BusinessGoogleMyBusinessLocation page) {
		String invalidType = null;
		GMBLocationState locationState = JSONUtils.fromJSON(page.getLocationState(), GMBLocationState.class);
		if(Objects.nonNull(locationState)){
			if (locationState.getIsDuplicate() != null && locationState.getIsDuplicate() ){
				invalidType = "duplicate";
			} else if (locationState.getIsVerified() != null && !locationState.getIsVerified()) {
				invalidType = "unverified";
			} else if(locationState.getIsSuspended()!=null && locationState.getIsSuspended()){
				invalidType = "suspended";
			} else if(locationState.getIsDisabled() != null && locationState.getIsDisabled()){
				invalidType = "disabled";
			} else if(locationState.getWaitForVoiceOfMerchant()!=null && (locationState.getWaitForVoiceOfMerchant())) {
				invalidType = "pendingVerification";
			} else if (locationState.getHasVoiceOfMerchant() == null) {
				invalidType = "disconnected";
			} else {
				invalidType = "integration";
			}
		}
		return invalidType;
	}

	@Override
	public Validity fetchValidityAndErrorMessage(BusinessGoogleMyBusinessLocation page, Boolean googleMessageEnabled) {
		Validity validity = getPageValidity(page, googleMessageEnabled);
		if(validity.getErrorCode() != null) {
			PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndErrorCode(
					SocialChannel.GMB.getName(),validity.getErrorCode());
			validity.setErrorMessage(permissionMapping.getErrorMessage());
		}
		return validity;
	}
	private Validity fetchValidity(BusinessGoogleMyBusinessLocation page, Boolean googleMessageEnabled, Map<String, String> errorCodesMap) {
		Validity validity = getPageValidity(page, googleMessageEnabled);
		if(validity.getErrorCode() != null) {
			String errorMessage=errorCodesMap.containsKey(validity.getErrorCode())?errorCodesMap.get(validity.getErrorCode()):errorCodesMap.get(Constants.INTEGRATION);
			validity.setErrorMessage(errorMessage);
		}
		return validity;
	}

	private Validity getPageValidity(BusinessGoogleMyBusinessLocation page, Boolean googleMessageEnabled) {
		Validity validity = new Validity();
		if(page.getIsValid().equals(0)) {
			validity.setValidType(ValidTypeEnum.INVALID.getName());
			validity.setErrorCode(fetchInvalidTypeForPage(page));
		}
		/*else if(googleMessageEnabled) {
			if (page.getPermissions() == null || !page.getPermissions().contains(Constants.BUSINESS_MESSAGING)
					|| !page.getPermissions().contains(Constants.BUSINESS_COMMUNICATION)) {
				validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
				validity.setErrorCode(Constants.MESSENGER_NOT_ENABLED);
			} else if (page.getgMsgLocationStatus() != null && page.getgMsgLocationStatus().contains(Constants.ERROR)) {
				if (page.getgMsgLocationComment() != null && page.getgMsgLocationComment().contains(Constants.FOUND_ENABLED_LOCATION)) {
					validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
					validity.setErrorCode(Constants.LOCATION_ALREADY_LAUNCHED);
				} else if (page.getgMsgLocationComment() != null && page.getgMsgLocationComment().contains(Constants.INSUFFICIENT_AUTHENTICATION_SCOPE)) {
					validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
					validity.setErrorCode(Constants.INSUFFICIENT_SCOPE);
				} else {
					validity.setValidType(ValidTypeEnum.PARTIAL_VALID.getName());
					validity.setErrorCode(Constants.DEFAULT_MESSAGING);
				}
			} else {
				validity.setValidType(ValidTypeEnum.VALID.getName());
			}
		}*/
		else {
			validity.setValidType(ValidTypeEnum.VALID.getName());
		}
		return validity;
	}

	private ChannelPages getPageInfo(BusinessGoogleMyBusinessLocation page, BusinessEntity mappedBusiness,Boolean googleMessageEnabled) {
		ChannelPages pageInfo = new ChannelPages();
		pageInfo.setAddress(page.getSingleLineAddress());
		pageInfo.setId(page.getLocationId());
		pageInfo.setImage(page.getPictureUrl() != null ? page.getPictureUrl() : page.getCoverImageUrl());
		pageInfo.setPageName(page.getLocationName());
		pageInfo.setLink(page.getLocationMapUrl());

		if (mappedBusiness != null) {
			pageInfo.setLocationId(mappedBusiness.getId());
			pageInfo.setUserId(mappedBusiness.getEmailId());
			pageInfo.setLocationName(mappedBusiness.getAlias1() != null ? mappedBusiness.getAlias1() : mappedBusiness.getName());
		}
		Validity validity = fetchValidityAndErrorMessage(page, googleMessageEnabled);
		pageInfo.setValidType(validity.getValidType());
		pageInfo.setErrorCode(validity.getErrorCode());
		pageInfo.setErrorMessage(validity.getErrorMessage());
		return pageInfo;
	}
	private ChannelPages getPageInfoForReseller(BusinessGoogleMyBusinessLocation page,Boolean googleMessageEnabled,BusinessLocationLiteEntity businessLiteDTO, BusinessCoreUser userDetail) {
		LOGGER.info("Prepare ChannelPages for business id :{}",page.getBusinessId());
		ChannelPages pageInfo = new ChannelPages();
		pageInfo.setId(page.getLocationId());
		pageInfo.setAddress(page.getSingleLineAddress());
		pageInfo.setAddedBy(Objects.nonNull(userDetail)?businessCoreService.getFullUsername(userDetail):null);
		if( Objects.nonNull(businessLiteDTO)){
			pageInfo.setLocationId(businessLiteDTO.getId());
			pageInfo.setLocationAddress(commonService.prepareBusinessAddress(businessLiteDTO));
			pageInfo.setLocationName(businessLiteDTO.getAlias1() != null ? businessLiteDTO.getAlias1() : businessLiteDTO.getName());
			pageInfo.setParentName(businessLiteDTO.getAccountName());
		}
		pageInfo.setLink(page.getLocationMapUrl());
		pageInfo.setImage(page.getPictureUrl() != null ? page.getPictureUrl() : page.getCoverImageUrl());
		pageInfo.setPageName(page.getLocationName());
		pageInfo.setUserId(page.getEmailId());
		pageInfo.setLink(page.getLocationMapUrl());
		Validity validity = fetchValidityAndErrorMessage(page, googleMessageEnabled);
		pageInfo.setValidType(validity.getValidType());
		pageInfo.setErrorCode(validity.getErrorCode());
		pageInfo.setErrorMessage(validity.getErrorMessage());
		return pageInfo;
	}

	private ConsumerTokenAndSecret getAppKeyAndToken(Long businessId) throws Exception {
		Business business = businessRepo.findByBusinessId(businessId);
		return commonService.getAppKeyAndToken(business, SocialChannel.YOUTUBE.getName());
	}

	@Override
	public String getGoogleAuthUrl(Long businessId, Boolean redirectToSetup) throws Exception {
		ConsumerTokenAndSecret appCreds = getAppKeyAndToken(businessId);
		return googleAuthenticationService.getAuthenticationUrlForGoogle(appCreds.getDomainName(), appCreds.getToken(), redirectToSetup);
	}

	@Override
	public String getYoutubeAuthUrl(Long businessId, Boolean redirectToSetup,String domainName, String origin) throws Exception {
		ConsumerTokenAndSecret appCreds = getAppKeyAndToken(businessId);
		return googleAuthenticationService.getAuthenticationUrlForYoutube(domainName, appCreds.getToken(), origin,redirectToSetup, businessId);
	}

	private Map<String, GoogleMyBusinessPagesDTO> getGoogleMyBusinessPagesDTO(GoogleAuthToken gmbAuth, int pageSize, String requestId) {
		Map<String, GoogleMyBusinessPagesDTO> pagesMap = new HashMap<>();
		try {
			List<GMBAccount> gmbAccountList = gmbService.getAllGmbAccounts(gmbAuth.getAccess_token());
			if (CollectionUtils.isNotEmpty(gmbAccountList)) {
				// prepare Account DTO
				List<GMBAccountDTO> accounts = new ArrayList<>();
				gmbAccountList.stream().forEach(gmbacc -> accounts.add(prepareAccountDTO(gmbacc)));
				LOGGER.info("[GMB Setup] GMB Accounts for refresh token: {}, are: {}", gmbAuth.getRefresh_token(), gmbAccountList);
				//GMBNotificationRequest notificationSubmit = new GMBNotificationRequest();
				List<String> accountIds = new ArrayList<>();
				for (GMBAccountDTO gmbAccount : accounts) {
					accountIds.add(gmbAccount.getAccountId());
					Map<String, GoogleMyBusinessPagesDTO> data = processAllGMBLocationsForAccount(gmbAccount, gmbAuth, pageSize);
					if (data != null && !data.isEmpty()) {
						pagesMap.putAll(data);
					} else {
						LOGGER.error("Account : {} has no locations ", gmbAccount);
					}
				}
				//notificationSubmit.setAccountId(accountIds);
				//notificationSubmit.setAccessToken(gmbAuth.getAccess_token());
				//notificationSubmit.setRequestId(requestId);

				//push to kafka - enable notification request
				//producer.sendObject(Constants.ENABLE_NOTIFICATION_TOPIC, notificationSubmit);
			}
		} catch (Exception exe) {
			LOGGER.error("Error {} while getting GMB locations", exe.getLocalizedMessage());
		}
		return pagesMap;
	}

	private GMBAccountDTO prepareAccountDTO(GMBAccount gmbaccnt) {
		GMBAccountDTO account = new GMBAccountDTO();
		account.setAccountId(CoreUtils.getAccountOrLocationId(gmbaccnt.getName(), 1));
		account.setName(gmbaccnt.getName());
		account.setAccountName(gmbaccnt.getAccountName());
		account.setStatus(gmbaccnt.getVerificationState() != null ? gmbaccnt.getVerificationState(): null);
		account.setType(gmbaccnt.getType());
		return account;
	}

	private Map<String, GoogleMyBusinessPagesDTO> preapreGoogleMyBusinessPagesDTO(GMBAccountDTO gmbAccount, List<GMBPageLocation> gmbLocations, GoogleAuthToken gmbAuth) {
		Map<String, GoogleMyBusinessPagesDTO> businessPages = new HashMap<>();
		gmbLocations.stream().forEach(location -> {
			GoogleMyBusinessPagesDTO businessPage = prepareLocationData(location);
			businessPage.setAccountId(gmbAccount.getAccountId());
			businessPage.setAccountStatus(gmbAccount.getStatus());
			businessPage.setAccountType(gmbAccount.getType());
			businessPage.setAccountName(gmbAccount.getAccountName());
			businessPage.setUserId(gmbAuth.getUserId());
			businessPage.setRefreshTokenId(gmbAuth.getRefreshTokenId());
			businessPage.setScope(gmbAuth.getScope());
			businessPages.put(businessPage.getLocationId(), businessPage);
		});
		return businessPages;
	}

	private GoogleMyBusinessPagesDTO prepareLocationData(GMBPageLocation location) {

		GoogleMyBusinessPagesDTO businessPage = new GoogleMyBusinessPagesDTO();
		businessPage.setLocationId(CoreUtils.getAccountOrLocationId(location.getName(), 1));
		businessPage.setLocationName(location.getTitle());
		businessPage.setWebsiteUrl(location.getWebsiteUri());
		businessPage.setPrimaryPhone(location.getPhoneNumbers().getPrimaryPhone());
		businessPage.setSingleLineAddress(location.getStorefrontAddress() != null ? location.getStorefrontAddress().getAddressInSingleLine() : null);
		businessPage.setPlaceId(location.getMetadata() != null ? location.getMetadata().getPlaceId() : null);
//		businessPage.setGooglePlusId(location.getLocationKey() != null ? location.getLocationKey().getPlusPageId() : null);
		businessPage.setLocationMapUrl(location.getMetadata() != null ? location.getMetadata().getMapsUri() : null);
		businessPage.setIsVerified(isLocationVerified(location.getMetadata()));
		businessPage.setLocationUrl(location.getName());
//		businessPage.setPictureUrl(location.getPhotos() != null ? location.getPhotos().getProfilePhotoUrl() : null);
//		businessPage.setCoverImageUrl(location.getPhotos() != null ? location.getPhotos().getCoverPhotoUrl() : null);
		businessPage.setLocationState(prepareLocationState(location.getMetadata(), location.getOpenInfo()));
		return businessPage;
	}

	private String prepareLocationState(GMBLocationPageUrl metaData, OpenInfo openInfo) {
		GMBLocationState state = new GMBLocationState();
		try {
				ObjectMapper mapper = new ObjectMapper();
			if (Objects.nonNull(metaData)) {
				if(Objects.nonNull(metaData.getHasVoiceOfMerchant()) && metaData.getHasVoiceOfMerchant()){
					state.setHasVoiceOfMerchant(true);
				}
				if (Objects.nonNull(metaData.getDuplicateLocation())){
					state.setIsDuplicate(true);
				}
			}
			return mapper.writeValueAsString(state);

		} catch (Exception exe) {
			LOGGER.error("Error {} occurs while preparing location state {}", exe.getLocalizedMessage(), state);
		}
		return null;
	}

	private int isLocationVerified(GMBLocationPageUrl state) {
		if (state != null && state.getHasVoiceOfMerchant() != null && state.getHasVoiceOfMerchant()) {
			return 1;
		}
		return 0;
	}

	public Map<String, GoogleMyBusinessPagesDTO> processAllGMBLocationsForAccount(GMBAccountDTO gmbAccount, GoogleAuthToken gmbAuth, int pageSize) {
		LOGGER.info("For GMB Account: {}, Fetching GMB location", gmbAccount);
		String nextPageToken = null;
		Map<String, GoogleMyBusinessPagesDTO> gmbLocations = new HashMap<>();
		do {
			GMBPageLocationResponse locationResponse = gmbService.getGMBLocations(gmbAuth.getAccess_token(), gmbAccount.getName(), nextPageToken);
			if (Objects.isNull(locationResponse) || CollectionUtils.isEmpty(locationResponse.getLocations())) {
				// if we got no locations
				gmbLocations = null;
				break;
			}
			// prepare GMBPageLocation data
			gmbLocations.putAll(preapreGoogleMyBusinessPagesDTO(gmbAccount, locationResponse.getLocations(), gmbAuth));
			nextPageToken = locationResponse.getNextPageToken();
		} while (StringUtils.isNotBlank(nextPageToken) && gmbLocations.size() <= pageSize);
		LOGGER.info("For GMB Account: {}, Total GMB locations: {}.", gmbAccount, (gmbLocations != null ? gmbLocations.size() : 0));
		return gmbLocations;
	}

	private GoogleAuthToken getGoogleToken(Long businessId, String code, String redirectUri) throws Exception {
		Business business = businessRepo.findByBusinessId(businessId);
		SocialAppCredsInfo domainGoogleInfo = socialAppService.getGoogleAppSettings();
		GoogleAuthToken token = googleAuthenticationService.generateGoogleTokensUsingCode(domainGoogleInfo.getChannelClientId(), domainGoogleInfo.getChannelClientSecret(), code, redirectUri);
		LOGGER.info(" For business {} GoogleAuthToken is {}", businessId, token);
		if (token.getAccess_token() == null) {
			LOGGER.error("[GooglePlus Social] Failed to generated access token for business: {}", business.getBusinessId());
			throw new BirdeyeSocialException(ErrorCodes.NOT_AUTHENTICATED, EXCEPTION_IN_GENERATING_ACCESS_TOKEN);
		}
		GoogleProfileResponse user = googlePlusService.getUserDetailsForGoogleUser(token.getAccess_token());
		Integer refreshTokenId = null;
		if (Objects.isNull(user.getId()) || Objects.isNull(business.getId())){
			LOGGER.error("No user or business exist for the token:{}",token);
			throw new BirdeyeSocialException(ErrorCodes.USER_NOT_FOUND);
		}
		LOGGER.info("[GooglePlus Reconnect] User id for Google plus: {}, business id: {}", user.getId(), business.getId());
		if (StringUtils.isNotBlank(token.getRefresh_token())) {
			GoogleRefreshToken refreshToken = new GoogleRefreshToken();
			refreshToken.setRefreshToken(token.getRefresh_token());
			// TODO: Need to check if existing entry requires UPDATE (old google user) or INSERT (new google user)
			refreshTokenId = socialPostGooglePlusService.saveGoogleAuthRefeshTokenDetails(refreshToken, user, business.getId(), Integer.parseInt(domainGoogleInfo.getSocialCredsId()));
			token = googleAuthenticationService.getGoogleAuthTokens(refreshTokenId);
			token.setRefreshTokenId(refreshTokenId);
		} else {
			token = getExistingValidToken(user.getId(), domainGoogleInfo);
			if (token == null) {
				LOGGER.error("[GooglePlus Social] Failed to get refersh token business: {}", business.getBusinessId());
				throw new BirdeyeSocialException(ErrorCodes.NOT_AUTHENTICATED, EXCEPTION_IN_GENERATING_ACCESS_TOKEN);
			}
		}
		token.setUserId(user.getId());
		token.setUserName(user.getName());
		token.setEmail(user.getEmail());
		return token;
	}

	private GoogleAuthToken getGoogleToken(String code, String redirectUri) throws Exception {
		try {
			Integer refreshTokenId = null;
			SocialAppCredsInfo domainGoogleInfo = socialAppService.getGoogleAppSettings();
			GoogleAuthToken token = googleAuthenticationService.generateGoogleTokensUsingCode(domainGoogleInfo.getChannelClientId(),
					domainGoogleInfo.getChannelClientSecret(), code, redirectUri);
			if (token.getAccess_token() == null) {
				LOGGER.error("[GooglePlus Social] Failed to generated access token for session token: {}");  // For business id
				throw new BirdeyeSocialException(ErrorCodes.NOT_AUTHENTICATED, EXCEPTION_IN_GENERATING_ACCESS_TOKEN);
			}
			GoogleProfileResponse user = googlePlusService.getUserDetailsForGoogleUser(token.getAccess_token());
			if (StringUtils.isNotBlank(token.getRefresh_token())) {
				GoogleRefreshToken refreshToken = new GoogleRefreshToken();
				refreshToken.setRefreshToken(token.getRefresh_token());
				refreshTokenId = socialPostGooglePlusService.saveGoogleAuthRefeshTokenDetails(refreshToken, user, null, Integer.parseInt(domainGoogleInfo.getSocialCredsId()));
				token = googleAuthenticationService.getGoogleAuthTokens(refreshTokenId);
				token.setRefreshTokenId(refreshTokenId);
			}
			token.setUserId(user.getId());
			token.setUserName(user.getName());
			token.setEmail(user.getEmail());
			return token;
		}
		catch (Exception ex){
			LOGGER.error("Exception occured while generating access token for gmb:{}",ex.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_GENERATE_ACCESS_TOKEN_ON_GMB);
		}
	}

		private List<ChannelAccountInfo> connectGoogleMyBusinessPages(List<String> pageIds, Long enterpriseId, Integer accountId) {
		LOGGER.info("connect google my businessPages : pageIds : {}", pageIds);
		googleMyBusinessPageService.updateGMBPagesByLocationIds(pageIds, enterpriseId,accountId);
		return getAccountInfoForGmb(getGMBPages(pageIds, enterpriseId), enterpriseId);
	}

	private List<ChannelAccountInfo> connectGoogleMyBusinessPagesForReseller(List<String> pageIds, Long resellerId, String userId) {
		LOGGER.info("connect google my businessPages for reseller: pageIds : {}", pageIds);
		googleMyBusinessPageService.updateGMBPagesByLocationIdsForReseller(pageIds, resellerId);
		return getAccountInfoForGmbReseller(getGMBPagesForReseller(pageIds, resellerId), resellerId, userId);
	}

	private List<ChannelAccountInfo> getAccountInfoForGmbReseller(List<BusinessGoogleMyBusinessLocation> gmbpages, Long enterpriseId, String userId) {
		List<ChannelAccountInfo> accountInfo = new ArrayList<>();

		gmbpages.stream().forEach(gmb -> {
			Boolean googleMessageEnabled = false;
			if (Objects.nonNull(gmb.getEnterpriseId())) {
				googleMessageEnabled = isGoogleMessageEnabled(null, gmb.getEnterpriseId());
			}
			Boolean finalGoogleMessageEnabled = googleMessageEnabled;
			accountInfo.add(prepareAccountInfoReseller(gmb, enterpriseId, finalGoogleMessageEnabled, userId));
		});
		return accountInfo;
	}

	@Override
	public List<ChannelAccountInfo> getAccountInfoForGmb(List<BusinessGoogleMyBusinessLocation> gmbpages, Long enterpriseId) {
		List<ChannelAccountInfo> accountInfo = new ArrayList<>();
		Boolean googleMessageEnabled = false;
		if (enterpriseId != null) {
			googleMessageEnabled = isGoogleMessageEnabled(null, enterpriseId);
		}
		Boolean finalGoogleMessageEnabled = googleMessageEnabled;
		//enterpriseid passed in the method param was not getting used anywhere so changed the code and passed enterpriseId coking from UI request
		gmbpages.stream().forEach(gmb -> accountInfo.add(prepareAccountInfo(gmb, enterpriseId, finalGoogleMessageEnabled)));
		return accountInfo;
	}


	private ChannelAccountInfo prepareAccountInfo(BusinessGoogleMyBusinessLocation gmb, Long enterpriseId,Boolean googleMessageEnabled) {
		//enterpriseid passed in the method param is not getting used anywhere so used it for setting mapping details in method setCommonChannelAccountInfo as under
		ChannelAccountInfo accInfo = new ChannelAccountInfo();
		accInfo.setAddress(gmb.getSingleLineAddress());
		accInfo.setId(gmb.getLocationId());
		accInfo.setImage(gmb.getPictureUrl() != null ? gmb.getPictureUrl() : gmb.getCoverImageUrl());
		accInfo.setLink(gmb.getLocationMapUrl());
		Validity validity = fetchValidityAndErrorMessage(gmb, googleMessageEnabled);
		accInfo.setValidType(validity.getValidType());
		accInfo.setErrorCode(validity.getErrorCode());
		accInfo.setErrorMessage(validity.getErrorMessage());
		accInfo.setDisabled((gmb.getIsSelected() != null && gmb.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
		accInfo.setPageName(gmb.getLocationName());
		accInfo.setAccountName(gmb.getAccountName());
		accInfo.setPlaceId(gmb.getPlaceId());
		if(Objects.nonNull(gmb.getEnterpriseId()) && Objects.nonNull(gmb.getIsSelected()) && gmb.getIsSelected() == 1
				&& Objects.nonNull(enterpriseId)) {
			commonService.setCommonChannelAccountInfo(accInfo, gmb.getBusinessId(), gmb.getIsSelected(),
					enterpriseId,
					gmb.getEnterpriseId()
			);
		}
		if (Objects.nonNull(gmb.getEnterpriseId()) && Objects.nonNull(enterpriseId)) {
			boolean isSameAccount = gmb.getEnterpriseId().equals(enterpriseId);
			accInfo.setSameAccountConnections(isSameAccount);
			accInfo.setDiffAccountConnections(!isSameAccount);
		}
		return accInfo;
	}

	private ChannelAccountInfoLite prepareAccountInfoReseller(BusinessGoogleMyBusinessLocation gmb, Long enterpriseId,Boolean googleMessageEnabled, String userId) {
		ChannelAccountInfoLite accInfo = new ChannelAccountInfoLite();
		accInfo.setAddress(gmb.getSingleLineAddress());
		accInfo.setId(gmb.getLocationId());
		accInfo.setUserId(userId);
		accInfo.setImage(gmb.getPictureUrl() != null ? gmb.getPictureUrl() : gmb.getCoverImageUrl());
		accInfo.setLink(gmb.getLocationMapUrl());
		Validity validity = fetchValidityAndErrorMessage(gmb, googleMessageEnabled);
		accInfo.setValidType(validity.getValidType());
		accInfo.setErrorCode(validity.getErrorCode());
		accInfo.setErrorMessage(validity.getErrorMessage());
		//BIRD-57670
		accInfo.setDisabled(null);
		accInfo.setPageName(gmb.getLocationName());
		accInfo.setAccountName(gmb.getAccountName());
		return accInfo;
	}

	private List<BusinessGoogleMyBusinessLocation> getGMBPages(List<String> pageIds, Long enterpriseId) {
		return googleMyBusinessPageService.getGMBPagesByEnterpriseAndLocationIds(pageIds, enterpriseId);
	}

	private List<BusinessGoogleMyBusinessLocation> getGMBPagesForReseller(List<String> pageIds, Long enterpriseId) {
		return googleMyBusinessPageService.getGMBPagesByResellerAndLocationIds(pageIds, enterpriseId);
	}

	@Override
	public LocationPageMapping getLocationMappingPages(Long parentId, Integer userId, List<Integer> businessIds, Set<String> status,String type,Integer page,Integer size,String search, List<String> includeModules) throws Exception {
		LOGGER.info("getLocationMappingPages: parentId {} userId {} status {}", parentId, userId, status);
		if ( Objects.isNull(parentId) || Objects.isNull(userId) || CollectionUtils.isEmpty(businessIds) ) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid value for parentId/userId/businessIds");
		}
		LocationPageMapping response = new LocationPageMapping();
		response.setTotalLocations(businessIds.size());
		//TODO: add error in case of blank status input
		if(Objects.isNull(status) || status.size() == 0) {
			response.setLocationList(new ArrayList<>());
			response.setDisconnectedCount(0);
			response.setUnmapped(0);
			response.setAllPagesMapped(false);
			response.setPermissionIssuePageCount(0);
			return response;
		}
		Boolean toSearch = Objects.nonNull(search) && !search.isEmpty();
		if(!CollectionUtils.isEmpty(businessIds) ) {
			List<BusinessGoogleMyBusinessLocation> gmbPages = socialGMBRepo.findAllByBusinessIdIn(businessIds);
			response.setUnmapped(businessIds.size() - gmbPages.size());
			if( status.size()>1)
				prepareLocationGooglePageMapping(parentId, businessIds, response, status,page,size,search,toSearch,gmbPages, includeModules);
			else
				prepareLocationDataForMappedAndUnmapped(parentId,businessIds,response,status,page,size,search,toSearch,gmbPages, includeModules);
		}
		return response;
	}

	private void prepareLocationDataForMappedAndUnmapped(Long parentId, List<Integer> businessIds, LocationPageMapping response, Set<String> status, Integer page, Integer size,String search,Boolean toSearch,List<BusinessGoogleMyBusinessLocation> gmbPages, List<String> includeModules) {
		Map<Integer, BusinessGoogleMyBusinessLocation> businessGMBMap = new HashMap<>();
		if(Objects.nonNull(gmbPages)){
			businessGMBMap = gmbPages.stream().collect(Collectors.toMap(BusinessGoogleMyBusinessLocation::getBusinessId, Function.identity()));
		}
		List<ChannelLocationInfo> locList = new LinkedList<>();
		Map<Integer, BusinessLocationLiteEntity> businessLocMap;
		List<Integer> filterBusinessIds = new ArrayList<>();
		if(Objects.nonNull(businessGMBMap)){
			filterBusinessIds.addAll(businessGMBMap.keySet());
		}
		businessIds.removeAll(filterBusinessIds);
		List<BusinessGoogleMyBusinessLocation> invalidMappings = new ArrayList<>();
		if (status.contains(LocationStatusEnum.UNMAPPED.getName()) ) {
			if(toSearch){
				businessLocMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
			}else{
				businessLocMap = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
			}
			if(Objects.nonNull(businessLocMap)) {
				locList = getUnmappedLocations(businessLocMap, businessGMBMap);
			}
			LOGGER.info("prepareLocationGooglePageMapping: unmapped location list {}", locList);
		} else if (status.contains(LocationStatusEnum.MAPPED.getName())) {
			if(toSearch){
				businessLocMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(filterBusinessIds,page,size,search,response);
			}else{
				businessLocMap = businessUtilService.getBusinessLocationsLiteMapPaginated(filterBusinessIds,page,size);
			}
			Pair<List<ChannelLocationInfo>, DisconnectedInvalidPermissionInfo> pair = null;
			if(Objects.nonNull(businessLocMap)){
				pair = getMappedLocations(businessLocMap, businessGMBMap, invalidMappings,parentId, includeModules);
				locList = pair.getKey();
				LOGGER.info("prepareLocationGooglePageMapping: mapped location list {}", locList);
				response.setDisconnectedCount(pair.getValue().getDisconnectedCount());
				response.setPermissionIssuePageCount(pair.getValue().getPermissionMissingCount());
			}
		}
		locList.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
		response.setLocationList(locList);
		response.setAllPagesMapped(businessIds.size() <= 0);
	}

	@Override
	public Integer getUnmappedLocationCount(UnmappedLocationMappingReq request) {
		Map<Integer, BusinessLocationLiteEntity> businessIdLocationMap = businessUtilService.getBusinessLocationsLiteMap(request.getBusinessIds());
		if (MapUtils.isNotEmpty(businessIdLocationMap)) {
			Map<Integer, BusinessGoogleMyBusinessLocation> businessGMBMap = getBusinessGMBPageMap(request.getBusinessIds());
			List<ChannelLocationInfo> unmappedLocList = getUnmappedLocations(businessIdLocationMap, businessGMBMap);
			return unmappedLocList.size();
		}else{
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "No business found for given businessIds");
		}
	}





	private Map<Integer, BusinessGoogleMyBusinessLocation> getBusinessGMBPageMap(List<Integer> businessIds) {
		if ( CollectionUtils.isNotEmpty(businessIds) ) {
			List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = socialGMBRepo.findByBusinessIdIn(businessIds);
			if ( CollectionUtils.isNotEmpty(businessGoogleMyBusinessLocations) ) {
				Map<Integer, BusinessGoogleMyBusinessLocation> gmbLocationMap = new HashMap<>();
				for (BusinessGoogleMyBusinessLocation bgmb : businessGoogleMyBusinessLocations) {
					if (gmbLocationMap.get(bgmb.getBusinessId()) != null) {
						if (gmbLocationMap.get(bgmb.getBusinessId()).getIsValid() == 0 && bgmb.getIsValid() == 1) {
							gmbLocationMap.put(bgmb.getBusinessId(), bgmb);
						}
					} else {
						gmbLocationMap.put(bgmb.getBusinessId(), bgmb);
					}
				}
				return gmbLocationMap;
			}
		}
		return null;
	}

	private ChannelLocationInfo getChannelLocInfo(BusinessLocationLiteEntity businessLoc) {
		final ChannelLocationInfo locInfo = new ChannelLocationInfo();
		locInfo.setLocationId(businessLoc.getId());
		locInfo.setLocationName(businessLoc.getAlias1() != null ? businessLoc.getAlias1() :businessLoc.getName());
		locInfo.setAddress(commonService.prepareBusinessAddress(businessLoc));
		return locInfo;
	}

	/**
	 * Fetches details of all unmapped locations for given businesses
	 * @param businessLocMap
	 * @param businessGMBMap
	 * @return List of ChannelLocationInfo
	 */
	private List<ChannelLocationInfo> getUnmappedLocations(Map<Integer, BusinessLocationLiteEntity> businessLocMap, Map<Integer, BusinessGoogleMyBusinessLocation> businessGMBMap) {
		final List<ChannelLocationInfo> response = new ArrayList<>();
		for ( Map.Entry<Integer, BusinessLocationLiteEntity> entry : businessLocMap.entrySet() ) {
			// Only take this business-location if it does not have any mapping
			if ( businessGMBMap == null || businessGMBMap.isEmpty() || !businessGMBMap.containsKey(entry.getKey()) ) {
				response.add(getChannelLocInfo(entry.getValue()));
			}
		}
		response.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
		return response;
	}

	/**
	 * Fetches details of all mapped locations for given businesses
	 * @param businessLocMap
	 * @param businessGMBMap
	 * @return Pair of disconnectedCount and List of ChannelLocationInfo
	 */
	private Pair<List<ChannelLocationInfo>, DisconnectedInvalidPermissionInfo> getMappedLocations(Map<Integer, BusinessLocationLiteEntity> businessLocMap,
		Map<Integer, BusinessGoogleMyBusinessLocation> businessGMBMap,List<BusinessGoogleMyBusinessLocation> invalidMappings,Long parentId, List<String> includeModules) {
		final List<ChannelLocationInfo> response = new ArrayList<>();
		DisconnectedInvalidPermissionInfo info = new DisconnectedInvalidPermissionInfo();
		if(Objects.nonNull(businessLocMap)){
			int disconnectedCount = 0;
			int permissionMissingCount = 0;
			Boolean googleMessageEnabled = false;
			if(parentId != null) {
				try {
					googleMessageEnabled =  isGoogleMessageEnabled(null,parentId) ;
				}catch (ExternalAPIException e){
					LOGGER.info("Something went wrong while getting isGoogleMessagingEnabled for enterprise id: {}",parentId);
				}
			}
			List<PermissionMapping> permissionMappings = permissionMappingService.getDataByChannel(
					SocialChannel.GMB.getName());
			Map<String, String> errorCodesMap = permissionMappings.stream()
					.filter(permission -> permission.getErrorCode() != null)
					.collect(Collectors.toMap(PermissionMapping::getErrorCode, PermissionMapping::getErrorMessage,
							(existingValue, newValue) -> existingValue));
			for ( Map.Entry<Integer, BusinessLocationLiteEntity> entry : businessLocMap.entrySet() ) {
			// Only take this business-location if it has a mapping in gmb mapping table
				if ( Objects.nonNull(businessGMBMap) && !businessGMBMap.isEmpty() && businessGMBMap.containsKey(entry.getKey()) ) {
					final ChannelLocationInfo locInfo = getChannelLocInfo(entry.getValue());
					BusinessGoogleMyBusinessLocation rawGMBPage = null;
					rawGMBPage = businessGMBMap.get(entry.getKey());

					if ( Objects.nonNull(rawGMBPage) ) {
						final LocationPageListInfo pageInfo = prepareGMBPageData(rawGMBPage,googleMessageEnabled, includeModules,errorCodesMap);
						if(pageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.INVALID.getName())
								|| pageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName()))
						disconnectedCount += 1;
						permissionMissingCount += (googleMessageEnabled.equals(true)
							&& StringUtils.isNotEmpty(pageInfo.getErrorCode())
							&& pageInfo.getErrorCode().equalsIgnoreCase(Constants.MESSENGER_NOT_ENABLED) ? 1 : 0);
						Map<String, LocationPageListInfo> pageData = new HashMap<>();
						pageData.put(SocialChannel.GMB.getName(), pageInfo);
						locInfo.setPageData(pageData);
						response.add(locInfo);
					} else {
						// mapping is present, but page is not present in raw table
						LOGGER.error("getMappedLocations: PageId {} is not present in raw page table",businessGMBMap.get(entry.getKey()));
						invalidMappings.add(businessGMBMap.get(entry.getKey()));
					}
				}
			}
			response.sort(Comparator.comparing(ChannelLocationInfo::getLocationName, nullsFirst(Comparator.naturalOrder())));
			info.setDisconnectedCount(disconnectedCount);
			info.setPermissionMissingCount(permissionMissingCount);
		}
		return new Pair<>(response, info);
	}

	private void prepareLocationGooglePageMapping(Long parentId, List<Integer> businessIds, LocationPageMapping response, Set<String> status,Integer page,Integer size,String search,Boolean toSearch,List<BusinessGoogleMyBusinessLocation> gmbPages, List<String> includeModules) {
		Map<Integer, BusinessLocationLiteEntity> businessLocMap = new HashMap<>();
		if(toSearch){
			businessLocMap = businessUtilService.getBusinessLocationsLiteMapPaginatedSearch(businessIds,page,size,search,response);
		} else{
			businessLocMap = businessUtilService.getBusinessLocationsLiteMapPaginated(businessIds,page,size);
		}

		if (Objects.nonNull(businessLocMap) && !businessLocMap.isEmpty()) {
			LOGGER.info("prepareLocationGooglePageMapping: Total business locations fetched {}", businessLocMap.size());
			List<BusinessGoogleMyBusinessLocation> invalidMappings = new ArrayList<>();
			Map<Integer, BusinessLocationLiteEntity> finalBusinessLocMap1 = businessLocMap;
			List<BusinessGoogleMyBusinessLocation> locationList = gmbPages.stream().filter(p -> finalBusinessLocMap1.containsKey(p.getBusinessId())).collect(Collectors.toList());
			Map<Integer, BusinessGoogleMyBusinessLocation> businessGMBMap = new HashMap<>();
			if(Objects.nonNull(locationList)){
				businessGMBMap = locationList.stream().collect(Collectors.toMap(BusinessGoogleMyBusinessLocation::getBusinessId, Function.identity()));
			}
			LOGGER.info("prepareLocationGooglePageMapping: Total gmb mappings found {}", businessGMBMap == null ? 0 : businessGMBMap.size());

			if(Objects.nonNull(status)){
				List<ChannelLocationInfo> unmappedLocList = getUnmappedLocations(businessLocMap, businessGMBMap);
				Pair<List<ChannelLocationInfo>, DisconnectedInvalidPermissionInfo> pair = getMappedLocations(businessLocMap, businessGMBMap, invalidMappings,parentId,includeModules);
				List<ChannelLocationInfo> mappedLocList = pair.getKey();
				// make invalid mappings a part of unmapped locations
				Map<Integer, BusinessLocationLiteEntity> finalBusinessLocMap = businessLocMap;
				invalidMappings.forEach(bizGmbLoc -> unmappedLocList.add(getChannelLocInfo(finalBusinessLocMap.get(bizGmbLoc.getBusinessId()))));
				LOGGER.info("prepareLocationGooglePageMapping: mapped location list {}", mappedLocList);
				LOGGER.info("prepareLocationGooglePageMapping: unmapped location list {}", unmappedLocList);
				response.setAllPagesMapped(mappedLocList.size() == businessLocMap.size());
				response.setDisconnectedCount(pair.getValue().getDisconnectedCount());
				response.setPermissionIssuePageCount(pair.getValue().getPermissionMissingCount());
				response.setLocationList(unmappedLocList);
				response.getLocationList().addAll(mappedLocList);
			}
		} else {
			response.setLocationList(new ArrayList<>());
		}
	}

	private LocationPageListInfo prepareGMBPageData(BusinessGoogleMyBusinessLocation gmbPage,
													Boolean googleMessageEnabled, List<String> includeModules,Map<String, String> errorCodesMap) {
		LocationPageListInfo pageInfo = new LocationPageListInfo();
		pageInfo.setId(gmbPage.getLocationId());
		pageInfo.setImage(gmbPage.getCoverImageUrl());
		pageInfo.setLink(gmbPage.getLocationMapUrl());
		pageInfo.setPageName(gmbPage.getLocationName());
		pageInfo.setAddress(gmbPage.getSingleLineAddress());
		pageInfo.setConnectedInReseller(Objects.isNull(gmbPage.getResellerId())?false:true);
		Validity validity = null;
		if(Objects.isNull(errorCodesMap)) {
			validity=fetchValidityAndErrorMessage(gmbPage, googleMessageEnabled);
		}else {
			validity=fetchValidity(gmbPage, googleMessageEnabled,errorCodesMap);
		}
		pageInfo.setValidType(validity.getValidType());
		pageInfo.setErrorCode(validity.getErrorCode());
		pageInfo.setErrorMessage(validity.getErrorMessage());

		if (CollectionUtils.isNotEmpty(includeModules)) {
			List<String> permissionList = null;
			if(Objects.nonNull(gmbPage.getPermissions())) {
				permissionList = Arrays.asList(gmbPage.getPermissions().split(","));
				permissionList = permissionList.stream().map(String::trim).collect(Collectors.toList());
			}
			LOGGER.info("GMB page: {}, contains permissions: {}",gmbPage.getAccountId(),permissionList);
			Map<String, PermissionDTO> permissionsMap = new HashMap<>();
			for (String module : includeModules) {

				SocialModulePermission socialModulePermission = socialModulePermissionService
						.getPermissionsForChannelAndModule(SocialChannel.GOOGLE.getId(), module);
				List<String> modulePermissions = new ArrayList<>();
				if(Objects.nonNull(socialModulePermission) && Objects.nonNull(socialModulePermission.getPermissionsNeeded())) {
					modulePermissions = Arrays.asList(socialModulePermission.getPermissionsNeeded().split(","));
				}
				LOGGER.info("For GMB page: {}, required permissions for module: {} are: {}",gmbPage.getAccountId(),module,modulePermissions);
				if(CollectionUtils.isNotEmpty(permissionList) && new HashSet<>(permissionList).containsAll(modulePermissions)) {
					permissionsMap.put(module, new PermissionDTO(true));
				} else {
					permissionsMap.put(module, new PermissionDTO(false));
				}
			}
			pageInfo.setModulePermission(permissionsMap);
		}
		return pageInfo;
	}

	private String prepareBusinessAddress(BusinessLocationEntity location) {
		StringBuilder address = new StringBuilder();
		if (StringUtils.isNotEmpty(location.getAddress1())) {
			address.append(location.getAddress1()).append(", ");

		}
		if (StringUtils.isNotEmpty(location.getAddress2())) {
			address.append(location.getAddress2()).append(", ");

		}
		if (StringUtils.isNotEmpty(location.getCity())) {
			address.append(location.getCity()).append(", ");

		}
		if (StringUtils.isNotEmpty(location.getState())) {
			address.append(location.getState()).append(" ");

		}
		// Zipcode will be always there
		if (StringUtils.isNotEmpty(location.getZip())) {
			address.append(location.getZip());

		}
		return address.toString();
	}

	/**
	 * Method to insert entry in platform business_gmb_location
	 * @param gmbPage
	 * @param locationId
	 * @param userId
	 */
	@Override
	@Transactional
	public void savePlatformMapping(BusinessGoogleMyBusinessLocation gmbPage, Integer locationId, Integer userId) {
		BusinessGMBLocation gmbPageNew = getBusinessGMBPage(gmbPage);
		LOGGER.info("Inside  mapping: location: {}",locationId);
		if (gmbPage != null) {
			gmbPageNew.setBusinessId(locationId);
			gmbPageNew.setEnabled(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialAutoPostFlag());
			gmbPageNew.setCreatedBy(userId);
			gmbPageNew.setUpdatedBy(userId);
			businessGMBPageRepo.saveAndFlush(gmbPageNew);
		}
		LOGGER.info("Inside  mapping function complete for location: {}",locationId);
	}

	@Override
	public void saveGMBLocationMapping(Integer locationId, String pageId, Integer userId,String type, Long resellerId) {
		List<BusinessGoogleMyBusinessLocation> gmbPageList = googleMyBusinessPageService.getGMBPagesByLocationIds(Collections.singletonList(pageId));
		if (CollectionUtils.isEmpty(gmbPageList)) {
			LOGGER.error("For GMB page id {} no data found ", pageId);
			throw new BirdeyeSocialException(ErrorCodes.GMB_PAGE_NOT_FOUND, "GMB page data not found");
		}
		if(socialGMBRepo.existsByBusinessId(locationId)){
			throw new BirdeyeSocialException(ErrorCodes.MAPPING_ALREADY_EXISTS,MAPPING_ALREADY_EXIST_ERROR);
		}
		BusinessGoogleMyBusinessLocation gmbPage = gmbPageList.get(0);
		if(Objects.nonNull(gmbPage) && Objects.nonNull(resellerId)){
			commonService.checkRequestFromAuthorizedSourceUsingLongResellerID(gmbPage.getResellerId(), resellerId);
		}

		gmbPage.setBusinessId(locationId);
		updateGMBLocationForReseller(gmbPage, type, locationId);
		googleMyBusinessPageService.saveGMBRowPage(gmbPage);
		commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(), gmbPageList, String.valueOf(userId), locationId,Constants.ENTERPRISE.equals(type)?gmbPage.getEnterpriseId():gmbPage.getResellerId());
		kafkaProducer.sendObject(Constants.GMB_PAGE_MAPPING_ADDED, new LocationPageMappingRequest(locationId, pageId));
		BAMUpdateRequest payload =
				new BAMUpdateRequest("gmb", locationId, gmbPageList.get(0).getLocationMapUrl(),gmbPageList.get(0).getLocationId(), gmbPageList.get(0).getPlaceId());
		kafkaProducer.sendWithKey(Constants.INTEGRATION_TOPIC, String.valueOf(locationId), payload);
	}

	private boolean isAutoLaunchEnabled(Long enterpriseId) {
		return socialBusinessPropertyService.findAutoLaunchEnabledByEnterpriseId(enterpriseId);
	}

	@Override
	public boolean isPageBusinessPlaceIdSame(Integer locationId, String pageId, Integer userId) throws Exception {
		List<BusinessGoogleMyBusinessLocation> gmbPages = googleMyBusinessPageService.getGMBPagesByLocationIds(Collections.singletonList(pageId));
		if ( CollectionUtils.isEmpty(gmbPages) ) {
			throw new BirdeyeSocialException(ErrorCodes.GMB_PAGE_LOCATION_NOT_FOUND, "GMB Page with provided pageId not found");
		}
		Map<String,List<String>> businessPlaceIdsMap = bamAggregationService.getBusinessPlaceIds(SocialChannel.GMB.getName(), Collections.singletonList(locationId));
		if ( businessPlaceIdsMap != null ) {
			LOGGER.info("Checking if placeId {} exists in map {}", gmbPages.get(0).getPlaceId(), businessPlaceIdsMap);
			return businessPlaceIdsMap.getOrDefault(String.valueOf(locationId), new ArrayList<>()).contains(gmbPages.get(0).getPlaceId());
		}
		return false;
	}

	private BusinessGMBLocation getBusinessGMBPage(BusinessGoogleMyBusinessLocation page) {
		BusinessGMBLocation gmbPage = new BusinessGMBLocation();
		gmbPage.setRefreshTokenId(page.getRefreshTokenId());
		gmbPage.setLocationId(page.getLocationId());
		gmbPage.setLocationName(page.getLocationName());
		gmbPage.setLocationUrl(page.getLocationUrl());
		gmbPage.setLocationMapUrl(page.getLocationMapUrl());
		gmbPage.setProfileImageUrl(page.getPictureUrl() != null ? page.getPictureUrl() : page.getCoverImageUrl());
		gmbPage.setIsValid(page.getIsValid());
		gmbPage.setAddress(page.getSingleLineAddress());
		return gmbPage;
	}

	@Override
	public void removeGMBLocationPageMappings(List<LocationPageMappingRequest> locationPageMappings,String type, boolean unlink) throws Exception {
		LOGGER.info("remove GMB page mappings with locationPageMappings {}", locationPageMappings);
		Set<String> pageIds = locationPageMappings.stream().map(LocationPageMappingRequest::getPageId).collect(Collectors.toSet());

		List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = googleMyBusinessPageService.getGMBPagesByLocationIds(pageIds);
		// Google messaging is deprecated
		/*
		Map<Integer,List<BusinessGoogleMyBusinessLocation>> agentPagesIdsMap = prepareMapOfAgentIdAndListOfPageIds(businessGoogleMyBusinessLocations);
		for(Map.Entry<Integer,List<BusinessGoogleMyBusinessLocation>> map : agentPagesIdsMap.entrySet()){
			try {
				LocationBulkUnlaunchRequest locationBulkUnlaunchRequest = new LocationBulkUnlaunchRequest(map.getKey(), true);
				unLaunchLocationByAgentId(locationBulkUnlaunchRequest,map.getValue());
			}catch (Exception e){
				LOGGER.error("Exception occurred while un-launch the location for agent: {}",map);
			}
		}
		 */
		if(Objects.nonNull(locationPageMappings.get(0)) && Objects.nonNull(locationPageMappings.get(0).getResellerId())){
			List<Long> storedResellerIds = businessGoogleMyBusinessLocations.stream().map(BusinessGoogleMyBusinessLocation::getResellerId).collect(Collectors.toList());
			commonService.checkRequestFromAuthorizedSourceUsingResellerIdsList(storedResellerIds, locationPageMappings.get(0).getResellerId());
		}
		businessGoogleMyBusinessLocations.forEach(businessGoogleMyBusinessLocation ->{
			commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.REMOVE_MAPPING.name(), Arrays.asList(businessGoogleMyBusinessLocation),
					businessGoogleMyBusinessLocation.getUserId(), businessGoogleMyBusinessLocation.getBusinessId(),Constants.ENTERPRISE.equals(type)?businessGoogleMyBusinessLocation.getEnterpriseId():businessGoogleMyBusinessLocation.getResellerId());
			businessGoogleMyBusinessLocation.setBusinessId(null);
			if(Objects.nonNull(businessGoogleMyBusinessLocation.getResellerId())){
				businessGoogleMyBusinessLocation.setEnterpriseId(null);
				businessGoogleMyBusinessLocation.setAccountId(null);
			}
			if(unlink) {
				businessGoogleMyBusinessLocation.setIsSelected(0);
				businessGoogleMyBusinessLocation.setEnterpriseId(null);
				businessGoogleMyBusinessLocation.setResellerId(null);
			}
		});
		googleMyBusinessPageService.saveOrUpdateGMBRowPage(businessGoogleMyBusinessLocations);

		kafkaProducer.sendObject(Constants.GMB_PAGE_MAPPING_REMOVED, locationPageMappings);

	}

	private Business getEnterpriseRequest(Business business) {
		if (business != null) {
			if(business.getEnterprise() != null) {
				return business.getEnterprise();
			} else {
				return business;
			}
		}
		return null;
	}

	@Override
	public List<SocialPageListInfo> getUnmappedGMBPagesByEnterpriseId(Long enterpriseId) {
		// Get connected google pages
		List<BusinessGoogleMyBusinessLocation> connectedGmbPages = googleMyBusinessPageService.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
		if (CollectionUtils.isEmpty(connectedGmbPages)) {
			return Collections.emptyList();
		}
        List<PermissionMapping> permissionMappings = permissionMappingService.getDataByChannel(
				SocialChannel.GMB.getName());
		Map<String, String> errorCodesMap = permissionMappings.stream()
				.filter(permission -> permission.getErrorCode() != null)
				.collect(Collectors.toMap(PermissionMapping::getErrorCode, PermissionMapping::getErrorMessage,
						(existingValue, newValue) -> existingValue));
		List<SocialPageListInfo> unmappedGMBPagesFinal = connectedGmbPages.stream()
				.map(connectedGMBPage -> convertBusinessGMBPageToLocationPageListInfo(connectedGMBPage,enterpriseId, false,errorCodesMap))
				.collect(Collectors.toList());
		unmappedGMBPagesFinal.sort(Comparator.comparing(SocialPageListInfo::getPageName, nullsFirst(Comparator.naturalOrder())));
		return unmappedGMBPagesFinal;
	}

	private InboxStatusResponse checkMessengerStatus(BusinessGoogleMyBusinessLocation gmbPage, Long enterpriseId, Boolean googleMessageEnabled) {
		InboxStatusResponse response = new InboxStatusResponse();
		if (googleMessageEnabled == false) {
			response.setMessagingEnabled(false);
			response.setMessagingInvalidType(Constants.MESSAGING_NOT_ALLOWED);
		} else {
			if (gmbPage.getPermissions() == null || !gmbPage.getPermissions().contains(Constants.BUSINESS_MESSAGING)
					|| !gmbPage.getPermissions().contains(Constants.BUSINESS_COMMUNICATION)) {
				response.setMessagingInvalidType(Constants.MESSENGER_NOT_ENABLED);
				response.setMessagingEnabled(false);
			} else {
				response.setMessagingEnabled(true);
			}
		}
		return response;
	}

	private SocialPageListInfo convertBusinessGMBPageToLocationPageListInfo(BusinessGoogleMyBusinessLocation gmbPage,Long enterpriseId, Boolean googleMessageEnabled,Map<String, String> errorCodesMap) {
		if (gmbPage == null) {
			return null;
		}
		SocialPageListInfo locationPageListInfo = new SocialPageListInfo();
		locationPageListInfo.setId(gmbPage.getLocationId());
		locationPageListInfo.setMapped(gmbPage.getBusinessId() != null ? Boolean.TRUE : Boolean.FALSE);
		locationPageListInfo.setPageName(gmbPage.getLocationName());
		locationPageListInfo.setLink(gmbPage.getLocationMapUrl());
		locationPageListInfo.setAddress(gmbPage.getSingleLineAddress());
		locationPageListInfo.setPhoneNumber(gmbPage.getPrimaryPhone());
		locationPageListInfo.setImage(gmbPage.getPictureUrl() != null ? gmbPage.getPictureUrl() : gmbPage.getCoverImageUrl());
		Validity validity = fetchValidity(gmbPage, googleMessageEnabled,errorCodesMap);
		locationPageListInfo.setValidType(validity.getValidType());
		locationPageListInfo.setErrorCode(validity.getErrorCode());
		locationPageListInfo.setErrorMessage(validity.getErrorMessage());
		locationPageListInfo.setConnectedInReseller(Objects.isNull(gmbPage.getResellerId())?false:true);
		return locationPageListInfo;
	}

	@Override
	public void removeGMBPage(List<String> pageIds, Long enterpriseId)  {
		LOGGER.info("Remove GMB page Ids {}", pageIds);
		final List<BusinessGoogleMyBusinessLocation> gmbRawPages = googleMyBusinessPageService.getGMBPagesByLocationId(pageIds);
		if(CollectionUtils.isEmpty(gmbRawPages)){
			return;
		}
		removeGMBPages(gmbRawPages);


		// TODO: 06/01/23 Why we are doing this update ? @Navroj
		//pushgMsgLocationLaunchInFirebase(enterpriseId,, GoogleLocationResponseStatus.UNLAUNCHED.name());
//		updateFirebaseLocationLaunchState(new LocationUnlaunchRequest(null, enterpriseId), true);
	}

	private void removeGMBPages(List<BusinessGoogleMyBusinessLocation> gmbRawPages) {
		// Google messaging is deprecated
		/*
		Map<Integer,List<BusinessGoogleMyBusinessLocation>> agentPagesIdsMap = prepareMapOfAgentIdAndListOfPageIds(gmbRawPages);
		for(Map.Entry<Integer,List<BusinessGoogleMyBusinessLocation>> map : agentPagesIdsMap.entrySet()){
			try {
				LocationBulkUnlaunchRequest locationBulkUnlaunchRequest = new LocationBulkUnlaunchRequest(map.getKey(), true);
				unLaunchLocationByAgentId(locationBulkUnlaunchRequest,map.getValue());
			}catch (Exception e){
				LOGGER.error("Exception occurred while un-launch the location for agent: {}",map);
			}
		}
		 */
		gmbRawPages.forEach(rawPage -> {
			socialGMBRepo.delete(rawPage.getId());
			commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.REMOVE_PAGE.name(), Collections.singletonList(rawPage),
					rawPage.getUserId(), rawPage.getBusinessId(),rawPage.getEnterpriseId());
		});
		producer.sendObject(Constants.SOCIAL_PAGE_REMOVED, gmbRawPages.stream().map(rawPage ->
				new ChannelPageRemoved(SocialChannel.GMB.getName(), rawPage.getLocationId(), rawPage.getLocationName(),
						rawPage.getBusinessId(), rawPage.getEnterpriseId(), rawPage.getPlaceId(), null, rawPage.getId())).collect(Collectors.toList()));
	}

	@Deprecated
	private Map<Integer, List<BusinessGoogleMyBusinessLocation>> prepareMapOfAgentIdAndListOfPageIds(List<BusinessGoogleMyBusinessLocation> gmbRawPages) {
		Map<Integer, List<BusinessGoogleMyBusinessLocation>> agentIdMap = new LinkedHashMap<>();
		if(CollectionUtils.isEmpty(gmbRawPages)){
			return agentIdMap;
		}
		return gmbRawPages.stream().filter(page -> Objects.nonNull(page.getAgentId()))
				.collect(Collectors.groupingBy(BusinessGoogleMyBusinessLocation::getAgentId,
						Collectors.mapping(Function.identity(), Collectors.toList())));

	}

	@Override
	public void removeGMBPageForReseller(List<String> pageIds, Integer limit)  {

		LOGGER.info("Remove GMB page Ids {}", pageIds);
		List<ChannelPageRemoved> channelPageRemoved = new ArrayList<>();
		final List<BusinessGoogleMyBusinessLocation> gmbRawPages = googleMyBusinessPageService.
				getGMBPagesByLocationIdWithLimit(pageIds,new org.springframework.data.domain.PageRequest(0,limit));
		if(CollectionUtils.isEmpty(gmbRawPages)){
			return;
		}
		// Google messaging is deprecated
		/*
		Map<Integer,List<BusinessGoogleMyBusinessLocation>> agentGmbPageMap = prepareMapOfAgentIdAndListOfPageIds(gmbRawPages);
		if(CollectionUtils.isNotEmpty(agentGmbPageMap.values())) {
			for (Map.Entry<Integer, List<BusinessGoogleMyBusinessLocation>> map : agentGmbPageMap.entrySet()) {
				try {
					LocationBulkUnlaunchRequest locationBulkUnlaunchRequest = new LocationBulkUnlaunchRequest(map.getKey(), true);
					unLaunchLocationByAgentId(locationBulkUnlaunchRequest, map.getValue());
				} catch (Exception e) {
					LOGGER.error("Exception occurred while un-launch the location for agent: {}", map);
				}
			}
		}
		 */
		googleMyBusinessPageService.deleteGMBLocation(pageIds);
		gmbRawPages.forEach(page -> {
			commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.REMOVE_PAGE.name(), Arrays.asList(page), page.getUserId(), page.getBusinessId(), page.getEnterpriseId());

			ChannelPageRemoved channelPageRemove = new ChannelPageRemoved(SocialChannel.GMB.getName(),
					page.getLocationId(), page.getLocationName(), page.getBusinessId(), page.getResellerId(), page.getPlaceId(), null, page.getId());
			channelPageRemoved.add(channelPageRemove);
		});

		producer.sendObject(Constants.SOCIAL_PAGE_REMOVED, channelPageRemoved);

		//pushgMsgLocationLaunchInFirebase(resellerId, GoogleLocationResponseStatus.UNLAUNCHED.name());
		//updateFirebaseLocationLaunchState(new LocationUnlaunchRequest(null, enterpriseId), true);
	}

	@Override
	public PaginatedConnectedPages getResellerPages(Long resellerId, PageConnectionStatus status, Integer page, Integer size,String search, ResellerSearchType searchType,
													PageSortDirection sortDirection, ResellerSortType sortParam, List<Integer> locationIds,
													MappingStatus mappingStatus, List<Integer> userIds, Boolean locationFilterSelected) {
		LOGGER.info("Get pages for reseller id : {}",resellerId);
		PaginatedConnectedPages connectedPage = new PaginatedConnectedPages();
		Map<String, ChannelPageDetails> pageTypes = new HashMap<>();
		Page<BusinessGoogleMyBusinessLocation> connectedGmbPages ;
		Long getInvalidPageIds = socialGMBRepo.findCountByResellerIdAndValidityType(resellerId,Arrays.asList(ValidTypeEnum.INVALID.getId(),ValidTypeEnum.PARTIAL_VALID.getId()));
		connectedGmbPages = searchSortAndPaginate(search, resellerId, locationIds, status, userIds, 1, mappingStatus,
				page, size, sortDirection, sortParam, locationFilterSelected);

		LOGGER.info("get-pages : Found {} pages", CollectionUtils.size(connectedGmbPages));
		try {
			ChannelPageDetails gmbAccountInfo = getGMBPageInfoForReseller(connectedGmbPages.getContent(),resellerId);
			gmbAccountInfo.setDisconnected(Math.toIntExact(getInvalidPageIds));
			pageTypes.put(SocialChannel.GMB.getName(), gmbAccountInfo);
			connectedPage.setPageTypes(pageTypes);
			connectedPage.setPageCount(connectedGmbPages.getTotalPages());
			connectedPage.setTotalCount(connectedGmbPages.getTotalElements());
			return connectedPage;
		} catch (Exception e) {
			LOGGER.info("exception occred while setting page details error: {}",e.getMessage());
			throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, "Internal Server Error");
		}
	}

	public Page<BusinessGoogleMyBusinessLocation> searchSortAndPaginate(String search, Long resellerId, List<Integer> businessIds, PageConnectionStatus pageConnectionStatus,
																List<Integer> createdByIds, Integer isSelected, MappingStatus mappingStatus, Integer page, Integer size,
																PageSortDirection sortDirection, ResellerSortType sortType, Boolean locationFilterSelected) {
		Specification<BusinessGoogleMyBusinessLocation> spec = Specifications.where((gmbSpecification.hasResellerId(resellerId)));
		if(Objects.nonNull(search)) {
			spec = Specifications.where(spec).and(gmbSpecification.hasLocationName(search));
		}
		if(CollectionUtils.isNotEmpty(businessIds)) {
			if(locationFilterSelected) {
				if(MappingStatus.UNMAPPED.equals(mappingStatus)) return new PageImpl<>(new ArrayList<>());
				else spec = Specifications.where(spec).and(gmbSpecification.inBusinessIds(businessIds));
			} else {
				if(MappingStatus.MAPPED.equals(mappingStatus)) {
					spec = Specifications.where(spec).and(gmbSpecification.inBusinessIds(businessIds));
				} else if(MappingStatus.UNMAPPED.equals(mappingStatus)) {
					spec = Specifications.where(spec).and(gmbSpecification.hasBusinessIdNullOrNotNull(true));
				} else {
					Specification<BusinessGoogleMyBusinessLocation> orSpec = Specifications.where(gmbSpecification.inBusinessIds(businessIds));
					orSpec = Specifications.where(orSpec).or(gmbSpecification.hasBusinessIdNullOrNotNull(true));
					spec = Specifications.where(spec).and(orSpec);
				}
			}
		} else {
			if(MappingStatus.MAPPED.equals(mappingStatus)) {
				return new PageImpl<>(new ArrayList<>());
			} else {
				spec = Specifications.where(spec).and(gmbSpecification.hasBusinessIdNullOrNotNull(true));
			}
		}
		if(PageConnectionStatus.CONNECTED.equals(pageConnectionStatus)) {
			spec = Specifications.where(spec).and(gmbSpecification.isValid(1));
			if(Objects.nonNull(isSelected)) {
				spec = Specifications.where(spec).and(gmbSpecification.isSelected(isSelected));
			}
		} else if(PageConnectionStatus.DISCONNECTED.equals(pageConnectionStatus)) {
			spec = Specifications.where(spec).and(gmbSpecification.isValid(0));
		} else {
			spec = Specifications.where(spec).and(gmbSpecification.isSelected(isSelected));
		}
		if(CollectionUtils.isNotEmpty(createdByIds)) {
			spec = Specifications.where(spec).and(gmbSpecification.inCreatedByIds(createdByIds));
		}
		org.springframework.data.domain.PageRequest pageRequest = null;
		if(ResellerSortType.PAGE_NAME.equals(sortType) && Objects.nonNull(sortDirection)) {
			pageRequest = new org.springframework.data.domain.PageRequest(page, size,
					new Sort(PageSortDirection.ASC.equals(sortDirection)? Sort.Direction.ASC:Sort.Direction.DESC, "locationName"));
		} else if(ResellerSortType.STATUS.equals(sortType) && Objects.nonNull(sortDirection)) {
			pageRequest = new org.springframework.data.domain.PageRequest(page, size,
					new Sort(PageSortDirection.ASC.equals(sortDirection)? Sort.Direction.DESC:Sort.Direction.ASC, "isValid")
					.and(new Sort(Sort.Direction.ASC, "locationName")));
		} else {
			spec = Specifications.where(spec).and(gmbSpecification.sortBusinessIdNullsFirst());
			pageRequest = new org.springframework.data.domain.PageRequest(page, size);
		}

		return socialGMBRepo.findAll(spec, pageRequest);
	}

	public Page<BusinessGoogleMyBusinessLocation> search(String search, Pageable page,Long resellerId) {
		Specification<BusinessGoogleMyBusinessLocation> gmb =
				Specifications.where((gmbSpecification.hasEmail(search))).
						or(gmbSpecification.hasLocationName(search)).
						or(gmbSpecification.hasSingleLineAddress(search)).
						and(gmbSpecification.isSelected(1)).
						and(gmbSpecification.hasResellerId(resellerId));
		return socialGMBRepo.findAll(gmb,page);
	}


	@Override
	public ConnectPagesResponse getPagesAfterConnect(Long resellerId, Integer size, Integer page) {
		BusinessGetPageRequest req = businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(resellerId,SocialChannel.GMB.getName(),Constants.CONNECT);
		LOGGER.info("Get pages for reseller id :{} and channel :{} and requestId :{}",resellerId,SocialChannel.GMB.getName(),req.getId());
		PaginatedGMBResponse googleMyBusinessLocations = googleMyBusinessPageService.getGMBConnectPages(req.getId().toString(),resellerId,new org.springframework.data.domain.PageRequest(page,size));
		List<ChannelAccountInfo> channelAccountInfos = getAccountInfoForGmb(googleMyBusinessLocations.getPages(),resellerId);
		ConnectPagesResponse response = new ConnectPagesResponse();
		LOGGER.info("Converting to response object for connected pages for request id :{}",req.getId());
		response.setTotalPages(googleMyBusinessLocations.getTotalPages());
		response.setPages(channelAccountInfos);
		response.setTotalElements(googleMyBusinessLocations.getTotalElements());
		return response;
	}

	@Override
	public void updateEnterpriseWithNoReseller() {
		List<BusinessGMBLocationRawRepository.MigrationBFL> enterpriseIds = socialGMBRepo.findByEnterpriseIdIsNotNullAndResellerIdIsNull();
		enterpriseIds.forEach(value ->{
			try {
				BusinessLiteDTO businessLiteDTO= businessCoreService.getBusinessLiteByNumber(value.getEnterpriseId());
				if(Objects.nonNull(businessLiteDTO.getResellerId())  && !Constants.RESELLER_MIGRATION_AVOID_LIST.contains(businessLiteDTO.getResellerId())) {
					BusinessLiteDTO resellerDetails = businessCoreService.getBusinessLite(businessLiteDTO.getResellerId(), false);
					socialGMBRepo.updateResellerId(resellerDetails.getBusinessNumber(), value.getEnterpriseId());
					final String userEmailById;
					if (value.getRequestId().startsWith(OPEN_URL_REQUEST_PREFIX)) {
						userEmailById = businessGetPageOpenUrlReqRepo.findUserEmailById(value.getRequestId());
					} else {
						userEmailById  = businessGetPageReqRepo.findUserEmailById(Integer.valueOf(value.getRequestId()));
					}

					socialGMBRepo.updateUserEmailId(userEmailById, value.getId());

					String logs = "ResellerId " + resellerDetails.getBusinessNumber() + " is updated for enterpriseId: " + value;
					socialErrorMessageService.saveMessageToSocialMigration(SocialChannel.GMB.getName(), logs, false);
				} else {
					String logs = "ResellerId " + businessLiteDTO.getResellerId() + " cannot be updated for enterpriseId " + value + " as it is forbidden";
					socialErrorMessageService.saveMessageToSocialMigration(SocialChannel.GMB.getName(), logs, true);
				}
			}  catch (Exception e) {
				String logs = "Cound not update resellerId for enterprise id " + value + " Error: " + e.getMessage();
				socialErrorMessageService.saveMessageToSocialMigration(SocialChannel.GMB.getName(), logs, true);
			}
		});
	}

	@Override
	public void upgradeNotificationsAPI() {
		LOGGER.info("Inside enable notification for all accounts");
		List<BusinessGMBLocationRawRepository.BL> accounts = socialGMBRepo.findAllByAccountId();
		upgradeNotificationsToNewVersion(accounts);
	}

	public void upgradeNotificationsAPIForTopic() {
		AtomicInteger processed = new AtomicInteger();
		AtomicInteger unprocessed = new AtomicInteger();
		AtomicInteger failed = new AtomicInteger();
		LOGGER.info("Inside enable notification for all accounts");
		String gmbNotificationTopic = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(GMB_NOTIFICATION_TOPIC);
		List<BusinessGMBLocationRawRepository.BL> accounts = socialGMBRepo.findAllByAccountId();
		LOGGER.info("Total account ids : {}",accounts.size());
		accounts.stream().filter(id -> Objects.nonNull(id.getAccountId())).forEach(account -> {
			try {
				String accessToken = googleAuthenticationService.getGoogleAccessToken(account.getRefreshTokenId());
				GMBNotificationResponse request = googleService.getNotificationsNewVersion(accessToken, account.getAccountId());
				if(Objects.nonNull(request.getNotificationTypes())){
					EnableGMBNotificationRequest gmbNotificationRequest = new EnableGMBNotificationRequest(account.getAccountId(), accessToken, gmbNotificationTopic, request.getNotificationTypes());
					googleService.enableGMBNotificationNewVersion(gmbNotificationRequest);
					LOGGER.info("processed : {},unproccessed {},failed {}",processed.incrementAndGet(),unprocessed.get(),failed.get());
				}else{
					LOGGER.info("processed : {},unproccessed {} , failed {}",processed.get(),unprocessed.incrementAndGet(),failed.get());

				}
			}catch (Exception e){
				LOGGER.info("processed : {},unproccessed {},failed {}",processed.get(),unprocessed.get(),failed.incrementAndGet());
			}
		});
		LOGGER.info("Total accounts : {} ,processed {},unprocessed {},failed {}",accounts.size(),processed.get(),unprocessed.get(),failed.get());
	}

	public void upgradeNotificationsToNewVersion(List<BusinessGMBLocationRawRepository.BL> accounts){
		LOGGER.info("Total accounts : {}",accounts.size());
		AtomicInteger index = new AtomicInteger();
		AtomicInteger failed = new AtomicInteger();
		AtomicInteger unprocessed = new AtomicInteger();

		String gmbNotificationTopic = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getProperty(GMB_NOTIFICATION_TOPIC);
		accounts.stream().filter(id -> Objects.nonNull(id.getAccountId())).forEach(account -> {
			try {
				String accessToken = googleAuthenticationService.getGoogleAccessToken(account.getRefreshTokenId());
				GMBNotificationResponse request = googleService.getNotifications(accessToken, account.getAccountId());
				List<String> types = request.getNotificationTypes();
				if (Objects.nonNull(request.getNotificationTypes()) && gmbNotificationTopic.equalsIgnoreCase(request.getTopicName())) {
					String status = googleService.disableGMBNotificationOldVersion(account.getAccountId(), gmbNotificationTopic, accessToken);
					for (int i=0;i<types.size();i++){
						if(types.get(i).equalsIgnoreCase("UPDATED_LOCATION_STATE")){
							types.remove(i);
						}
					}
					if(GMBNotificationStatus.SUCCESS.toString().equalsIgnoreCase(status)){
						EnableGMBNotificationRequest gmbNotificationRequest = new EnableGMBNotificationRequest(account.getAccountId(), accessToken, gmbNotificationTopic, types);
						googleService.enableGMBNotificationNewVersion(gmbNotificationRequest);
						LOGGER.info("Total accounts : {} processed {},unprocessed {},Failed count : {}",accounts.size(), index.incrementAndGet(),unprocessed.get(),failed.get());
					}
				} else{
					LOGGER.info("Total accounts : {} processed {},unprocessed {},Failed count : {} ",accounts.size(), index.get(),unprocessed.incrementAndGet(),failed.get());
				}
			} catch (Exception e) {
				LOGGER.info("Total accounts : {} ,processed {},unprocessed {}, Failed count : {}",accounts.size(),index.get(),unprocessed.get(),failed.incrementAndGet());
				LOGGER.error("Unable to upgrade notification version for account id : {} and refresh token id : {}", account.getAccountId(), account.getRefreshTokenId());
			}
		});
		LOGGER.info("Total accounts : {} ,processed {},unprocessed {}, Failed count : {}",accounts.size(),index.get(),unprocessed.get(),failed.get());
	}

	public void validityCheckForAllGMBPages() {
		List<BusinessGoogleMyBusinessLocation> pages = socialGMBRepo.findAll();
		List<BusinessGoogleMyBusinessLocation> businessLocations = new ArrayList<>();
		Map<Long,List<BusinessGoogleMyBusinessLocation>> mapPages = generateMapForGoogleMessageEnabled(pages);
		if(Objects.nonNull(mapPages)){
			for (Map.Entry<Long,List<BusinessGoogleMyBusinessLocation>> map : mapPages.entrySet()){
				try {
					Long enterpriseId = map.getKey();
					Boolean googleMessageEnabled = false;
					if(Objects.nonNull(enterpriseId)){
						googleMessageEnabled = isGoogleMessageEnabled(null,enterpriseId);
					}
					Boolean finalGoogleMessageEnabled = googleMessageEnabled;
					map.getValue().forEach(page -> businessLocations.add(getValidity(page,finalGoogleMessageEnabled)));
				}catch (Exception e){
					LOGGER.info("Exception from core {}",e.getMessage());
				}
			}
		}
		socialGMBRepo.save(businessLocations);
		LOGGER.info("Successfully saved to db and populated check validity column");
	}

	@Override
	public void upgradeNotifications(List<String> accountIds) {
		LOGGER.info("Inside enable notification for accountIds : {}",accountIds);
		List<BusinessGMBLocationRawRepository.BL> accounts = socialGMBRepo.findByAccountIds(accountIds);
		upgradeNotificationsToNewVersion(accounts);
	}

	@Override
	public void updateLocationStateForPages(Long enterpriseId) {
		LOGGER.info("Update location state for enterprise id : {}",enterpriseId);
		List<String> pages = socialGMBRepo.findPlaceIdsForEnterpriseId(enterpriseId);
		UpdateLocationStateRequest request = new UpdateLocationStateRequest(enterpriseId,pages,false);
		updateLocationState(request);
//		placeIds = pages.stream().map(BusinessGoogleMyBusinessLocation::getPlaceId).filter(Objects::nonNull).collect(Collectors.toList());
//		UpdateLocationStateRequest request = new UpdateLocationStateRequest(enterpriseId,placeIds);
//		updateLocationState(request);
	}

	@Override
	public void updateLocationStateByAgentId(Integer agentId) {
		LOGGER.info("Update location state for agentId id : {}",agentId);
		List<BusinessGoogleMyBusinessLocation> pages = socialGMBRepo.findByAgentIdAndIsSelected(agentId,1);
		if(CollectionUtils.isEmpty(pages)){
			return;
		}
		List<String> placeIds = pages.stream().map(BusinessGoogleMyBusinessLocation::getPlaceId).filter(Objects::nonNull).collect(Collectors.toList());
		UpdateLocationStateRequest request = new UpdateLocationStateRequest(pages.get(0).getEnterpriseId(),placeIds,false);
		updateLocationState(request);
//		if(CollectionUtils.isNotEmpty(pages)){
//			Long enterpriseId = pages.get(0).getEnterpriseId();
//
//			UpdateLocationStateRequest request = new UpdateLocationStateRequest(enterpriseId, placeIds);
//			updateLocationStateByAgentId(request, agentId);
//			LOGGER.info("Synced the location state with google for enterprise id : {}",enterpriseId);
//			pushgMsgLocationLaunchInFirebase(enterpriseId,GoogleAgentStatus.UN_LAUNCHED.name());
//		}
	}

	@Override
	public Map<Long ,List<BusinessGoogleMyBusinessLocation>> generateMapForGoogleMessageEnabled(List<BusinessGoogleMyBusinessLocation> pages){
		Map<Long ,List<BusinessGoogleMyBusinessLocation>> mapPages = pages.stream().filter(page -> Objects.nonNull(page.getEnterpriseId())).collect(groupingBy(BusinessGoogleMyBusinessLocation::getEnterpriseId));

		// for null enterpriseIds
		List<BusinessGoogleMyBusinessLocation> gmbPages = pages.stream().filter(page -> Objects.isNull(page.getEnterpriseId())).collect(Collectors.toList());
		mapPages.put(null,gmbPages);
		return mapPages;
	}

	@Override
	public Map<String ,List<BusinessGoogleMyBusinessLocation>> generateMapForGoogleAccountId(List<BusinessGoogleMyBusinessLocation> pages){
		return pages.stream().filter(page -> Objects.nonNull(page.getAccountId())).collect(groupingBy(BusinessGoogleMyBusinessLocation::getAccountId));
	}

	@Override
	public BusinessGoogleMyBusinessLocation getValidity(BusinessGoogleMyBusinessLocation page,Boolean finalGoogleMessageEnabled){
		Validity validity = fetchValidityAndErrorMessage(page, finalGoogleMessageEnabled);
		if (Objects.nonNull(validity.getValidType())) {
			if (validity.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName())) {
				page.setValidType(ValidTypeEnum.PARTIAL_VALID.getId());
			} else if (validity.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())) {
				page.setValidType(ValidTypeEnum.VALID.getId());
			} else {
				page.setValidType(ValidTypeEnum.INVALID.getId());
			}
		}
		return page;
	}

	@Override
	public void removePageMap(String name, Integer businessId) {
		List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = socialGMBRepo.findAllByBusinessIdIn(Collections.singletonList(businessId));
		if(CollectionUtils.isEmpty(businessGoogleMyBusinessLocations)) {
			LOGGER.info("No page found for business id : {}",businessId);
			return;
		}
		businessGoogleMyBusinessLocations.forEach(page ->{
			LOGGER.info("Remove mapping for page id :{}",page.getLocationId());
			commonService.removeMapping(page.getBusinessId(),page.getLocationId(),SocialChannel.GMB.getName(), null);
		});
	}

	@Override
	public List<SocialNotificationAudit> auditNotifications(Object notificationObject) {
		SocialNotificationAudit audit = new SocialNotificationAudit();
		try {
			SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
			SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			ObjectMapper mapper = new ObjectMapper();
			JsonNode node = mapper.convertValue(notificationObject, JsonNode.class);
			audit.setChannel(Constants.GMB);
			for (Iterator<String> it = node.fieldNames(); it.hasNext(); ) {
				String s = it.next();
				switch (s) {
					case "locationId":
						audit.setEvent_id(node.get("locationId").asText());
						break;
					case "eventData":
						String[] dataArray = node.get(s).asText().split(",");
						for(String data : dataArray){
							if(data.contains("publishTime")){
								String publishedAt = data.substring(data.lastIndexOf("=") + 1).trim();
								Date date = inputFormat.parse(publishedAt);
								audit.setPublishedAt(outputFormat.format(date));
								break;
							}
						}
						break;
					case "reviewType":
						audit.setReview_type(node.get("reviewType").asText());
						break;
					case "agent":
						audit.setAgent_id(node.get("agent").asText());
						break;
					case "sourceReviewId":
						audit.setReview_id(node.get("sourceReviewId").asText());
						audit.setEvent_type(NotificationAuditTypes.GMB_REVIEW_NOTIFICATION.name());
						break;
					case "conversationId":
						audit.setConversation_id(node.get("conversationId").asText());
						if(node.get("message").isNull()) {
							audit.setEvent_type(NotificationAuditTypes.GMB_MESSAGE_NOTIFICATION_ACKNOWLEDGEMENT.name());
						} else {
							audit.setEvent_type(NotificationAuditTypes.GMB_MESSAGE_NOTIFICATION.name());
						}
						break;
					case "context":
						JsonNode reason = node.get(s);
						String res = reason.get("placeId").asText();
						audit.setEvent_id(res);
						break;
					case "receipts":
						JsonNode receiptType = node.get(s);
						JsonNode receipts = receiptType.get("receipts");
						if(Objects.nonNull(receipts)) {
							String finalReceipt = receipts.get(0).get("receiptType").asText();
							audit.setMessage_type(finalReceipt);
						}
						break;
				}
			}
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			audit.setCreated_at(simpleDateFormat.format(new Date()));
			audit.setEvent_payload(JSONUtils.toJSON(notificationObject));
		}catch (Exception e ){
			LOGGER.info("Unable to convert data to audit :{} and exception : {}",notificationObject,e.getLocalizedMessage());
		}
		return Collections.singletonList(audit);
	}

	private ChannelPageDetails sortInvalidAndValidPage(List<ChannelPages> pageInfo) {
		ChannelPageDetails channelPageDetails = new ChannelPageDetails();
		Map<Boolean, List<ChannelPages>> partitions = pageInfo.stream().collect(Collectors.partitioningBy(this::isPageInfoDisconnected));

		List<ChannelPages> invalidPages = partitions.get(true);
		channelPageDetails.setDisconnected(invalidPages.size());
		List<ChannelPages> validPages = partitions.get(false);
		invalidPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
		validPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
		invalidPages.addAll(validPages);
		channelPageDetails.setPages(invalidPages);
		return channelPageDetails;
	}

	private ChannelPageDetails sortInvalidAndValidPageForReseller(List<ChannelPages> pageInfo,Long resellerId) {
		ChannelPageDetails channelPageDetails = new ChannelPageDetails();
		Map<Boolean, List<ChannelPages>> partitions = pageInfo.stream().collect(Collectors.partitioningBy(this::isPageInfoDisconnected));
		List<String> getInvalidPageIds = socialGMBRepo.findByResellerIdAndValidType(resellerId,Arrays.asList(ValidTypeEnum.INVALID.getId(),ValidTypeEnum.PARTIAL_VALID.getId()));
		List<ChannelPages> invalidPages = partitions.get(true);
		channelPageDetails.setDisconnected(getInvalidPageIds.size());
		List<ChannelPages> validPages = partitions.get(false);
		invalidPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
		validPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
		invalidPages.addAll(validPages);
		channelPageDetails.setPages(invalidPages);
		return channelPageDetails;
	}

	// TODO: Document it
	private GoogleAuthToken getExistingValidToken(String userId, SocialAppCredsInfo domainGoogleInfo) {
		GoogleAuthToken token = null;
		/*
		 * Google refresh token is generated once for each user. The next time user logs into same app, response will not have refresh token but only the
		 * access token.
		 */
		List<GoogleRefreshToken> savedRefreshTokens = googleRefreshTokenRepo.getByUserIdAndValidAndType(userId, "social");
		LOGGER.info("For user {} valid social saved Refresh Tokens {}", userId, (savedRefreshTokens != null ? savedRefreshTokens.size() : 0));

		// Still have this check for tokens if for that user we don't have any valid
		if (CollectionUtils.isEmpty(savedRefreshTokens)) {
			LOGGER.error("[GooglePlus Social] no token with isValid = 1");
			List<GoogleRefreshToken> refreshTokens = googleRefreshTokenRepo.findByGoogleUserIdAndTypeAndChannelIsNull(userId, "social");
			LOGGER.info("For user {} social Refresh Tokens {}", userId, (refreshTokens != null ? refreshTokens.size() : 0));
			if (CollectionUtils.isNotEmpty(refreshTokens)) {
				for (GoogleRefreshToken refToken : refreshTokens) {
					try {
						token = googleAuthenticationService.generateGoogleTokensUsingRefreshToken(domainGoogleInfo.getChannelClientId(), domainGoogleInfo.getChannelClientSecret(), refToken.getRefreshToken());
						if (token != null) {
							LOGGER.info("For user {} got the Tokens {}", userId, token);
							refToken.setIsValid(1);
							GoogleRefreshToken saveToken = googleRefreshTokenRepo.saveAndFlush(refToken);
							token.setRefreshTokenId(saveToken.getId());
							break;
						}
					} catch (Exception e) {
						LOGGER.info("[GooglePlus Social] could not create access token for refresh token id {}", refToken.getId());
					}
				}
			}
		} else {
			Integer refreshTokenId = null;
			for (GoogleRefreshToken savedRefToken : savedRefreshTokens) {
				try {
					token = googleAuthenticationService.generateGoogleTokensUsingRefreshToken(domainGoogleInfo.getChannelClientId(), domainGoogleInfo.getChannelClientSecret(), savedRefToken.getRefreshToken());
					if (token != null) {
						LOGGER.info("For user {} got the Tokens {}", userId, token);
						refreshTokenId = savedRefToken.getId();
						break;
					}

					LOGGER.info("For user {} valid Token {} found ", userId, token);
				} catch (Exception e) {
					LOGGER.info("[GooglePlus Social] could not create access token for refresh token id {}", savedRefToken.getId());
					// savedRefToken.setIsValid(0);
					// googleRefreshTokenRepository.saveAndFlush(savedRefToken);
				}
			}
			if (refreshTokenId != null) {
				token.setRefreshTokenId(refreshTokenId);
			}
		}
		LOGGER.info("in getExistingValidToken For user {} GoogleAuthToken is : {} ", userId, token);
		return token;
	}

	@Override
	public void updateGMBLocationIsValidStatus(String locationId, Integer isValid) {
		LOGGER.info("For GMB location {} marking the status {}", locationId, isValid);
		googleMyBusinessPageService.updateGMBLocationIsValidStatus(locationId, isValid);

	}

	@Override
	public void submitFetchPageRequest(Long parentId, Integer birdeyeUserId, String googlePlusCode, String redirectUri) throws Exception {
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(parentId));
		boolean lock = redisService.tryToAcquireLock(key);
		BusinessGetPageRequest request  = businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId, SocialChannel.GMB.getName(),CONNECT);

		LOGGER.info("[Redis Lock] Lock status : {} with key {}", lock, key);
		if(lock) {
			try {
				GoogleAuthToken token = getGoogleToken(parentId, googlePlusCode, redirectUri);
				LOGGER.info("generated  GoogleAuthToken = {}", token);
				List<String> statusList = Arrays.asList(Status.INITIAL.getName(), Status.FETCHED.getName());
				if (Objects.isNull(request) || !statusList.contains(request.getStatus())) {
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(),CONNECT,Status.INITIAL.getName(),parentId);
					BusinessGetPageRequest businessGetPageRequest = submitGmbRequest(parentId, birdeyeUserId, token,Status.INITIAL.getName(),Constants.ENTERPRISE);
					googleMyBusinessPageService.fetchGmbLocations(businessGetPageRequest, token);
				}else{
					LOGGER.info("[GMB] BusinessGetPageRequest found with status init or fetched for enterprise id {}",parentId);
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),request.getStatus(),parentId);
					redisService.release(key);
				}
			}catch(Exception e) {
				// Cleanup redis cache for error cases. e.g. Google Access Token
				redisService.release(key);
				LOGGER.error("[Redis Lock] (Google) Lock released for business {} , exception {}", parentId,e);
				BusinessGetPageRequest reqGMB = checkInProgressRequestsGMB(parentId, CONNECT);
				if (reqGMB != null) {
					reqGMB.setStatus(Status.CANCEL.getName());
					businessGetPageReqRepo.saveAndFlush(reqGMB);
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(),reqGMB.getRequestType(),Status.COMPLETE.getName(),parentId,true);
				}else{
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(),CONNECT,Status.COMPLETE.getName(),parentId,true);
				}
			}
		} else {
			LOGGER.info("[Redis Lock] (Google) Lock is already acquired for business {}", parentId);
			handleFailureLock(request,SocialChannel.GMB.getName(), key,parentId,CONNECT);
		}
	}

	@Override
	public OpenUrlFetchPageResponse submitFetchPageRequestForOpenURL(Long businessId, ChannelAuthOpenUrlRequest authRequest) throws Exception {
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(authRequest.getFirebaseKey()));
		boolean lock = redisService.tryToAcquireLock(key);
		LOGGER.info("[Redis Lock] Lock status : {}",lock);
		if(lock) {
			try {
				GoogleAuthToken token = getGoogleToken(businessId, authRequest.getGooglePlusCode(), authRequest.getRedirectUri());
				LOGGER.info("generated  GoogleAuthToken = {}", token);
				String channel = SocialChannel.GMB.getName();
				return submitGmbRequestForOpenUrl(businessId, token, authRequest.getFirebaseKey());
			} catch(Exception e) {
				// Cleanup redis cache for error cases. e.g. Google Access Token
				handleCleanupRedisForOpenurl(key, authRequest, businessId);
			}
		} else {
			LOGGER.info("[Redis Lock] (Google) Lock is already acquired for business {}", businessId);
		}
		OpenUrlFetchPageResponse openUrlFetchPageResponse = new OpenUrlFetchPageResponse();
		openUrlFetchPageResponse.setFirebaseKey(authRequest.getFirebaseKey());
		return null; // To confirm
	}

	private BusinessGetPageRequest submitGmbRequest(Long businessId, Integer birdeyeUserId, GoogleAuthToken token,String status,String type) {
		BusinessGetPageRequest gmbRequest = new BusinessGetPageRequest();
		gmbRequest.setBirdeyeUserId(birdeyeUserId);
		gmbRequest.setSocialUserId(token.getUserId());
		gmbRequest.setChannel(SocialChannel.GMB.getName());
		if(type.equalsIgnoreCase(Constants.RESELLER)){
			gmbRequest.setResellerId(businessId);
		}else{
			gmbRequest.setEnterpriseId(businessId);
		}
		gmbRequest.setStatus(status);
		gmbRequest.setPageCount(0);
		gmbRequest.setRequestType(CONNECT);
		gmbRequest.setEmail(token.getEmail());
		return businessGetPageReqRepo.saveAndFlush(gmbRequest);
	}

	private BusinessGetPageOpenUrlRequest submitGmbOpenUrlRequest(Long businessId, GoogleAuthToken token, String status,String fireBaseKey) {
		BusinessGetPageOpenUrlRequest gmbRequest = new BusinessGetPageOpenUrlRequest();
		gmbRequest.setSocialUserId(token.getUserId());
		gmbRequest.setChannel(SocialChannel.GMB.getName());
		gmbRequest.setEnterpriseId(businessId);
		gmbRequest.setStatus(status);
		gmbRequest.setPageCount(0);
		gmbRequest.setRequestType(CONNECT);
		gmbRequest.setEmail(token.getEmail());
		gmbRequest.setFirebaseKey(fireBaseKey);
		return businessGetPageOpenUrlReqRepo.saveAndFlush(gmbRequest);
	}

	private BusinessGetPageRequest submitFreemiumRequest(GoogleAuthToken token,String status,String type,Integer sessionId) {
		BusinessGetPageRequest gmbRequest = new BusinessGetPageRequest();
		gmbRequest.setSocialUserId(token.getUserId());
		gmbRequest.setChannel(SocialChannel.GMB.getName());
		gmbRequest.setStatus(status);
		gmbRequest.setPageCount(0);
		gmbRequest.setRequestType(CONNECT);
		gmbRequest.setEmail(token.getEmail());
		gmbRequest.setFreemiumSessionId(sessionId);
		return businessGetPageReqRepo.saveAndFlush(gmbRequest);
	}

	private BusinessGetPageRequest submitGmbResellerRequest(Long businessId, Integer birdeyeUserId, GoogleAuthToken token,String status) {
		BusinessGetPageRequest gmbRequest = new BusinessGetPageRequest();
		gmbRequest.setBirdeyeUserId(birdeyeUserId);
		gmbRequest.setSocialUserId(token.getUserId());
		gmbRequest.setChannel(SocialChannel.GMB.getName());
		gmbRequest.setResellerId(businessId);
		gmbRequest.setStatus(status);
		gmbRequest.setPageCount(0);
		gmbRequest.setRequestType(CONNECT);
		gmbRequest.setEmail(token.getEmail());
		return businessGetPageReqRepo.saveAndFlush(gmbRequest);
	}

	private OpenUrlFetchPageResponse submitGmbRequestForOpenUrl(Long businessId, GoogleAuthToken token,
			String fireBaseKey) {
		BusinessGetPageOpenUrlRequest gmbRequest = new BusinessGetPageOpenUrlRequest();
		gmbRequest.setSocialUserId(token.getUserId());
		gmbRequest.setChannel(SocialChannel.GMB.getName());
		gmbRequest.setEnterpriseId(businessId);
		gmbRequest.setStatus(Status.INITIAL.getName());
		gmbRequest.setPageCount(0);
		gmbRequest.setRequestType(CONNECT);
		gmbRequest.setEmail(token.getEmail());
		gmbRequest.setFirebaseKey(fireBaseKey);
		businessGetPageOpenUrlReqRepo.saveAndFlush(gmbRequest);
		nexusService.insertMapInFirebase("socialOpenUrl/"+ fireBaseKey,fireBaseKey,Status.INITIAL.getName());
		googleMyBusinessPageService.fetchGmbLocations(gmbRequest, token);
		OpenUrlFetchPageResponse openUrlFetchPageResponse = new OpenUrlFetchPageResponse();
		openUrlFetchPageResponse.setFirebaseKey(fireBaseKey);
		openUrlFetchPageResponse.setEmail(token.getEmail());
		return openUrlFetchPageResponse;

	}

	private List<BusinessGetPageRequest> getRequestForBusiness(Long businessId, String status, String channel, String requestType) {
		return businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannelAndRequestType(businessId, status, channel, requestType);
	}

	private List<BusinessGetPageRequest> getRequestForReseller(Long resellerId,String status, String channel, String requestType){
		return businessGetPageReqRepo.findByResellerIdAndStatusAndChannelAndRequestType(resellerId, status, channel, requestType);
	}

	private List<BusinessGetPageOpenUrlRequest> getRequestForBusinessOpenUrl(Long businessId,String status, String channel, String requestType, String firebaseKey){
		return businessGetPageOpenUrlReqRepo.findByEnterpriseIdAndStatusAndChannelAndRequestTypeAndFirebaseKey(businessId, status, channel, requestType, firebaseKey);
	}
	private BusinessGetPageRequest getRequestForBusinessWithoutStatus(Long businessId, String channel, String requestType) {

		List <BusinessGetPageRequest> gmbData = businessGetPageReqRepo.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId, channel, requestType);

		if (CollectionUtils.isNotEmpty(gmbData)) {
			return gmbData.get(0);
		} else {
			return null;
		}
	}


	private Boolean isUnderProcess(Long businessId, String requestType) {
		List<BusinessGetPageRequest> underProcessGMB = getRequestForBusiness(businessId, Status.INITIAL.getName(), SocialChannel.GMB.getName(), requestType);
		return CollectionUtils.isNotEmpty(underProcessGMB);
	}

	private Boolean isFetched(Long businessId, Map<String, BusinessGetPageRequest> requestMap, String requestType) {
		LOGGER.info("in isFetched method for business = {}", businessId);
		if (CONNECT.equalsIgnoreCase(requestType)) {
			Boolean gmb = Boolean.FALSE;
			List<BusinessGetPageRequest> fetchedRequestGMB = getRequestForBusiness(businessId, Status.FETCHED.getName(), SocialChannel.GMB.getName(), requestType);

			if (CollectionUtils.isNotEmpty(fetchedRequestGMB)){
				gmb = Boolean.TRUE;
				requestMap.put(SocialChannel.GMB.getName(), fetchedRequestGMB.get(0));
			}
			return (gmb);
		}
		return false;
	}

	public Map<String, List<ChannelAccountInfo>> getPages(BusinessGetPageRequest request, Long enterpriseId){
		LOGGER.info("[GMB] getPages for request id {}",request.getId());
		Map<String, List<ChannelAccountInfo>> pageTypes = new HashMap<>();
		List<ChannelAccountInfo> gmbPages = getGoogleMyBusinessPages(request, enterpriseId);
		if(CollectionUtils.isNotEmpty(gmbPages)){
			pageTypes.put(SocialChannel.GMB.getName(), gmbPages);
		}
		return pageTypes;
	}

	private List<ChannelAccountInfo> getGoogleMyBusinessPages(BusinessGetPageRequest gmbReq, Long enterpriseId){
		List<ChannelAccountInfo> gmbAcctInfo = null;
		if(gmbReq == null)
			return Collections.emptyList();

		List<BusinessGoogleMyBusinessLocation> gmbLocations;
		// if two step and not null -> fetch by accountID
		// if two step and null -> that mean old converted to new follow -> fetch by requestId only : this will happen only one time till user again connect
		// if not on two step -> then fetch by requestId only old follow
		if(showAccount(gmbReq.getEnterpriseId())){
			if(gmbReq.getGmbAccount()!=null){
				GoogleAccount googleAccount = null;
				List<GoogleAccount> googleAccountList = googleAccountService.findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(gmbReq.getGmbAccount(), gmbReq.getId().toString());
				if(CollectionUtils.isNotEmpty(googleAccountList)) {
					googleAccount = googleAccountList.get(0);
				}
				if(Objects.isNull(googleAccount)){
					return gmbAcctInfo;
				}else{
					gmbLocations = googleMyBusinessPageService.getGMBPagesByRequestIdAndGmbAccountId(gmbReq.getId().toString(),googleAccount.getId());
				}
			}else{
				gmbLocations = googleMyBusinessPageService.getGMBPagesByRequestId(gmbReq.getId().toString());
			}
		}else{
			gmbLocations = googleMyBusinessPageService.getGMBPagesByRequestId(gmbReq.getId().toString());
		}
		if(CollectionUtils.isNotEmpty(gmbLocations))
			gmbAcctInfo = getAccountInfoForGmb(gmbLocations,enterpriseId);
		return gmbAcctInfo;
	}

	@Override
	public void cancelRequest(String channel, Long businessId, Boolean forceCancel) {
		BusinessGetPageRequest req =  businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId, SocialChannel.GMB.getName(),CONNECT);
		if(Objects.isNull(req)) {
			LOGGER.error("No record found in business get page request for businessId: {}", businessId);
			return;
		}
		if(!forceCancel && Status.INITIAL.getName().equals(req.getStatus())) {
			throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, Constants.INIT_TO_CANCEL_ERROR_REQUEST_MESSAGE);
		}
		if(req.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName())){
			redisService.release(SocialChannel.GOOGLE_PLUS_GMB.getName().concat("_group_").concat(String.valueOf(businessId)));
		}else{
			releaseLock(businessId);
		}
		req.setStatus(Status.CANCEL.getName());
		req.setUpdated(new Date());
		businessGetPageReqRepo.saveAndFlush(req);
		pushCheckStatusInFirebase(SocialChannel.GMB.getName(),req.getRequestType(),Status.COMPLETE.getName(), req.getEnterpriseId());
	}

	private void releaseLock(Long businessId) {
		redisService.release(SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(businessId)));
	}

	@Override
	public OpenUrlPagesInfo connectPagesFetchedByOpenUrl(Long enterpriseId, OpenUrlConnectRequest connectRequest, Integer userId) throws Exception {
		LOGGER.info("connectPagesFetchedByOpenUrl : enterpriseId {} connectRequest {} userId {}", enterpriseId, connectRequest, userId);
		if ( enterpriseId == null || connectRequest == null || StringUtils.isEmpty(connectRequest.getFirebaseKey()) || CollectionUtils.isEmpty(connectRequest.getPageRequests()) ) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for enterpriseId/connectRequest/userId");
		}
		final String firebaseKey = connectRequest.getFirebaseKey();
		final List<String> pageIds = connectRequest.getPageRequests().stream().map(PageRequest::getId).collect(Collectors.toList());
		OpenUrlPagesInfo response = new OpenUrlPagesInfo();
		List<BusinessGetPageOpenUrlRequest> gmbRequests = getRequestForOpenUrlBusiness(enterpriseId, Status.FETCHED.getName(), SocialChannel.GMB.getName(), CONNECT, firebaseKey);
		if ( gmbRequests == null || gmbRequests.isEmpty() ) {
			// case of no request found
			LOGGER.error("connectPagesFetchedByOpenUrl : No rows found with fetched status for pageIds {} enterpriseId {} firebaseKey {}", pageIds, enterpriseId, firebaseKey);
			throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "Seems request status has already changed");
		} else if ( gmbRequests.size() > 1 ) {
			// case of multiple requests present
			LOGGER.error("connectPagesFetchedByOpenUrl : Multiple rows found with fetched status for pageIds {} and enterpriseId {} firebaseKey {}", pageIds, enterpriseId, firebaseKey);
			throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "Multiple rows with fetched status found in BusinessGetPageOpenUrlRequest");
		} else {
			List<BusinessGoogleMyBusinessLocation> gmbPages = socialGMBRepo.findByLocationIdIn(pageIds);
			if ( CollectionUtils.isEmpty(gmbPages) ) {
				LOGGER.error("connectPagesFetchedByOpenUrl : No GMB pages found with pageIds {}", pageIds);
				throw new BirdeyeSocialException(ErrorCodes.GMB_PAGE_NOT_FOUND, "GMB Pages not found with given pageIds");
			}

			// Get details of business using core API
			// TODO: Call service method here
			BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
			businessLiteRequest.setKey("businessNumber");
			businessLiteRequest.setValue(enterpriseId);
			BusinessLiteDTO business = businessCoreService.getBusinessLite(businessLiteRequest);
			if ( Objects.isNull(business) ) {
				throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "Business not found using Business Lite API");
			}

			final boolean isBusinessNotMapped = isBusinessNotMappedToGMBPage(business);
			final boolean isSmbAndMapped = checkBusinessSMB(business) && !isBusinessNotMapped;
			LOGGER.info("connectPagesFetchedByOpenUrl: checkBusinessSMB {} isBusinessNotMapped {} invalidSmbCase {}", checkBusinessSMB(business), isBusinessNotMapped, isSmbAndMapped);
			List<BusinessGoogleMyBusinessLocation> existingPages = new ArrayList<>();
			List<String> newPages = new ArrayList<>();
			// TODO: Check if this code can be made simpler
			for ( BusinessGoogleMyBusinessLocation gmbPage : gmbPages ) {
				if (gmbPage.getEnterpriseId() == null && !isSmbAndMapped) { // case of newly found page
					gmbPage.setIsSelected(1);
					gmbPage.setEnterpriseId(enterpriseId);
					gmbPage.setShortAccountId(business.getBusinessId());
					socialGMBRepo.saveAndFlush(gmbPage);
					newPages.add(gmbPage.getLocationId());
				} else if ( gmbPage.getEnterpriseId() != null && gmbPage.getEnterpriseId().equals(enterpriseId) ) { // case of existing page
					gmbPage.setIsValid(googleMyBusinessPageService.isPageValid(gmbPage) ? 1 : 0);
					existingPages.add(gmbPage);
					socialGMBRepo.saveAndFlush(gmbPage);
					brokenIntegrationService.pushValidIntegrationStatus(gmbPage.getEnterpriseId(),SocialChannel.GMB.getName(),gmbPage.getId(),gmbPage.getIsValid(),gmbPage.getLocationId());
				} else if ( isSmbAndMapped ) { // case of smb account already mapped
					// TODO: Call this repo method from service
					setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
							gmbPage.getLocationId(), String.valueOf(userId), gmbPage.toString(), SocialChannel.GOOGLE_PLUS_GMB.getName(),
							"SMB account is already mapped"));
				} else { // case of trying to connect page belonging to different enterpriseId
					setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
							gmbPage.getLocationId(), String.valueOf(userId), gmbPage.toString(), SocialChannel.GOOGLE_PLUS_GMB.getName(),
							"This page is already connected with some other enterpriseId"));
				}
			}

			LOGGER.info("connectPagesFetchedByOpenUrl : Existing mappings to be updated for pages {}", existingPages);
			updateValidStatusOfGMBMappings(existingPages);

			// prepare response
			response.setPageTypes(getAccountInfoForGmb(gmbPages, enterpriseId));
			response.setStatus(Status.COMPLETE.getName());
			response.setStatusType(CONNECT);
			LOGGER.info("connectPagesFetchedByOpenUrl : Connected GMB Pages {}", gmbPages);

			// update request in db
			gmbRequests.get(0).setStatus(Status.COMPLETE.getName());
			gmbRequests.get(0).setUpdated(new Date());
			businessGetPageOpenUrlReqRepo.saveAndFlush(gmbRequests.get(0));
			LOGGER.info("connectPagesFetchedByOpenUrl : Request status updated to complete");

			// try to map business & page if it is valid SMB case
			if (checkBusinessSMB(business) && isBusinessNotMapped) {
				try {
					LOGGER.info("connectPagesFetchedByOpenUrl : Trying to map business {} to pageId {}", business.getBusinessId(), pageIds.get(0));
					saveGMBLocationMapping(business.getBusinessId(), pageIds.get(0), userId,Constants.ENTERPRISE, null);
				} catch (Exception saveMappingException) {
					// we need to return 200 OK response even if mapping fails
					LOGGER.error("connectPagesFetchedByOpenUrl : Failed to map business with page with error {}", saveMappingException.getMessage());
					setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
							pageIds.get(0), String.valueOf(userId), null, SocialChannel.GOOGLE_PLUS_GMB.getName(),
							"SMB mapping failed"));
				}
			}else{
				if(CollectionUtils.isNotEmpty(newPages)){
					LOGGER.info("publishing SOCIAL_PAGE_CONNECT event for gmb open url for enterpriseID {}",enterpriseId);
					SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest(newPages,SocialChannel.GMB.getName());
					kafkaProducer.sendObject(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);
				}
			}
		}

		LOGGER.info("connectPagesFetchedByOpenUrl : Response {}", response);
		return response;
	}

	@Override
	public boolean isBusinessNotMappedToGMBPage(BusinessLiteDTO business) {
		return Objects.isNull(socialGMBRepo.findByBusinessId(business.getBusinessId()));
	}

	private void updateValidStatusOfGMBMappings(List<BusinessGoogleMyBusinessLocation> gmbPages) {
		if ( gmbPages != null && !gmbPages.isEmpty() ) {
			for ( BusinessGoogleMyBusinessLocation gmbPage : gmbPages ) {
				if(gmbPage.getBusinessId()!=null){
					BAMUpdateRequest payload = new BAMUpdateRequest("gmb", gmbPage.getBusinessId(), gmbPage.getLocationMapUrl(),gmbPage.getLocationId(), gmbPage.getPlaceId());

					producer.sendWithKey(Constants.INTEGRATION_TOPIC, String.valueOf(gmbPage.getBusinessId()), payload);
					LOGGER.info("connectPagesFetchedByOpenUrl : Kafka event pushed for gmbLocation id {}", gmbPage.getId());
				}
			}
		}
	}

	private void checkForUnMappedSmbPages(BusinessLiteDTO business) {
		if (checkBusinessSMB(business)) {
			List<BusinessGoogleMyBusinessLocation> existingPages = socialGMBRepo.findByShortAccountId(business.getBusinessId());
			List<BusinessGoogleMyBusinessLocation> mappedPage = existingPages.stream().filter(googleMyBusinessLocation -> Objects.nonNull(googleMyBusinessLocation.getBusinessId())).collect(Collectors.toList());
			List<String> mappedProfileIds = mappedPage.stream().map(BusinessGoogleMyBusinessLocation::getLocationId).collect(Collectors.toList());

			if(CollectionUtils.isNotEmpty(mappedProfileIds)) {
				socialGMBRepo.deleteByShortAccountIdAndLocationIdNotIn(business.getBusinessId(),mappedProfileIds);
			} else {
				socialGMBRepo.deleteByShortAccountId(business.getBusinessId());
			}
		}
	}

	@Override
	public ChannelPageInfo connectGooglePagesV1(Map<String, List<String>> pageIds, Long enterpriseId, Integer accountId) {
		LOGGER.info("connectPages : pageIds : {}, enterpriseId : {}", pageIds, enterpriseId);
		ChannelPageInfo pageInfo = new ChannelPageInfo();
		List<BusinessGetPageRequest> gmbReq = getRequestForBusiness(enterpriseId, Status.FETCHED.getName(), SocialChannel.GMB.getName(),CONNECT);
		Map<String, List<ChannelAccountInfo>> pageTypes = new TreeMap<>(Collections.reverseOrder());
		Boolean autoMappingRequired = checkForAutoMapping(pageIds.get(SocialChannel.GMB.getName()),enterpriseId);
		pageInfo.setAutoMapping(autoMappingRequired);
		if (CollectionUtils.isNotEmpty(pageIds.get(SocialChannel.GMB.getName()))) {
			LOGGER.info("gmb save process :");
			if(CollectionUtils.isEmpty(gmbReq)) {
				LOGGER.info("connectPages : gmbReq request is empty");
				throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "Seems request status has already changed");
			} else if(gmbReq.size() > 1)
				throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "multiple rows with fetched status found");
			else{
				BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(enterpriseId);
				checkForUnMappedSmbPages(business);
				pageTypes.put(SocialChannel.GMB.getName(), connectGoogleMyBusinessPages(pageIds.get(SocialChannel.GMB.getName()), enterpriseId,accountId));
				gmbReq.get(0).setStatus(Status.COMPLETE.getName());
				gmbReq.get(0).setUpdated(new Date());
				businessGetPageReqRepo.saveAndFlush(gmbReq.get(0));
				pushCheckStatusInFirebase(SocialChannel.GMB.getName(),gmbReq.get(0).getRequestType(),Status.COMPLETE.getName(), gmbReq.get(0).getEnterpriseId());
				SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest(pageIds.get(SocialChannel.GMB.getName()),SocialChannel.GMB.getName());
				kafkaProducer.sendObjectV1(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);
			}
		} else if(CollectionUtils.isNotEmpty(gmbReq)){
			LOGGER.info("no g-plus location selected to save :");
			gmbReq.get(0).setStatus(Status.COMPLETE.getName());
			gmbReq.get(0).setUpdated(new Date());
			businessGetPageReqRepo.saveAndFlush(gmbReq.get(0));
			pushCheckStatusInFirebase(SocialChannel.GMB.getName(),gmbReq.get(0).getRequestType(),Status.COMPLETE.getName(), gmbReq.get(0).getEnterpriseId());
		}
		if(autoMappingRequired.equals(true)) {
			triggerAutoMapping(enterpriseId);
		}
		releaseLock(enterpriseId);
		BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
		businessLiteRequest.setKey("businessNumber");
		businessLiteRequest.setValue(enterpriseId);
		BusinessLiteDTO business = businessCoreService.getBusinessLite(businessLiteRequest);
		if (checkBusinessSMB(business) && isBusinessNotMappedToGMBPage(business)) {
			saveGMBLocationMapping(business.getBusinessId(), pageIds.get(SocialChannel.GMB.getName()).get(0), null,Constants.ENTERPRISE, null);
		}
		pageInfo.setPageTypes(pageTypes);
		return pageInfo;
	}

	@Override
	public ChannelPageInfo connectFreemium(FreemiumConnectRequest freemiumConnectRequest){
		List<ChannelAccountInfo> accInfo = new ArrayList<>();
		BusinessGetPageRequest request = businessGetPageService.findByFreemiumSessionId(freemiumConnectRequest.getFreemiumSessionId());
		if(Objects.isNull(freemiumConnectRequest.getLocationId())){
			LOGGER.info("LOCATION ID IS NULL FOR REQUEST :{}",freemiumConnectRequest);
			throw new BirdeyeSocialException(ErrorCodes.LOCATION_ID_NULL);
		}
		BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLiteByNumber(freemiumConnectRequest.getEnterpriseId());
		List<BusinessGoogleMyBusinessLocation> pages=socialGMBRepo.findByLocationId(freemiumConnectRequest.getLocationId());
		if(CollectionUtils.isNotEmpty(pages) && Objects.nonNull(pages.get(0).getEnterpriseId())){
			LOGGER.info("LOCATION ID IS MAPPED FOR DIFFERENT BUSINESS :{}",pages.get(0).getEnterpriseId());
			throw new BirdeyeSocialException((ErrorCodes.LOCATION_IS_MAPPED_AGAINST_DIFFERENT_BUSINESS));
		}
		else if (CollectionUtils.isNotEmpty(pages) && Objects.isNull(pages.get(0).getEnterpriseId())){
			pages.get(0).setBusinessId(freemiumConnectRequest.getBusinessId());
			pages.get(0).setEnterpriseId(freemiumConnectRequest.getEnterpriseId());
			pages.get(0).setShortAccountId(businessLiteDTO.getBusinessId());
			pages.get(0).setIsSelected(1);
			socialGMBRepo.saveAndFlush(pages.get(0));
			Boolean googleMessageEnabled = false;
			if (freemiumConnectRequest.getEnterpriseId() != null) {
				googleMessageEnabled = isGoogleMessageEnabled(null, freemiumConnectRequest.getEnterpriseId());
			}
			accInfo.add(prepareAccountInfo(pages.get(0), freemiumConnectRequest.getEnterpriseId(), googleMessageEnabled));
		}else {
			Boolean googleMessageEnabled = false;
			LOGGER.info("connectPages : locationId : {}, enterpriseId : {}", freemiumConnectRequest.getLocationId(), freemiumConnectRequest.getEnterpriseId());
			List<SocialFreemiumLocations> page = socialFreemiumRepository.findByLocationId(freemiumConnectRequest.getLocationId());
			BusinessGoogleMyBusinessLocation businessPage = createPageInMyBusiessLocations(page.get(0), request.getEmail());
			businessPage.setLocationId(freemiumConnectRequest.getLocationId());
			businessPage.setBusinessId(freemiumConnectRequest.getBusinessId());
			businessPage.setEnterpriseId(freemiumConnectRequest.getEnterpriseId());
			businessPage.setShortAccountId(businessLiteDTO.getBusinessId());
			businessPage.setIsSelected(1);
			socialGMBRepo.saveAndFlush(businessPage);
			if (freemiumConnectRequest.getEnterpriseId() != null) {
				googleMessageEnabled = isGoogleMessageEnabled(null, freemiumConnectRequest.getEnterpriseId());
			}
			ChannelAccountInfo channelAccountInfo=prepareAccountInfo(businessPage, freemiumConnectRequest.getEnterpriseId(), googleMessageEnabled);
			accInfo.add(channelAccountInfo);

			//Todo Add complete status in firebase as well business get page request-completed
			//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), request.getRequestType(), Status.COMPLETE.getName(),
			//		Long.parseLong(freemiumConnectRequest.getFreemiumSessionId().toString()));
			request.setStatus(Status.COMPLETE.getName());
			businessGetPageService.saveAndFlush(request);
		}
		Map<String, List<ChannelAccountInfo>> pageTypes = new HashMap<>();
		pageTypes.put("GMB", accInfo);
		ChannelPageInfo pageInfo = new ChannelPageInfo();
		pageInfo.setPageTypes(pageTypes);
		pageInfo.setAutoMapping(false);
		pageInfo.setStatus(request.getStatus());
		pageInfo.setStatusType(request.getType());
		return pageInfo;
	}

	private BusinessGoogleMyBusinessLocation createPageInMyBusiessLocations(SocialFreemiumLocations info,String email){
		BusinessGoogleMyBusinessLocation page=new BusinessGoogleMyBusinessLocation();
		page.setAccountId(info.getAccountId());
		page.setAccountName(info.getAccountName());
		page.setPlaceId(info.getPlaceId());
		page.setSingleLineAddress(info.getSingleLineAddress());
		page.setCoverImageUrl(info.getCoverImageUrl());
		page.setGooglePlusId(info.getGooglePlusId());
		page.setLocationMapUrl(info.getLocationMapUrl());
		page.setPrimaryPhone(info.getPrimaryPhone());
		page.setRefreshTokenId(info.getRefreshTokenId());
		page.setWebsiteUrl(info.getWebsiteUrl());
		page.setLocationState(info.getLocationState());
		page.setIsValid(isPageValid(info) ? 1 : 0);
		page.setLocationUrl(info.getLocationUrl());
		page.setLocationName(info.getLocationName());
		page.setEmailId(email);
		if(StringUtils.isNotBlank(info.getScope())){
			page.setPermissions(commonService.extractGMBPermission(info.getScope()));
		}
		return page;
	}

	@Override
	public  FreemiumStatusResponse fetchStatusForRequest(Integer sessionId){
		BusinessGetPageRequest request= businessGetPageService.findByFreemiumSessionId(sessionId);
		if (Objects.isNull(request)){
			LOGGER.info("No such freemium session id present :{}",sessionId);
			throw new BirdeyeSocialException(ErrorCodes.SESSION_ID_NOT_FOUND);
		}
		FreemiumStatusResponse statusResponse=new FreemiumStatusResponse();
		statusResponse.setStatus(request.getStatus());
		return statusResponse;
	}

	@Override
	public  ChannelPageInfo connectGooglePagesForReseller(List<String> pageIds, Long resellerId, Boolean selectAll, String searchStr) {
		LOGGER.info("connectPages : pageIds : {}, resellerId : {}", pageIds, resellerId);
		ChannelPageInfo channelPages = new ChannelPageInfo();
		List<BusinessGetPageRequest> gmbReq = getRequestForReseller(resellerId, Status.FETCHED.getName(), SocialChannel.GMB.getName(),CONNECT);
		List<String> locationIds;
		if(selectAll && !searchStr.isEmpty()) {
			locationIds = googleMyBusinessPageService.getGMBLocationIdsByLocationName(searchStr, gmbReq.get(0).getId());
		} else if(selectAll) {
			locationIds = googleMyBusinessPageService.getGMBLocationIdsByRequestId(gmbReq.get(0).getId());
		} else {
			locationIds = pageIds;
		}

		Map<String, List<ChannelAccountInfo>> pageTypes = new TreeMap<>(Collections.reverseOrder());

		if (CollectionUtils.isNotEmpty(locationIds)) {
			LOGGER.info("gmb save process :");
			if(CollectionUtils.isEmpty(gmbReq)) {
				LOGGER.info("connectPages : gmbReq request is empty");
				throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "Seems request status has already changed");
			} else if(gmbReq.size() > 1)
				throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "multiple rows with fetched status found");
			else{
				pageTypes.put(SocialChannel.GMB.getName(), connectGoogleMyBusinessPagesForReseller(locationIds, resellerId, gmbReq.get(0).getEmail()));
				gmbReq.get(0).setStatus(Status.COMPLETE.getName());
				gmbReq.get(0).setUpdated(new Date());
				businessGetPageReqRepo.saveAndFlush(gmbReq.get(0));
				pushCheckStatusInFirebase(SocialChannel.GMB.getName(),gmbReq.get(0).getRequestType(),Status.COMPLETE.getName(), gmbReq.get(0).getResellerId());
				SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest(locationIds,SocialChannel.GMB.getName());
				kafkaProducer.sendObject(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);
			}
		} else if(CollectionUtils.isNotEmpty(gmbReq)){
			LOGGER.info("no gplus location selected to save :");
			gmbReq.get(0).setStatus(Status.COMPLETE.getName());
			gmbReq.get(0).setUpdated(new Date());
			businessGetPageReqRepo.saveAndFlush(gmbReq.get(0));
			pushCheckStatusInFirebase(SocialChannel.GMB.getName(),gmbReq.get(0).getRequestType(),Status.COMPLETE.getName(), gmbReq.get(0).getResellerId());
		}
//		if(autoMappingRequired.equals(true)) {
//			triggerAutoMapping(resellerId);
//		}
		releaseLock(resellerId);
//		BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
//		businessLiteRequest.setKey("businessNumber");
//		businessLiteRequest.setValue(resellerId);
//		BusinessLiteDTO business = businessCoreService.getBusinessLite(businessLiteRequest);
//		if (checkBusinessSMB(business) && isBusinessNotMappedToGMBPage(business)) {
//			saveGMBLocationMapping(business.getBusinessId(), pageIds.get(SocialChannel.GMB.getName()).get(0), null);
//		}
		//todo : userId to be removed from here
//		channelPages.setUserId(gmbReq.get(0).getEmail());
		channelPages.setPageTypes(pageTypes);
		return channelPages;
	}


	private void pushGmbAutoMappingInFirebase(Long enterpriseId, String status) {
		String topicSocial = AUTOMAPPING+SocialChannel.GMB.getName()+"/"+enterpriseId;
		nexusService.insertMapInFirebase(topicSocial,"socialAutoMappingStatus",status);
	}
	@Override
	public Boolean checkForAutoMapping(List<String> pageIds, Long enterpriseId) {
		LOGGER.info("checkForAutoMapping : pageIds : {} and enterprise: {}", pageIds,enterpriseId);
		List<BusinessGoogleMyBusinessLocation> existingPages = socialGMBRepo.findByEnterpriseId(enterpriseId);
		Business business = businessRepo.findByBusinessId(enterpriseId);
		List<Integer> businessIds = businessRepo.findEnterpriseLocations(business.getId());
		if (CollectionUtils.isEmpty(existingPages) && !CollectionUtils.isEmpty(businessIds)) {
			return true;
		}
		return false;
	}

	@Override
	public void triggerAutoMapping(Long enterpriseId) {
		LOGGER.info("triggering auto mapping for enterprise: {}", enterpriseId);
		pushGmbAutoMappingInFirebase(enterpriseId,Status.INITIAL.getName());
		autoMappingService.createAutoMappingEntry(enterpriseId,SocialChannel.GMB.getName());
		AutoMappingRequest autoMappingRequest  = new AutoMappingRequest();
		autoMappingRequest.setEnterpriseId(enterpriseId);
		autoMappingRequest.setChannel(SocialChannel.GMB.getName());
		producer.sendObjectV1(TOPIC_INIT,autoMappingRequest);
	}

	/**
	 * Method to check in progress request for a business in case an exception occurred : GMB
	 * @param businessId
	 * @param requestType
	 * @return
	 */
	private BusinessGetPageRequest checkInProgressRequestsGMB(Long businessId, String requestType) {
		LOGGER.info("Exception occurred in connect gmb, Checking for in progress request for business id {}", businessId);
		List<BusinessGetPageRequest> underProcessRequests = getRequestForReseller(businessId, Status.INITIAL.getName(),SocialChannel.GMB.getName(),requestType);
		if (CollectionUtils.isNotEmpty(underProcessRequests)) {
			return underProcessRequests.get(0);
		}
		return null;
	}



	/* (non-Javadoc)
	 * @see com.birdeye.social.service.GoogleSocialAccountService#removeGMBInactiveIntegration(java.lang.String, java.lang.Long)
	 */
	@Async
	@Override
	public void removeGMBInactiveIntegration(String channel, Long enterpriseId){
		List<BusinessGoogleMyBusinessLocation> existingPages = socialGMBRepo.findByEnterpriseId(enterpriseId);
		LOGGER.info("Count of existing pages found for channel {} and enterpriseId {} : size {}",channel, enterpriseId, existingPages.size());
		if (CollectionUtils.isEmpty(existingPages)) {
			return;
		}
		existingPages.forEach(page ->{
			LOGGER.info("Remove Page for id :{}",page.getLocationId());
			DeleteEventRequest request = DeleteEventRequest.builder()
					.channel(SocialChannel.GMB.getName())
					.pagesIds(Collections.singletonList(page.getLocationId()))
					.build();
			//delete from BusinessFBPage
			kafkaProducer.sendObjectV1(KafkaTopicEnum.SOCIAL_DELETE_PAGE.getName(),request);
		});
	}

	@Deprecated
	private void unLaunchGMBLocation(List<BusinessGoogleMyBusinessLocation> existingPages) {
		List<ChannelPageRemoved> channelPageRemovedList = new ArrayList<>();
		Map<Integer,List<BusinessGoogleMyBusinessLocation>> agentIdPageIdsMap = new HashMap<>();
		if(CollectionUtils.isEmpty(existingPages)){
			return;
		}
		existingPages.forEach(page -> {
			// Un-launch Location
			if(Objects.nonNull(page.getAgentId()) && StringUtils.isNotEmpty(page.getgMsgLocationStatus())
					&& page.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCHED.name())) {
				LOGGER.info("Map agent id {} with place id : {}",page.getAgentId(),page.getPlaceId());
				if(agentIdPageIdsMap.containsKey(page.getAgentId())){
					agentIdPageIdsMap.get(page.getAgentId()).add(page);
				}else {
					agentIdPageIdsMap.put(page.getAgentId(), Collections.singletonList(page));
				}
//						LocationUnlaunchRequest request = new LocationUnlaunchRequest();
//						request.setEnterpriseId(page.getEnterpriseId());
//						request.setProfileId(page.getPlaceId());
//						request.setAgentId(page.getAgentId());
//						unLaunchLocation(request);
			}
			//save into social audit/backup table
			SocialPagesAudit auditPage = getGMBAuditSocialObject(SocialChannel.GMB.getName(),page);
			socialPagesAuditRepo.saveAndFlush(auditPage);
			socialGMBRepo.delete(page);
			socialGMBRepo.flush();
			ChannelPageRemoved channelPageRemoved = new ChannelPageRemoved(SocialChannel.GMB.getName(),
					page.getLocationId(), page.getLocationName(), page.getBusinessId(), page.getEnterpriseId(), page.getPlaceId(), null, page.getId());
			channelPageRemovedList.add(channelPageRemoved);
		});
		if(CollectionUtils.isNotEmpty(channelPageRemovedList)){
			producer.sendObject(Constants.SOCIAL_PAGE_REMOVED,channelPageRemovedList);
		}
		if(CollectionUtils.isEmpty(agentIdPageIdsMap.values())){
			return;
		}
		for(Map.Entry<Integer,List<BusinessGoogleMyBusinessLocation>> map : agentIdPageIdsMap.entrySet()) {
			try {
				LOGGER.info("Agent id to un-launch for id : {}",map.getKey());
				LocationBulkUnlaunchRequest locationBulkUnlaunchRequest = new LocationBulkUnlaunchRequest();
				locationBulkUnlaunchRequest.setAgentId(map.getKey());
				unLaunchLocationByAgentId(locationBulkUnlaunchRequest,map.getValue());
			}catch (Exception e){
				LOGGER.info("Unable to un-launch location for request : {} with error : {}",map,e.getMessage());
			}
		}
	}

	// TODO: 10/01/23 @Navroj -- done
	@Override
	@Deprecated
	public void initUnLaunchAgent(Long enterpriseId) {
		List<GoogleMessagesAgent> gmbAgents = googleMsgAgentService.findByEnterpriseIdIn(enterpriseId);
		if(CollectionUtils.isEmpty(gmbAgents)){
			LOGGER.warn("Location not un-launched for enterprise {}", enterpriseId);
			return;
		}
		gmbAgents.forEach(gmbAgent-> {
			if (gmbAgent.getStatus() != null && gmbAgent.getStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCHED.toString())) {
				LOGGER.info("Initiating un-launch Agent for agent id: {}",gmbAgent.getId());
				AgentUnLaunchRequest request = new AgentUnLaunchRequest();
				request.setEnterpriseId(gmbAgent.getEnterpriseId());
				request.setAgentId(gmbAgent.getId());
				producer.sendObjectV1(TOPIC_AGENT_UNLAUNCH, request);
			} else {
				LOGGER.warn("Location not un-launched for agent {}", gmbAgent.getId());
			}
		});
	}

	@Override
	public void removeBusinessGMBInactiveIntegration(String channel, Integer businessId) {
		List<BusinessGoogleMyBusinessLocation> existingPage = socialGMBRepo.findAllByBusinessId(businessId);
		if(CollectionUtils.isEmpty(existingPage)){
			LOGGER.info("No page found with business id :{}",businessId);
			return;
		}
		existingPage.forEach(page ->{
			LOGGER.info("Remove Page for id :{}",page.getLocationId());
			DeleteEventRequest request = DeleteEventRequest.builder()
					.channel(SocialChannel.GMB.getName())
					.pagesIds(Collections.singletonList(page.getLocationId()))
					.build();
			kafkaProducer.sendObjectV1(KafkaTopicEnum.SOCIAL_DELETE_PAGE.getName(),request);
		});
	}

	@Override
	public void moveGmbAccountLocation(Long sourceEnterpriseNumber, Long targetEnterpriseNumber, Integer businessId,
			boolean isMultiLocationSource,Integer accountId) {
		LOGGER.info("updated GMB enterpriseId from {} to {} ", sourceEnterpriseNumber, targetEnterpriseNumber);
		List<BusinessGoogleMyBusinessLocation> gmbData = isMultiLocationSource
				? socialGMBRepo.findAllByBusinessId(businessId)
				: socialGMBRepo.findByEnterpriseId(sourceEnterpriseNumber);
		if(CollectionUtils.isEmpty(gmbData)) {
			LOGGER.info("Unable to get page for business id:{} or enterprise id  :{}", businessId, sourceEnterpriseNumber);
			return;
		}
		if (isMultiLocationSource) {
			// fetch mapping for changing location data in social
			BusinessGoogleMyBusinessLocation mappingData = socialGMBRepo.findByBusinessId(businessId);
			if(Objects.nonNull(mappingData)){
				String locationId = mappingData.getLocationId();
				if (StringUtils.isNotBlank(locationId)) {
					LOGGER.info("updated GMB enterpriseId from {} to {}  for location Ids {}", sourceEnterpriseNumber,
							targetEnterpriseNumber, locationId);

					socialGMBRepo.updateEnterpriseIdAndAccountIdByLocationIdIn(sourceEnterpriseNumber, targetEnterpriseNumber,
							locationId,accountId);
				}
			}else{
				LOGGER.info("Unable to get page for business id:{}",businessId);
			}
		} else {
			LOGGER.info("updated GMB enterpriseId from {} to {} ", sourceEnterpriseNumber, targetEnterpriseNumber);
			socialGMBRepo.updateEnterpriseIdAndAccountId(sourceEnterpriseNumber, targetEnterpriseNumber,accountId);
		}
		updateMappingForGMBPages(sourceEnterpriseNumber,targetEnterpriseNumber,gmbData);
	}

	private void updateMappingForGMBPages(Long sourceEnterpriseNumber, Long targetEnterpriseNumber,List<BusinessGoogleMyBusinessLocation> mappingData) {
		mappingData.forEach(page-> {
			LOGGER.info("updated GMB enterpriseId from {} to {}  for location Ids {}", sourceEnterpriseNumber, targetEnterpriseNumber, page.getLocationId());
			commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.UPDATE_MAPPING.name(),
					Collections.singletonList(page), null, page.getBusinessId(),page.getEnterpriseId());
			page.setEnterpriseId(targetEnterpriseNumber);
			socialGMBRepo.save(page);
		});
	}


	@Override
	public void removeUnmappedByEnterprise(Long enterpriseNumber, Integer businessId) {
		// fetch mapping for changing location data in social
		List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = socialGMBRepo.findByEnterpriseId(enterpriseNumber);
		businessGoogleMyBusinessLocations.forEach(page -> {
			if(!Objects.equals(businessId, page.getBusinessId())){
				LOGGER.info("Remove GMB page for location id : {}",page.getLocationId());
				commonService.deletePage(SocialChannel.GMB.getName(),page.getLocationId());
			}
		});
	}
	/**
	 * @param channel
	 * @param page
	 */
	private SocialPagesAudit getGMBAuditSocialObject(String channel, BusinessGoogleMyBusinessLocation page) {
		SocialPagesAudit auditPage = new SocialPagesAudit();
		auditPage.setChannel(channel);
		auditPage.setRemovedOnDate(new Date());
		auditPage.setEnterpriseId(page.getEnterpriseId());
		auditPage.setPageId(page.getLocationId());
		auditPage.setPageName(page.getLocationName());
		auditPage.setBusinessId(page.getBusinessId());
		auditPage.setPageUrl(page.getLocationUrl());
		auditPage.setAccessToken(String.valueOf(page.getRefreshTokenId()));
		auditPage.setPlaceId(page.getPlaceId());
		auditPage.setLocationMapUrl(page.getLocationMapUrl());
		auditPage.setProfileImageUrl(page.getPictureUrl());
		auditPage.setPictureUrl(page.getCoverImageUrl());
		auditPage.setIsSelected(page.getIsSelected());
		auditPage.setIsValid(page.getIsValid());
		auditPage.setIsVerified(page.getIsVerified());
		auditPage.setSingleLineAddress(page.getSingleLineAddress());
		auditPage.setUserId(page.getUserId());
		auditPage.setRequestId(page.getRequestId());
		auditPage.setGooglePlusId(page.getGooglePlusId());
		auditPage.setAccountId(page.getAccountId());
		auditPage.setAccountName(page.getAccountName());
		auditPage.setAccountType(page.getAccountType());
		auditPage.setAccountStatus(page.getAccountStatus());
		auditPage.setPrimaryPhone(page.getPrimaryPhone());
		auditPage.setWebsiteUrl(page.getWebsiteUrl());
		auditPage.setLocationState(page.getLocationState());
		auditPage.setCreatedBy(page.getCreatedBy());
		auditPage.setUpdatedBy(page.getUpdatedBy());
		return auditPage;
	}


	@Override
	public OpenUrlPagesInfo getPagesFetchedByOpenUrl(Long enterpriseId) throws Exception {
		LOGGER.info("getPagesFetchedByOpenUrl: enterpriseId {}", enterpriseId);
		OpenUrlPagesInfo response = new OpenUrlPagesInfo();
		// TODO: Move this repo method to service
		List<BusinessGetPageOpenUrlRequest> gmbRequests = businessGetPageOpenUrlReqRepo.findFirstByEnterpriseIdAndChannelAndRequestTypeOrderByCreatedDesc(enterpriseId, SocialChannel.GMB.getName(), CONNECT);
		LOGGER.info("getPagesFetchedByOpenUrl: List<BusinessGetPageOpenUrlRequest> got result {}", gmbRequests);
		if ( gmbRequests != null && !gmbRequests.isEmpty() ) {
			BusinessGetPageOpenUrlRequest gmbRequest = gmbRequests.get(0);
			// only get the pages if status is fetched
			if ( gmbRequest.getStatus().equalsIgnoreCase(Status.FETCHED.getName()) ) {
				LOGGER.info("getPagesFetchedByOpenUrl: gmbRequest found with fetched status");
				List<ChannelAccountInfo> gmbAcctInfo = new ArrayList<>();
				List<BusinessGoogleMyBusinessLocation> gmbPages = new ArrayList<>();

				if(Objects.nonNull(gmbRequest.getGmbAccount())){
					GoogleAccount googleAccount = null;
					List<GoogleAccount> googleAccountList = googleAccountService.findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(gmbRequest.getGmbAccount(), gmbRequest.getId());
					if(CollectionUtils.isNotEmpty(googleAccountList)) {
						googleAccount = googleAccountList.get(0);
					}
					if(Objects.nonNull(googleAccount)){
						gmbPages = googleMyBusinessPageService.getGMBPagesByRequestIdAndGmbAccountId(gmbRequest.getId(),googleAccount.getId());
					}
				}else{
					gmbPages = googleMyBusinessPageService.getGMBPagesByRequestId(gmbRequest.getId());
				}

				if(CollectionUtils.isNotEmpty(gmbPages)) {
					gmbAcctInfo = getAccountInfoForGmb(gmbPages, enterpriseId);
				}
				response.setPageTypes(gmbAcctInfo);
			} else {
				LOGGER.info("getPagesFetchedByOpenUrl: gmbRequest found with {} status", gmbRequest.getStatus());
			}
			response.setStatus(gmbRequest.getStatus());
			response.setStatusType(gmbRequest.getRequestType());
		} else {
			LOGGER.info("No gmbRequest found with given input");
			response.setStatus(Status.COMPLETE.getName());
			response.setStatusType(CONNECT);
		}

		LOGGER.info("getPagesFetchedByOpenUrl: response {}", response);
		return response;
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.GoogleSocialAccountService#getGoogleIntegrationRequestInfo(java.lang.Long, java.lang.String)
	 */
	@Override
	@Deprecated
	public ChannelPageInfo getGoogleIntegrationRequestInfo(Long businessId, String requestType) throws Exception {
		ChannelPageInfo response = new ChannelPageInfo();
		BusinessGetPageRequest gmbData = businessGetPageService.findLastRequestByEnterpriseIdAndChannel(businessId, SocialChannel.GMB.getName());
		if (gmbData!=null){
			if(gmbData.getStatus().equalsIgnoreCase(Status.INITIAL.getName()) || gmbData.getStatus().equalsIgnoreCase(Status.NO_PAGES_FOUND.getName())) {
				response.setStatus(gmbData.getStatus());
				response.setStatusType(gmbData.getRequestType());
				// code
			} else if(CONNECT.equalsIgnoreCase(gmbData.getRequestType()) && gmbData.getStatus().equalsIgnoreCase(Status.FETCHED.getName())) {
				LOGGER.info("[GMB] isFetched is true for businessId and request Type {} {}",businessId,requestType);
				response.setStatus(Status.FETCHED.getName());
				Map<String, List<ChannelAccountInfo>> pageType = getPages(gmbData, null);
				response.setPageTypes(pageType);
				response.setStatusType(gmbData.getRequestType());
			} else {
				response.setStatus(Status.COMPLETE.getName());
				response.setStatusType(gmbData.getRequestType());
			}
		} else {
			response.setStatus(Status.COMPLETE.getName());
			response.setStatusType(gmbData!=null?gmbData.getRequestType():requestType);
		}
		return response;
	}

	/**
	 * @param businessId
	 * @return
	 */
	private String isCompleteProcessType(Long businessId) {
		List<BusinessGetPageRequest> completeRequest = getMaxRequestForBusiness(businessId, Status.COMPLETE.getName());
		if (CollectionUtils.isNotEmpty(completeRequest)){
			return completeRequest.get(0).getRequestType();
		}
		return CONNECT;
	}

	private List<BusinessGetPageRequest> getMaxRequestForBusiness(Long businessId, String status) {
		return businessGetPageReqRepo.findLastRequestByEnterpriseIdAndStatusAndChannel(businessId, status, SocialChannel.GMB.getName());
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.GoogleSocialAccountService#reconnectPagesEnhancedFlow(java.lang.Long, java.util.List, java.lang.String, java.lang.Boolean, java.lang.Integer)
	 */
	@Override
	public void reconnectGMBPagesEnhancedFlow(Long parentId, List<String> pageIds, String accessToken, String redirectUri, Integer userId,String type) throws Exception {
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(parentId));
		boolean lock = redisService.tryToAcquireLock(key);
		LOGGER.info("[Redis Lock] Lock status : {}",lock);
		LOGGER.info("pageIds : {}",pageIds);
		if (lock) {
			try {
				// Check if another connect request is present in INIT or FETCHED state
				BusinessGetPageRequest existingInProgressReq =  type.equalsIgnoreCase(Constants.RESELLER) ?
						businessGetPageReqRepo.findFirstByResellerIdAndChannelAndStatusIn(parentId, SocialChannel.GMB.getName(), Arrays.asList(Status.FETCHED.name(), Status.INITIAL.name())):
						businessGetPageReqRepo.findFirstByEnterpriseIdAndChannelAndStatusIn(parentId, SocialChannel.GMB.getName(), Arrays.asList(Status.FETCHED.name(), Status.INITIAL.name()));;

				if (existingInProgressReq != null) {
					LOGGER.info("reconnectGMBPagesEnhancedFlow: Existing BusinessGetPageRequest found with status INITIAL/FETCHED {}", existingInProgressReq);
					throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
				}
				pushCheckStatusInFirebase(SocialChannel.GMB.getName(), RECONNECT, Status.INITIAL.getName(),parentId);
				GoogleAuthToken gmbAuth = getGoogleToken(parentId, accessToken, redirectUri);
				BusinessGetPageRequest request = new BusinessGetPageRequest();
				request.setBirdeyeUserId(userId);
				request.setSocialUserId(gmbAuth.getUserId());
				request.setChannel(SocialChannel.GMB.getName());
				if(type.equalsIgnoreCase(Constants.RESELLER)){
					request.setResellerId(parentId);
				}else{
					request.setEnterpriseId(parentId);
				}
				request.setPageCount(0);
				request.setStatus(Status.INITIAL.getName());
				request.setRequestType(RECONNECT);
				request.setEmail(gmbAuth.getEmail());
				businessGetPageReqRepo.saveAndFlush(request);
				submitReconnectFetchPageRequest(gmbAuth,pageIds,request,userId,parentId,type);
			} catch (Exception e) {
				// Cleanup redis cache for error cases.
				redisService.release(key);
				LOGGER.error("[Redis Lock] (GMB Reconnect) Lock released for business {}", parentId);
				if (e instanceof BirdeyeSocialException) {
					BirdeyeSocialException beExp = (BirdeyeSocialException) e;
					if (beExp.getCode() == ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.value())
						throw beExp;
				}
				//Cleanup business get pages request table
				BusinessGetPageRequest req = checkInProgressRequestsGMB(parentId, RECONNECT);
				if (req != null) {
					req.setStatus(Status.CANCEL.getName());
					businessGetPageReqRepo.saveAndFlush(req);
				}
				pushCheckStatusInFirebase(SocialChannel.GMB.getName(), RECONNECT, Status.COMPLETE.getName(),parentId,true);
			}
		} else {
			LOGGER.info("[Redis Lock] (GMB Reconnect) Lock is already acquired for business {}", parentId);
			throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
		}
	}

	@Override
	public void reconnectGMBPagesEnhancedFlowForReseller(Long resellerId, String accessToken, String redirectUri, Integer userId,String type,Integer limit)  {
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(resellerId));
		boolean lock = redisService.tryToAcquireLock(key);
		LOGGER.info("[Redis Lock] Lock status : {}",lock);
		List<String> pageIds = socialGMBRepo.findByResellerIdAndValidType(resellerId,Arrays.asList(ValidTypeEnum.INVALID.getId(),ValidTypeEnum.PARTIAL_VALID.getId()));
		LOGGER.info("PageIds: {}",pageIds);
		if (lock) {
			try {
				// Check if another connect request is present in INIT or FETCHED state
				BusinessGetPageRequest existingInProgressReq =
						businessGetPageReqRepo.findFirstByResellerIdAndChannelAndStatusIn(resellerId, SocialChannel.GMB.getName(), Arrays.asList(Status.FETCHED.name(), Status.INITIAL.name()));

				if (Objects.nonNull(existingInProgressReq)) {
					LOGGER.info("reconnectGMBPagesEnhancedFlow: Existing BusinessGetPageRequest found with status INITIAL/FETCHED {}", existingInProgressReq);
					throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
				}
				pushCheckStatusInFirebase(SocialChannel.GMB.getName(), RECONNECT, Status.INITIAL.getName(),resellerId);
				GoogleAuthToken gmbAuth = getGoogleToken(resellerId, accessToken, redirectUri);
				BusinessGetPageRequest request = new BusinessGetPageRequest();
				request.setBirdeyeUserId(userId);
				request.setSocialUserId(gmbAuth.getUserId());
				request.setChannel(SocialChannel.GMB.getName());
				request.setResellerId(resellerId);
				request.setPageCount(0);
				request.setStatus(Status.INITIAL.getName());
				request.setRequestType(RECONNECT);
				request.setEmail(gmbAuth.getEmail());
				businessGetPageReqRepo.saveAndFlush(request);
				submitReconnectFetchPageRequest(gmbAuth,pageIds,request,userId,resellerId,type);
			} catch (Exception e) {
				// Cleanup redis cache for error cases.
				redisService.release(key);
				LOGGER.error("[Redis Lock] (GMB Reconnect) Lock released for business {}", resellerId);
				if (e instanceof BirdeyeSocialException) {
					BirdeyeSocialException beExp = (BirdeyeSocialException) e;
					if (beExp.getCode() == ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.value())
						throw beExp;
				}
				//Cleanup business get pages request table
				BusinessGetPageRequest req = checkInProgressRequestsGMB(resellerId, RECONNECT);
				if (req != null) {
					req.setStatus(Status.CANCEL.getName());
					businessGetPageReqRepo.saveAndFlush(req);
				}
				pushCheckStatusInFirebase(SocialChannel.GMB.getName(), RECONNECT, Status.COMPLETE.getName(),resellerId,true);
			}
		} else {
			LOGGER.info("[Redis Lock] (GMB Reconnect) Lock is already acquired for business {}", resellerId);
			throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
		}
	}

	private void submitReconnectFetchPageRequest(GoogleAuthToken gmbAuth, List<String> pageIds, BusinessGetPageRequest request, Integer userId,Long parentId,String type){
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(parentId));
		try {
			List<GMBAccount> gmbAccountList = gmbService.getAllGmbAccounts(gmbAuth.getAccess_token());
			if (CollectionUtils.isNotEmpty(gmbAccountList)) {
				List<GMBAccountDTO> accounts = new ArrayList<>();
				gmbAccountList.forEach(gmbacc -> accounts.add(prepareAccountDTO(gmbacc)));
				LOGGER.info("[GMB Reconnect] Accounts for refresh token: {}, are: {}", gmbAuth.getRefresh_token(), gmbAccountList);
				googleMyBusinessPageService.saveToRedisByBusinessGetPageRequestId(accounts,request.getId().toString());
				for (GMBAccountDTO gmbAccount : accounts) {
					GMBAccountSyncRequest gmbAccountCheckRequest = new GMBAccountSyncRequest();
					gmbAccountCheckRequest.setGmbAccount(gmbAccount);
					gmbAccountCheckRequest.setGoogleAuthToken(gmbAuth);
					gmbAccountCheckRequest.setBusinessGetPageRequestId(request.getId().toString());
					gmbAccountCheckRequest.setPageIds(pageIds);
					gmbAccountCheckRequest.setUserId(userId);
					gmbAccountCheckRequest.setType(type);
					if(type.equalsIgnoreCase(Constants.RESELLER)){
						producer.sendWithKey(Constants.GMB_ACCOUNT_RECONNECT_PAGE_RESELLER,gmbAccount.getAccountId(), gmbAccountCheckRequest);
					}else{
						producer.sendWithKey(Constants.GMB_ACCOUNT_RECONNECT_PAGE,gmbAccount.getAccountId(), gmbAccountCheckRequest);
					}
					LOGGER.info("[GMB Reconnect] Account data pushed to kafka for businessGetpageReqId and account id  {} {}",gmbAccount.getAccountId(),request.getId());
				}
			}else{
				LOGGER.info("No account received from gmb for businessId and requestId {} {}",parentId,request.getId());
				redisService.release(key);
				request.setStatus(Status.COMPLETE.getName());
				request.setErrorLog("No account received from gmb");
				businessGetPageService.saveAndFlush(request);
				CheckStatusRequest checkStatusRequest = CheckStatusRequest.toBuilder().with($->{
					$.status = Status.COMPLETE.getName();
					$.statusType = request.getRequestType();
					$.error = false;
				}).build();
				pushCheckStatusInFirebase(SocialChannel.GMB.getName(),parentId,checkStatusRequest);
			}
		} catch (Exception exe) {
			redisService.release(key);
			request.setStatus(Status.CANCEL.getName());
			request.setErrorLog("No account received from gmb.");
			businessGetPageService.saveAndFlush(request);
			LOGGER.error("Error while pushing GMB account location data", exe.getLocalizedMessage());
			pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),Status.COMPLETE.getName(),parentId,true);
		}
	}

	@Override
	public void postFetchPageReconnectProcess(Map<String, GoogleMyBusinessPagesDTO> pagesMap, GMBAccountSyncRequest gmbAccountReconnectRequest,String type) {

		BusinessGetPageRequest request = businessGetPageService.findById(gmbAccountReconnectRequest.getBusinessGetPageRequestId());
		Long parentId  = type.equalsIgnoreCase(Constants.RESELLER) ? request.getResellerId(): request.getEnterpriseId();
		List<BusinessGoogleMyBusinessLocation> socialGMBPages = googleMyBusinessPageService.getGMBPagesByLocationId(gmbAccountReconnectRequest.getPageIds());
		Map<String, BusinessGoogleMyBusinessLocation> locationToGMBRawMap = socialGMBPages.stream()
				.collect(Collectors.toMap(BusinessGoogleMyBusinessLocation::getLocationId, rawPage -> rawPage, (x, y) -> x));
		List<GoogleMyBusinessPagesDTO> newMyBusinessPages = new ArrayList<>();
		Integer reconnectedCount = 0;
		if(MapUtils.isNotEmpty(pagesMap)){
			newMyBusinessPages.addAll(pagesMap.values());
			for (Map.Entry<String, GoogleMyBusinessPagesDTO> entry : pagesMap.entrySet()) {
				Integer isValid = 1;
				if (locationToGMBRawMap.containsKey(entry.getKey())) {
					BusinessGoogleMyBusinessLocation page = locationToGMBRawMap.get(entry.getKey());
					GoogleMyBusinessPagesDTO googleMyBusinessPagesDTO = entry.getValue();
					reconnectRawGMBLocation(gmbAccountReconnectRequest.getGoogleAuthToken(), request.getBirdeyeUserId(), request, googleMyBusinessPagesDTO, page, isValid,parentId,type);
					reconnectedCount++;
					// Change event name to reconnect to all social channels
					commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.RECONNECT_PAGE.name(), Arrays.asList(page), gmbAccountReconnectRequest.getUserId().toString(), page.getBusinessId(), page.getEnterpriseId());
					if(Objects.nonNull(page.getBusinessId())){
						//BAMUpdateRequest payload = new BAMUpdateRequest("gmb", page.getBusinessId(), page.getLocationMapUrl(), page.getPlaceId());
						BAMUpdateRequest payload =
								new BAMUpdateRequest("gmb", page.getBusinessId(), page.getLocationMapUrl(),page.getLocationId(), page.getPlaceId());
						producer.sendWithKey(Constants.INTEGRATION_TOPIC, String.valueOf(page.getBusinessId()), payload);
					}
				}
			}
			if(reconnectedCount > 0){
				LOGGER.info("GMB Reconnect : Pages reconnect from total requested for account :  {} {} {}"
						,gmbAccountReconnectRequest.getGmbAccount().getAccountId(),gmbAccountReconnectRequest.getPageIds().size(),reconnectedCount);
			}
			// Google messaging is deprecated
			/*LaunchAgentDTO launchAgentDTO = new LaunchAgentDTO();
			launchAgentDTO.setUserId(gmbAccountReconnectRequest.getUserId());
			launchAgentDTO.setParentId(parentId);
			launchAgentDTO.setRequestId(request.getId());
			launchAgentDTO.setIsEnterprise(true);
			producer.sendObject(TOPIC_RESELLER_AGENT_LAUNCH,launchAgentDTO);
			 */
		} else {
			LOGGER.info("GMB Reconnect No page fetched for businessGetPageReq and account id :  {} {}",request.getId(),gmbAccountReconnectRequest.getGmbAccount().getAccountId());
			request.setStatus(Status.NO_PAGES_FOUND.getName());
		}

		request.setPageCount(request.getPageCount()!=null?request.getPageCount()+reconnectedCount:reconnectedCount);
		request.setTotalPages(request.getTotalPages()!=null?request.getTotalPages()+newMyBusinessPages.size():newMyBusinessPages.size());

		LOGGER.info("GMB Reconnect check if all account data has been processed to close the request for businessGetPageReq and account id :  {} {}",request.getId(),gmbAccountReconnectRequest.getGmbAccount().getAccountId());
		Boolean isEligible = redisExternalService.checkIfAllProcessedAfterCurrentForGMB(request.getId().toString(),gmbAccountReconnectRequest.getGmbAccount().getAccountId());

		if(isEligible){
			LOGGER.info("GMB Reconnect all account data has been processed to close the request for businessGetPageReq and account id :  {} {}",request.getId(),gmbAccountReconnectRequest.getGmbAccount().getAccountId());
			request.setStatus(Status.COMPLETE.getName());
			businessGetPageReqRepo.saveAndFlush(request);
			releaseLock(parentId);
			pushCheckStatusInFirebase(SocialChannel.GMB.getName(), request.getRequestType(), request.getStatus(), parentId);
		}
	}

	@Override
	public void postFetchPageReconnectProcessForReseller(Map<String, GoogleMyBusinessPagesDTO> pagesMap, GMBAccountSyncRequest gmbAccountReconnectRequest,String type) {

		BusinessGetPageRequest request = businessGetPageService.findById(gmbAccountReconnectRequest.getBusinessGetPageRequestId());
		Long parentId  = type.equalsIgnoreCase(Constants.RESELLER) ? request.getResellerId(): request.getEnterpriseId();
		List<BusinessGoogleMyBusinessLocation> socialGMBPages = googleMyBusinessPageService.getGMBPagesByLocationId(gmbAccountReconnectRequest.getPageIds());
		Map<String, BusinessGoogleMyBusinessLocation> locationToGMBRawMap = socialGMBPages.stream()
				.collect(Collectors.toMap(BusinessGoogleMyBusinessLocation::getLocationId, rawPage -> rawPage, (x, y) -> x));
		List<GoogleMyBusinessPagesDTO> newMyBusinessPages = new ArrayList<>();
		Integer reconnectedCount = 0;
		if(MapUtils.isNotEmpty(pagesMap)){
			// Google messaging is deprecated
			/*
			LaunchAgentDTO launchAgentDTO = new LaunchAgentDTO();
			launchAgentDTO.setUserId(gmbAccountReconnectRequest.getUserId());
			launchAgentDTO.setParentId(parentId);
			launchAgentDTO.setIsEnterprise(false);
			launchAgentDTO.setRequestId(request.getId());
			producer.sendObject(TOPIC_RESELLER_AGENT_LAUNCH,launchAgentDTO);
			 */
			newMyBusinessPages.addAll(pagesMap.values());
			for (Map.Entry<String, GoogleMyBusinessPagesDTO> entry : pagesMap.entrySet()) {
				Integer isValid = 1;
				if (locationToGMBRawMap.containsKey(entry.getKey())) {
					BusinessGoogleMyBusinessLocation page = locationToGMBRawMap.get(entry.getKey());
					GoogleMyBusinessPagesDTO googleMyBusinessPagesDTO = entry.getValue();
					reconnectRawGMBLocation(gmbAccountReconnectRequest.getGoogleAuthToken(),
							request.getBirdeyeUserId(), request, googleMyBusinessPagesDTO, page, isValid,parentId,type);
					reconnectedCount++;
					if(Objects.nonNull(page.getBusinessId())){

						//BAMUpdateRequest payload = new BAMUpdateRequest("gmb", page.getBusinessId(), page.getLocationMapUrl(), page.getPlaceId());
						BAMUpdateRequest payload = new BAMUpdateRequest("gmb", page.getBusinessId(),
								page.getLocationMapUrl(),page.getLocationId(), page.getPlaceId());

						producer.sendWithKey(Constants.INTEGRATION_TOPIC, String.valueOf(page.getBusinessId()), payload);
					}
					commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.RECONNECT_PAGE.name(), Arrays.asList(page), gmbAccountReconnectRequest.getUserId().toString(), page.getBusinessId(), page.getEnterpriseId());
				}
			}
			if(reconnectedCount > 0){
				LOGGER.info("GMB Reconnect : Pages reconnect from total requested for account :  {} {}",gmbAccountReconnectRequest.getGmbAccount().getAccountId(),gmbAccountReconnectRequest.getPageIds().size(),reconnectedCount);
			}
		} else {
			LOGGER.info("GMB Reconnect No page fetched for businessGetPageReq and account id :  {} {}",request.getId(),gmbAccountReconnectRequest.getGmbAccount().getAccountId());
			request.setStatus(Status.NO_PAGES_FOUND.getName());
		}

		request.setPageCount(request.getPageCount()!=null?request.getPageCount()+reconnectedCount:reconnectedCount);
		request.setTotalPages(request.getTotalPages()!=null?request.getTotalPages()+newMyBusinessPages.size():newMyBusinessPages.size());

		LOGGER.info("GMB Reconnect check if all account data has been processed to close the request for businessGetPageReq and account id :  {} {}",request.getId(),gmbAccountReconnectRequest.getGmbAccount().getAccountId());
		Boolean isEligible = redisExternalService.checkIfAllProcessedAfterCurrentForGMB(request.getId().toString(),gmbAccountReconnectRequest.getGmbAccount().getAccountId());

		if(isEligible){
			LOGGER.info("GMB Reconnect all account data has been processed to close the request for businessGetPageReq and account id :  {} {}",request.getId(),gmbAccountReconnectRequest.getGmbAccount().getAccountId());
			request.setStatus(Status.COMPLETE.getName());
			businessGetPageReqRepo.saveAndFlush(request);
			releaseLock(parentId);
			pushCheckStatusInFirebase(SocialChannel.GMB.getName(), request.getRequestType(), request.getStatus(), parentId);
		}
	}


	private Integer reconnectRawGMBLocation(GoogleAuthToken gmbAuth, Integer userId, BusinessGetPageRequest request, GoogleMyBusinessPagesDTO googleMyBusinessPagesDTO,
			BusinessGoogleMyBusinessLocation rawPage, Integer isValid,Long parentId,String type) {

		rawPage.setRefreshTokenId(gmbAuth.getRefreshTokenId());
		rawPage.setUserId(googleMyBusinessPagesDTO.getUserId());
		rawPage.setUserName(googleMyBusinessPagesDTO.getUserName());
		rawPage.setEmailId(request.getEmail());
		//Updating Account details for GMB reconnect
		rawPage.setAccountId(googleMyBusinessPagesDTO.getAccountId());
		rawPage.setAccountName(googleMyBusinessPagesDTO.getAccountName());
		rawPage.setAccountStatus(googleMyBusinessPagesDTO.getAccountStatus());
		rawPage.setAccountType(googleMyBusinessPagesDTO.getAccountType());
		rawPage.setLocationId(googleMyBusinessPagesDTO.getLocationId());
		rawPage.setLocationName(googleMyBusinessPagesDTO.getLocationName());
		rawPage.setLocationUrl(Constants.ACCOUNTS+googleMyBusinessPagesDTO.getAccountId()+"/"+googleMyBusinessPagesDTO.getLocationUrl());
		rawPage.setLocationMapUrl(googleMyBusinessPagesDTO.getLocationMapUrl());
		rawPage.setPlaceId(googleMyBusinessPagesDTO.getPlaceId());
		rawPage.setLocationState(googleMyBusinessPagesDTO.getLocationState());
		rawPage.setSingleLineAddress(googleMyBusinessPagesDTO.getSingleLineAddress());
		if(googleMyBusinessPagesDTO.getScope()!=null) {
			rawPage.setPermissions(commonService.extractGMBPermission(googleMyBusinessPagesDTO.getScope()));
		}
		rawPage.setPrimaryPhone(googleMyBusinessPagesDTO.getPrimaryPhone());
		rawPage.setCreatedBy(userId);
		rawPage.setUpdatedBy(userId);
		rawPage.setUpdatedAt(new Date());
		rawPage.setRequestId(request.getId().toString());

		GMBLocationState locationState = JSONUtils.fromJSON(googleMyBusinessPagesDTO.getLocationState(), GMBLocationState.class);
		if ((locationState.getIsDuplicate() != null && locationState.getIsDuplicate()) ||
				(locationState.getHasVoiceOfMerchant() != null && !(locationState.getHasVoiceOfMerchant())) || googleMyBusinessPagesDTO.getPlaceId() == null){
			rawPage.setIsValid(0);
			isValid = 0;
		} else {
			rawPage.setIsValid(1);
		}
		rawPage.setIsValid(isValid);
		Integer businessId = type.equalsIgnoreCase(Constants.RESELLER)? businessCoreService.getBusinessId(request.getResellerId()):
				businessCoreService.getBusinessId(request.getEnterpriseId());
		if(Objects.nonNull(businessId)) {
			BusinessOptionsDTO businessOptionsDTO = businessCoreService.getBusinessOptions(businessId);
			if (businessOptionsDTO != null &&
					(businessOptionsDTO.getEnableChatWidget() != null && businessOptionsDTO.getEnableChatWidget() == 0) ||
					(businessOptionsDTO.getEnableMessenger() != null && businessOptionsDTO.getEnableMessenger() == 0)) {
				rawPage.setIsMessagingEnabled(0);
			} else if(rawPage.getPermissions() != null && (!rawPage.getPermissions().contains(Constants.BUSINESS_MESSAGING) || !rawPage.getPermissions().contains(Constants.BUSINESS_COMMUNICATION))) {
					rawPage.setIsMessagingEnabled(0);
			} else if(rawPage.getgMsgLocationStatus() != null &&
					rawPage.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCHED.name())) {
				rawPage.setIsMessagingEnabled(1);
			} else {
				rawPage.setIsMessagingEnabled(0);
			}
		}

		//Evict Access Token from Redis Cache
		googleAccessTokenCache.evictGoogleAccessTokenCache(rawPage);

		//Add Access Token in Redis Cache
		googleAccessTokenCache.updateGoogleAccessToken(rawPage, gmbAuth.getAccess_token());

		brokenIntegrationService.pushValidIntegrationStatus(rawPage.getEnterpriseId(),SocialChannel.GMB.getName(), rawPage.getId(),isValid,rawPage.getLocationId());
		googleMyBusinessPageService.saveGMBRowPage(rawPage);
		commonService.uploadPageImageToCDN(rawPage);

		if(rawPage.getIsValid().equals(0)) {
			commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(rawPage),
					rawPage.getUserId(), rawPage.getBusinessId(), rawPage.getEnterpriseId());
		}

		LOGGER.info(String.format("Sending validity check for location {} from %s","reconnectRawGMBLocation"),rawPage.getLocationId());
		googleMyBusinessPageService.pushToKafkaForValidity(Constants.GMB,rawPage.getLocationId());
		SocialChannelSyncRequest socialChannelSyncRequest = new SocialChannelSyncRequest(SocialChannel.GMB.getName(),rawPage.getLocationId());
		kafkaProducer.sendObject(Constants.SOCIAL_PAGE_SYNC,socialChannelSyncRequest);
		return isValid;
	}

	@Override
	public ChannelLocationInfo getSingleLocationMappingPages(Integer businessId) {
		ChannelLocationInfo locInfo = new ChannelLocationInfo();
		List<BusinessLocationEntity> businessLocations = businessRepo.getAllBusinessEnterpriseByBid(Collections.singletonList(businessId));
		if (CollectionUtils.isNotEmpty(businessLocations)) {
			BusinessLocationEntity businessLocationEntity = businessLocations.get(0);
			locInfo.setLocationId(businessLocationEntity.getId());
			locInfo.setLocationName(businessLocationEntity.getAlias1() != null ? businessLocationEntity.getAlias1() : businessLocationEntity.getName());
			locInfo.setAddress(prepareBusinessAddress(businessLocationEntity));
			BusinessGoogleMyBusinessLocation businessGoogleMyBusinessLocation = socialGMBRepo.findByBusinessId(businessId);
			// get facebook Page Mapping
			if(Objects.nonNull(businessGoogleMyBusinessLocation)){
				Map<String, LocationPageListInfo> pageInfoMap = new HashMap<>();
				Boolean googleMessageEnabled = false;
				googleMessageEnabled = isGoogleMessageEnabled(businessId,null);
				pageInfoMap.put(SocialChannel.GMB.getName(), prepareGMBPageData(businessGoogleMyBusinessLocation,googleMessageEnabled, null,null));

				locInfo.setPageData(pageInfoMap);
			}
		}
		return locInfo;
	}

	@Async
	@Override
	public void restoreBusinessInactiveIntegration(String name, Integer businessId) {
		SocialPagesAudit socialPagesAudit = socialPagesAuditRepo.findByBusinessIdAndChannel(businessId,name);
		if(Objects.nonNull(socialPagesAudit)){
			backupGMBPages(socialPagesAudit);
			try{
				LOGGER.info("gmb page restored, submitting request for enabling gmb notification for business {}",businessId);
				GMBNotificationRequest gmbNotificationRequest = new GMBNotificationRequest();
				List<String> accountId = new ArrayList<>();
				accountId.add(socialPagesAudit.getAccountId());
				gmbNotificationRequest.setAccountId(accountId);
				gmbNotificationRequest.setRequestId(socialPagesAudit.getRequestId());
				String accessToken = googleAuthenticationService.getGoogleAccessToken(Integer.valueOf(socialPagesAudit.getAccessToken()));
				if(Objects.nonNull(accessToken)){
					gmbNotificationRequest.setAccessToken(accessToken);
					producer.sendObject(Constants.ENABLE_NOTIFICATION_TOPIC, gmbNotificationRequest);
				}
			}catch (Exception ex){
				LOGGER.error("exception occurred while enabling gmb notification while location restoring for business {} , exception {}",businessId,ex);
			}
		}else{
			LOGGER.info("no backup found in social pages audit for channel gmb and business Id {}",businessId);
		}
	}

	@Override
	public void backupGMBPages(SocialPagesAudit socialPagesAudit)
	{
		LOGGER.info("Start restoring GMB PageId {} : for enterpriseId {} : ", socialPagesAudit.getPageId(),socialPagesAudit.getEnterpriseId());

		List<BusinessGoogleMyBusinessLocation> existingLocations=socialGMBRepo.findByLocationId(socialPagesAudit.getPageId());
		if(CollectionUtils.isEmpty(existingLocations)) {
			getGmbPageFromSocialAudit(socialPagesAudit);
			LOGGER.info("Restoring GMB PageId {} : in BusinessGoogleMyBusinessLocation for enterpriseId {} : is completed ", socialPagesAudit.getPageId(),socialPagesAudit.getEnterpriseId());
		} else {
			socialPagesAudit.setSocialEnterpriseRestorePageId("Exists :: "+existingLocations.get(0).getId());
			LOGGER.info("Already exists GMB pageId {} : in BusinessGoogleMyBusinessLocation for enterpriseId {} : ", socialPagesAudit.getPageId(),socialPagesAudit.getEnterpriseId());
		}
		socialPagesAuditRepo.saveAndFlush(socialPagesAudit);
	}

	private void getGmbPageFromSocialAudit(SocialPagesAudit socialPagesAudit) {
		BusinessGoogleMyBusinessLocation request=new BusinessGoogleMyBusinessLocation();
		request.setEnterpriseId(socialPagesAudit.getEnterpriseId());
		request.setLocationId(socialPagesAudit.getPageId());
		request.setLocationName(socialPagesAudit.getPageName() );
		request.setLocationUrl(socialPagesAudit.getPageUrl());
		request.setRefreshTokenId(Integer.valueOf(socialPagesAudit.getAccessToken()));
		request.setPlaceId(socialPagesAudit.getPlaceId());
		request.setLocationMapUrl(socialPagesAudit.getLocationMapUrl());
		request.setPictureUrl(socialPagesAudit.getProfileImageUrl());
		request.setCoverImageUrl(socialPagesAudit.getPictureUrl());
		request.setIsSelected(socialPagesAudit.getIsSelected());
		request.setIsValid(socialPagesAudit.getIsValid());
		request.setIsVerified(socialPagesAudit.getIsVerified());
		request.setSingleLineAddress(socialPagesAudit.getSingleLineAddress());
		request.setUserId(socialPagesAudit.getUserId());
		request.setRequestId(socialPagesAudit.getRequestId());
		request.setGooglePlusId(socialPagesAudit.getGooglePlusId());
		request.setAccountId(socialPagesAudit.getAccountId());
		request.setAccountName(socialPagesAudit.getAccountName());
		request.setAccountType(socialPagesAudit.getAccountType());
		request.setAccountStatus(socialPagesAudit.getAccountStatus());
		request.setPrimaryPhone(socialPagesAudit.getPrimaryPhone());
		request.setWebsiteUrl(socialPagesAudit.getWebsiteUrl());
		request.setLocationState(socialPagesAudit.getLocationState());
		request.setCreatedBy(socialPagesAudit.getCreatedBy());
		request.setUpdatedBy(socialPagesAudit.getUpdatedBy());
		request.setBusinessId(socialPagesAudit.getBusinessId());
		socialGMBRepo.saveAndFlush(request);
		if(request.getIsValid().equals(0)) {
			commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(request),
					request.getUserId(), request.getBusinessId(), socialPagesAudit.getEnterpriseId());
		}
		socialPagesAudit.setSocialEnterpriseRestorePageId(request.getId().toString());
	}

	@Override
	public boolean checkIfAccountExistsByAccountId(Long accountId) {
		return socialGMBRepo.existsByEnterpriseIdAndIsSelected(accountId,1);
	}

	@Override
	public void processAutoMappingInitRequest(Long enterpriseId) {
		//@refactor: use core business service -- discuss with amit
		Business business = businessRepo.findByBusinessId(enterpriseId);
		LOGGER.info("Initiating auto mapping for enterprise: {}",enterpriseId);
		List<Integer> businessIds = businessRepo.findEnterpriseLocations(business.getId());
		if(CollectionUtils.isEmpty(businessIds)) {
			businessIds.add(business.getId());
		}
		Map<String,List<String>> placeIdList = bamAggregationService.getBusinessPlaceIds(SocialChannel.GMB.getName(),businessIds);
		//TODO: created automapping API for future
		Map<Integer,String> finalMatchedList = new HashMap<>();
		if(placeIdList != null && placeIdList.size()>0) {
			LOGGER.info("profileId List length received for enterprise: {} is {}", enterpriseId,placeIdList.size());
			Set<String> existingPages = socialGMBRepo.findAllByEnterpriseIdAndSelectedPageIdsInSet(enterpriseId);
			LOGGER.info("Existing place id length fetched on social: {}",existingPages.size());
			List<String> bamPlaceIds;
			Set<String> matchedPlaceIds = new HashSet<>();
			for (Map.Entry<String,List<String>> entry : placeIdList.entrySet()) {
				bamPlaceIds = entry.getValue();
				bamPlaceIds.stream().forEach(bamPlaceId -> {
					if(existingPages.contains(bamPlaceId) && !matchedPlaceIds.contains(bamPlaceId)) {
						finalMatchedList.put(Integer.valueOf(entry.getKey()),bamPlaceId);
						matchedPlaceIds.add(bamPlaceId);
					} else if(matchedPlaceIds.contains(bamPlaceId)) {
						LOGGER.info("Place id {} already mapped, can not map with business ID: {}",bamPlaceId,entry.getKey());
					}
				});
			}
		}
		if(finalMatchedList.size()>0) {
			LOGGER.info("placeIdList auto mapped length for enterprise: {} is {}", enterpriseId,finalMatchedList.size());
			List<Integer> keyList = new ArrayList<>(finalMatchedList.keySet());
			String key = TOPIC_MATCHED + "/" +  SocialChannel.GMB.getName() + "/" + enterpriseId;
			redisExternalService.delete(key);
			redisExternalService.set(key,keyList);
			for (Map.Entry<Integer,String> entry : finalMatchedList.entrySet()) {
				AutoMappingMatchedRequest autoMappingMatchedRequest = new AutoMappingMatchedRequest();
				autoMappingMatchedRequest.setChannel(SocialChannel.GMB.getName());
				autoMappingMatchedRequest.setBusinessId(Integer.parseInt(String.valueOf(entry.getKey())));
				autoMappingMatchedRequest.setProfileId(entry.getValue());
				autoMappingMatchedRequest.setEnterpriseId(enterpriseId);
				String topicKey = String.valueOf(enterpriseId);
				producer.sendWithKey(TOPIC_MATCHED, topicKey, autoMappingMatchedRequest);
			}
		} else {
			LOGGER.info("No id matched in auto mapping for enterprise: {}", enterpriseId);
 			pushGmbAutoMappingInFirebase(enterpriseId,Status.COMPLETE.getName());
			autoMappingService.updateAutoMappingEntryStatus(enterpriseId,Status.COMPLETE.getName(),SocialChannel.GMB.getName());
		}
	}

	@Override
	public void processAutoMappingMatchedRequest(AutoMappingMatchedRequest request) {
		String key = TOPIC_MATCHED + "/" +  SocialChannel.GMB.getName() + "/" + request.getEnterpriseId();
//		List<Integer> setValue = new ArrayList<>();
//		setValue.add(request.getBusinessId());
//		setValue.add(request.getBusinessId());
//		redisExternalService.set(key, setValue);
		Optional<Object> redisValue = redisExternalService.get(key);

		if(!redisValue.isPresent()) {
			return;
		}
		List<Integer> businessIds = (List<Integer>) redisValue.get();
		List<Integer> updatedBusinessIds = new ArrayList<>();
		for (Integer businessId : businessIds) {
			if (!Objects.equals(businessId, request.getBusinessId())) {
				updatedBusinessIds.add(businessId);
			} else {
				BusinessGoogleMyBusinessLocation tempGMBPage =
						socialGMBRepo.findFirstByEnterpriseIdAndPlaceIdAndIsValidAndBusinessIdIsNull(
								request.getEnterpriseId(),request.getProfileId(),1);
				if(Objects.isNull(tempGMBPage)){
					continue;
				}
				tempGMBPage.setBusinessId(request.getBusinessId());
				socialGMBRepo.saveAndFlush(tempGMBPage);
				autoMappingService.updateAutoMappingRawIds(request.getEnterpriseId(), tempGMBPage.getId(), SocialChannel.GMB.getName());
				kafkaProducer.sendObjectV1(Constants.GMB_PAGE_MAPPING_ADDED,
						new LocationPageMappingRequest(request.getBusinessId(), tempGMBPage.getLocationId()));
				BAMUpdateRequest payload = new BAMUpdateRequest("gmb", request.getBusinessId(),
						tempGMBPage.getLocationMapUrl(), tempGMBPage.getLocationId(), tempGMBPage.getPlaceId());
				kafkaProducer.sendWithKey(Constants.INTEGRATION_TOPIC, String.valueOf(request.getBusinessId()), payload);
			}
		}
		if(updatedBusinessIds.size()>0) {
			redisExternalService.set(key,updatedBusinessIds);
		} else {
			LOGGER.info("auto mapping complete for enterprise: {}", request.getEnterpriseId());
			// Google Messaging is deprecated
			/*
			List<BusinessGoogleMyBusinessLocation> gmbPages =
					googleMyBusinessPageService.findByEnterpriseIdAndIsValidAndIsSelectedAndBusinessIdIsNotNull(request.getEnterpriseId());
			List<BusinessGoogleMyBusinessLocation> mappedPages = new ArrayList<>();
			List<GoogleMessagesAgent> googleMessagesAgents = agentRepo.findByEnterpriseIdIn(request.getEnterpriseId());

			if(isAutoLaunchEnabled(request.getEnterpriseId()) && CollectionUtils.isNotEmpty(gmbPages)
					&& CollectionUtils.isNotEmpty(googleMessagesAgents) && googleMessagesAgents.size() == 1 ) {
				gmbPages.stream().filter(gmbPage -> (StringUtils.isNotEmpty(gmbPage.getPermissions()) && StringUtils.isNotEmpty(gmbPage.getPlaceId()))).forEach(gmbPage -> {
					if((StringUtils.isEmpty(gmbPage.getgMsgLocationStatus()) ||
							(StringUtils.isNotEmpty(gmbPage.getgMsgLocationStatus()) && !gmbPage.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCHED.toString())))
							&& gmbPage.getPermissions() != null && gmbPage.getPermissions().contains(Constants.BUSINESS_MESSAGING)
							&& gmbPage.getPermissions().contains(Constants.BUSINESS_COMMUNICATION)){
						mappedPages.add(gmbPage);
					}
				});
				if(CollectionUtils.isNotEmpty(mappedPages)) {
					socialGMBRepo.updateAgentIdByBusinessIds(googleMessagesAgents.get(0).getId(), businessIds);
					LOGGER.info("setupAgent: Setting up locations for agentId: {} post automapping", googleMessagesAgents.get(0).getId());
					initSetupLocation(request.getEnterpriseId(), googleMessagesAgents.get(0), mappedPages, true);
				}
			} else {

			}
 */
			pushGmbAutoMappingInFirebase(request.getEnterpriseId(),Status.COMPLETE.getName());
			autoMappingService.updateAutoMappingEntryStatus(request.getEnterpriseId(),Status.COMPLETE.getName(),SocialChannel.GMB.getName());
		}
	}

	private Map<Integer, List<BusinessGoogleMyBusinessLocation>> prepareAgentAndGmbPageMap(List<BusinessGoogleMyBusinessLocation> gmbPages) {
		Map<Integer, List<BusinessGoogleMyBusinessLocation>> agentAndGmbPageMap = new LinkedHashMap<>();
		if(CollectionUtils.isEmpty(gmbPages)){
			return agentAndGmbPageMap;
		}
		agentAndGmbPageMap = gmbPages.stream().collect(Collectors.groupingBy(BusinessGoogleMyBusinessLocation::getAgentId,
				Collectors.mapping(page -> page, Collectors.toList())));
		return agentAndGmbPageMap;
	}

	@Override
	public void fetchDisconnectedAndStore() {
		try {
			long queueSize = redisExternalService.getPriorityQueueSize(Constants.PRIORITY_QUEUE_FOR_GMB_DISCONNECTED_ENTERPRISE);

			if (queueSize != 0) {
				LOGGER.warn("{} {} In fetchAndStore : Priority queue is not empty",Constants.CHANNEL_DISCONNECT_PREFIX,SocialChannel.GMB.getName());
				return;
				}

			List<Long> disconnectedEnterpriseList = socialGMBRepo.findDistinctEnterpriseIdByIsValid(0);
			redisExternalService.fillPriorityQueue(disconnectedEnterpriseList,Constants.PRIORITY_QUEUE_FOR_GMB_DISCONNECTED_ENTERPRISE);


		}catch(Exception e) {

			LOGGER.error("{} {} Some error occured due to {}",Constants.CHANNEL_DISCONNECT_PREFIX,SocialChannel.GMB.getName(),e.getMessage());

		}


	}
	  @Override
	  public String getConnectCTA() {
		  return "gmb";
	  }

	@Override
	public SmbUnmappedDataResponse getUnmappedGMBPagesForSmb() {
		SmbUnmappedDataResponse smbUnmappedGmbDataResponse = new SmbUnmappedDataResponse();
		smbUnmappedGmbDataResponse.setChannel(SocialChannel.GMB.getName());

		List <Integer> rawIds = new ArrayList<>();
		List<GMBPartialDTO> gmbPages = socialGMBRepo.findPartialSelectedId(1);
		LOGGER.info("Total gmb pages fetched: {}",gmbPages.size());
		if(gmbPages != null && gmbPages.size()>0) {
			List<Long> businessNumberList = gmbPages.stream()
					.map(r->r.getEnterpriseId()).distinct().collect(Collectors.toList());
			List<BusinesIdNumberDTO> businesIdNumberDTOS = new ArrayList<>();
			for(int i = 0; i<businessNumberList.size(); i=i+100) {
				List<Long> nElementsArray = new ArrayList<>();
				if(i+99<businessNumberList.size()) {
					nElementsArray = businessNumberList.subList(i,i+99);
				} else {
					nElementsArray = businessNumberList.subList(i,businessNumberList.size());
				}
				businesIdNumberDTOS.addAll(businessRepo.getAllSMBByBusinessId(nElementsArray));
			}
			if(businesIdNumberDTOS != null && businesIdNumberDTOS.size()>0) {
				LOGGER.info("Total SMB business fetched: {}",businesIdNumberDTOS.size());
				Set<Long> businessNumberSmbList = businesIdNumberDTOS.stream()
						.map(r->r.getBusinessId()).collect(Collectors.toSet());
				List<GMBPartialDTO> smbRawGmbPages = gmbPages.stream().filter(r -> businessNumberSmbList.contains(r.getEnterpriseId())).collect(Collectors.toList());
				List<GMBPartialDTO> unmappedGmbPages = smbRawGmbPages.stream().filter(gmbPartialDTO -> Objects.isNull(gmbPartialDTO.getBusinessId())).collect(Collectors.toList());
				rawIds = unmappedGmbPages.stream().map(GMBPartialDTO::getId).collect(Collectors.toList());
			}
		}
		smbUnmappedGmbDataResponse.setRawIds(rawIds);
		return smbUnmappedGmbDataResponse;
	}

	@Override
	@Deprecated
	public BusinessGoogleMyBusinessLocation createLocation(CreateLocationRequest req) throws Exception {
		BusinessGoogleMyBusinessLocation gmbPage = socialGMBRepo.findFirstByPlaceIdAndIsValid(req.getPlaceId(), 1);
		if (gmbPage == null) {
			throw new BirdeyeSocialException(ErrorCodes.GMB_PAGE_NOT_FOUND,
					"Valid GMB Page with PlaceId " + req.getPlaceId() + " not found");
		}
		try {
			LOGGER.info("Create Location: request {}", req);
//			if (StringUtils.isNotEmpty(gmbPage.getgMsgLocationStatus()) && StringUtils.isNotEmpty(gmbPage.getgMsgLocationName())) {
//				LOGGER.warn("createLocation: Location is already created for enterpriseId {}", req.getEnterpriseId());
//				return gmbPage;
//			}
			// when a location is moved from one agent to another we will be checking the agent id is null and location status should be other than launched.
			// In case of reconnect the flow will be same.
			if(StringUtils.isEmpty(gmbPage.getgMsgLocationStatus())
					|| GoogleLocationStatus.valueOf(gmbPage.getgMsgLocationStatus()).compareTo(GoogleLocationStatus.CREATED) < 0
					|| (StringUtils.isNotEmpty(gmbPage.getgMsgLocationName())
					&& (!req.getBrandName().split("/")[1].equalsIgnoreCase(gmbPage.getgMsgLocationName().split("/")[1])
					&& (Objects.nonNull(gmbPage.getAgentId())
					&& gmbPage.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.UN_LAUNCHED.name()))))) {
				Location location = googleLocationService.createLocation(req);
				gmbPage.setgMsgLocationStatus(GoogleLocationStatus.CREATED.toString());
				gmbPage.setgMsgLocationName(location.getName());
				gmbPage.setAgentId(req.getAgentId());
				socialGMBRepo.saveAndFlush(gmbPage);
			}
		} catch (GoogleJsonResponseException googlExp) {
			GoogleJsonError googleError = googlExp.getDetails();
			if (googleError.getCode() == 400 && googleError.getMessage().contains(GOOGLE_MESSAGES_LOCATION_ALREADY_CREATED)) {
				LOGGER.info("Location with placeId {} is already created under Brand of enterpriseId {}", req.getPlaceId(), req.getEnterpriseId());

				// Get brandName from GoogleMessagesAgent table
				String brandName = StringUtils.isNotEmpty(req.getBrandName()) ? req.getBrandName() : googleMsgAgentService.getBrandName(req.getAgentId());
				LOGGER.info("brandName of location found as {}", brandName);
				if (StringUtils.isEmpty(brandName)) {
					throw new BirdeyeSocialException("create Location: Brand name of created location cannot be empty");
				}

				// Get Location from Google Lib using placeId and brandName
				Location location = googleLocationService.getLocationByPlaceId(brandName, req.getPlaceId());
				if (location == null ) {
					throw new BirdeyeSocialException("create Location: Location not found in brand");
				}
				// Save the details in raw GMB page
				gmbPage.setgMsgLocationName(location.getName());
				gmbPage.setgMsgLocationStatus(GoogleLocationStatus.CREATED.name());
				gmbPage.setgMsgLocationComment("Location has been restored for brand");
				gmbPage.setAgentId(req.getAgentId());
				socialGMBRepo.saveAndFlush(gmbPage);
			} else {
				gmbPage.setgMsgLocationStatus(GoogleLocationStatus.CREATION_ERROR.toString());
				gmbPage.setgMsgLocationComment(parseGoogleErrorMessage(googlExp,req));
				gmbPage.setAgentId(req.getAgentId());
				socialGMBRepo.saveAndFlush(gmbPage);
				throw googlExp;
			}
		} catch(Exception e) {
			LOGGER.error("create Location: error {}", e);
			gmbPage.setgMsgLocationStatus(GoogleLocationStatus.CREATION_ERROR.toString());
			gmbPage.setgMsgLocationComment(parseGoogleErrorMessage(e,req));
			gmbPage.setAgentId(req.getAgentId());
			socialGMBRepo.saveAndFlush(gmbPage);
//			updateRedisDataWithError(req.getEnterpriseId(),req.getAgentId(),req.getPlaceId());
			throw e;
		}finally {
			LOGGER.info(String.format("Sending validity check for location {} from %s","createLocation"),gmbPage.getLocationId());
			createAuditRequest(gmbPage.getPlaceId(),gmbPage.getLocationId(),gmbPage.getLocationName(),gmbPage.getgMsgLocationStatus(),
					gmbPage.getgMsgLocationComment(),req.getBrandName(),req.getAgentName(),req.getAgentId(),GoogleAgentStatus.AGENT_CREATED.name());
			googleMyBusinessPageService.pushToKafkaForValidity(Constants.GMB,gmbPage.getLocationId());
		}
		return gmbPage;
	}

	public void createAuditRequest(String placeId,String locationId,String gmsgLocationName,String gmsgLocationStatus,String gsmbLocationComment,
								   String brandName,String agentName, Integer agentId,String agentStatus){
		CreateGoogleMessagingAuditRequest audit=new CreateGoogleMessagingAuditRequest();
		audit.setGsmbLocationStatus(gmsgLocationStatus);
		audit.setAgentId(agentId);
		audit.setGsmbComment(gsmbLocationComment);
		audit.setGsmbLocationName(gmsgLocationName);
		audit.setAgentName(agentName);
		audit.setBrandName(brandName);
		audit.setAgentStatus(agentStatus);
		audit.setPlaceId(placeId);
		audit.setLocationId(locationId);
		googleMsgAgentService.addAuditInfo(audit);
	}

	@Override
	public void requestLocationVerification(BusinessGoogleMyBusinessLocation gmbPage, CreateLocationRequest request) throws Exception {
		LOGGER.info("requestLocationVerification: request {}", request);
		if (gmbPage.getgMsgLocationStatus() != null
				&& GoogleLocationStatus.valueOf(gmbPage.getgMsgLocationStatus()).compareTo(GoogleLocationStatus.VERIFIED) >= 0) {
			LOGGER.warn("requestAgentVerification: Location already exists with VERIFIED status or above for page: {}",request.getPlaceId());
			return;
		}
		try {
			if(gmbPage.getgMsgLocationStatus() != null
					&& StringUtils.isNotEmpty(gmbPage.getgMsgLocationName())
					&& (gmbPage.getgMsgLocationName().split("/")[1].equalsIgnoreCase(request.getBrandName().split("/")[1]))
					&& (gmbPage.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.CREATED.name())
					|| gmbPage.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.VERIFICATION_ERROR.name()))) {
				googleLocationService.requestLocationVerification(gmbPage, googleAccessTokenCache.getGoogleAccessToken(gmbPage));
				gmbPage.setgMsgLocationStatus(GoogleLocationStatus.VERIFIED.toString());
				socialGMBRepo.saveAndFlush(gmbPage);
			}
		} catch (GoogleJsonResponseException googleExp) {
			// Check if this error is related to location already present in verified state
			GoogleJsonError googleJsonError = googleExp.getDetails();
			if (Objects.nonNull(googleJsonError) && googleJsonError.getCode() == 400 && googleJsonError.getMessage().contains(GOOGLE_MESSAGES_LOCATION_ALREADY_VERIFIED)) {
				LOGGER.warn("requestLocationVerification: Location is already verified. Ignoring google error...");
				gmbPage.setgMsgLocationStatus(GoogleLocationStatus.VERIFIED.name());
				gmbPage.setgMsgLocationComment("Location verification state restored");
				socialGMBRepo.saveAndFlush(gmbPage);
			} else {
				gmbPage.setgMsgLocationStatus(GoogleLocationStatus.VERIFICATION_ERROR.toString());
				gmbPage.setgMsgLocationComment(parseGoogleErrorMessage(googleExp,request));
				socialGMBRepo.saveAndFlush(gmbPage);
				throw googleExp;
			}
		} catch (Exception e) {
			gmbPage.setgMsgLocationStatus(GoogleLocationStatus.VERIFICATION_ERROR.toString());
			gmbPage.setgMsgLocationComment(parseGoogleErrorMessage(e,request));
			socialGMBRepo.saveAndFlush(gmbPage);
//			updateRedisDataWithError(request.getEnterpriseId(),request.getAgentId(),request.getPlaceId());
			throw e;
		}finally {
			LOGGER.info(String.format("Sending validity check for location {} from %s","requestLocationVerification"),gmbPage.getLocationId());
			createAuditRequest(gmbPage.getPlaceId(),gmbPage.getLocationId(),gmbPage.getLocationName(),gmbPage.getgMsgLocationStatus(),
					gmbPage.getgMsgLocationComment(),request.getBrandName(),request.getAgentName(),request.getAgentId(),GoogleAgentStatus.AGENT_CREATED.name());
			googleMyBusinessPageService.pushToKafkaForValidity(Constants.GMB,gmbPage.getLocationId());
		}
	}

	@Override
	public void requestLocationLaunch(BusinessGoogleMyBusinessLocation gmbPage, CreateLocationRequest request) throws Exception {
		LOGGER.info("requestLocationLaunch: request {}", request);
		if (gmbPage.getgMsgLocationStatus() != null && gmbPage.getgMsgLocationStatus().equals(GoogleLocationStatus.LAUNCHED.name())) {
			LOGGER.warn("requestLocationLaunch: location already exists with LAUNCHED status");
			return;
		}
		try {
			if(gmbPage.getgMsgLocationStatus() != null
					&& StringUtils.isNotEmpty(gmbPage.getgMsgLocationName())
					&& (gmbPage.getgMsgLocationName().split("/")[1].equalsIgnoreCase(request.getBrandName().split("/")[1]))
					&& (gmbPage.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.VERIFIED.name())
					|| gmbPage.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCH_ERROR.name())
					|| gmbPage.getgMsgLocationStatus().equalsIgnoreCase(GoogleLocationStatus.UN_LAUNCHED.name()))) {
				googleLocationService.requestLocationLaunch(gmbPage);
				gmbPage.setgMsgLocationStatus(GoogleLocationStatus.LAUNCHED.toString());
				gmbPage.setIsMessagingEnabled(1);
				socialGMBRepo.saveAndFlush(gmbPage);
			}
		} catch (GoogleJsonResponseException googleExp) {
			GoogleJsonError googleError = googleExp.getDetails();
			if (Objects.nonNull(googleError) && googleError.getCode() == 400 && googleError.getMessage().contains(GOOGLE_MESSAGES_LOCATION_ALREADY_LAUNCHED)) {
				LOGGER.warn("requestLocationVerification: Location is already launched. Ignoring google error...");
				gmbPage.setgMsgLocationStatus(GoogleLocationStatus.LAUNCHED.name());
				gmbPage.setgMsgLocationComment("Location launched state restored");
				socialGMBRepo.saveAndFlush(gmbPage);
			}  else {
				gmbPage.setgMsgLocationStatus(GoogleLocationStatus.LAUNCH_ERROR.toString());
				gmbPage.setgMsgLocationComment(parseGoogleErrorMessage(googleExp, request));
				socialGMBRepo.saveAndFlush(gmbPage);
				throw googleExp;
			}
		} catch (Exception e) {
			gmbPage.setgMsgLocationStatus(GoogleLocationStatus.UN_LAUNCHED.toString());
			String errorMsg = parseGoogleErrorMessage(e,request);
			gmbPage.setgMsgLocationComment(errorMsg);
			socialGMBRepo.saveAndFlush(gmbPage);
//			updateRedisDataWithError(request.getEnterpriseId(),request.getAgentId(),request.getPlaceId());
			throw e;
		}finally {
			LOGGER.info(String.format("Sending validity check for location {} from %s","requestLocationLaunch"),gmbPage.getLocationId());
			createAuditRequest(gmbPage.getPlaceId(),gmbPage.getLocationId(),gmbPage.getLocationName(),gmbPage.getgMsgLocationStatus(),
					gmbPage.getgMsgLocationComment(),request.getBrandName(),request.getAgentName(),request.getAgentId(),GoogleAgentStatus.AGENT_CREATED.name());
			googleMyBusinessPageService.pushToKafkaForValidity(Constants.GMB,gmbPage.getLocationId());
		}
	}

	private String parseGoogleErrorMessage(Exception exp, CreateLocationRequest request) {
		String response = "";
		if (exp instanceof GoogleJsonResponseException) {
			final GoogleJsonResponseException googleExp = (GoogleJsonResponseException) exp;
			final GoogleJsonError googleJsonError = googleExp.getDetails();
			LOGGER.error("handleGoogleError: {}", googleJsonError);
			if(googleJsonError != null && googleJsonError.getMessage() != null) {
				response = googleJsonError.getMessage();
			}
		} else {
			response = exp.getMessage();
		}
		if(response != null) {
			int maxLength = Math.min(response.length(), 300);
			response = response.substring(0, maxLength);
			LOGGER.info("response: {}",response);
//			if(request.getPostAutoMapping() != null && request.getPostAutoMapping().equals(true))
//				pushGmbAutoMappingInFirebase(request.getEnterpriseId(),Status.COMPLETE.getName());
//			if(response.contains(ALREADY_LOCATION_LAUNCHED_ERROR_STRING) || response.contains(ALREADY_LOCATION_LAUNCHED_ERROR_WITH_SAME_AGENT)) {
//				pushgMsgLocationLaunchInFirebase(request.getEnterpriseId(),request.getAgentId(),GoogleLocationResponseStatus.LOCATION_ALREADY_LAUNCHED.name());
//			} else {
//				pushgMsgLocationLaunchInFirebase(request.getEnterpriseId(),request.getAgentId(), GoogleLocationResponseStatus.ERROR.name());
//			}
		}
		return response;
	}

	@Deprecated
	private void pushgMsgLocationLaunchInFirebase(Long enterpriseId, Integer agentId,String status) {
		//LOCATIONLAUNCH+"/"+enterpriseId+"/"+agentId
		BusinessLiteDTO business = null;
		try {
			business = businessCoreService.getBusinessLiteByNumber(enterpriseId);
		} catch (Exception e) {
			LOGGER.info("Error while getting businessInfo: {}",e.getLocalizedMessage());
		}
		if(Objects.nonNull(business)) {
			String topicSocial;
			if (checkBusinessSMB(business)) {
				topicSocial = LOCATIONLAUNCH + "/" + enterpriseId;
			} else {
				topicSocial = LOCATIONLAUNCH + "/" + enterpriseId + "/" + agentId;
			}
			nexusService.insertMapInFirebase(topicSocial, "locationLaunchStatus", status);
		}
	}

	private void updateRedisDataWithError(Long enterpriseId,Integer agentId, String existingPlaceId) {
		//LOCATIONLAUNCH+"/"+enterpriseId+"/"+agentId
		String key = prepareRedisAndFirebaseKeyForGmbMsg(enterpriseId,agentId);
		Optional<Object> redisValue = redisExternalService.get(key);
		if(redisValue.isPresent()) {
			String placeIdStr = (String) redisValue.get();
			placeIdStr = placeIdStr.replace("[","").replace("]","");
			List<String> placeIds = new ArrayList<>(Arrays.asList(placeIdStr.split(",")));
			List<String> updatedplaceIds = new ArrayList<>();
			placeIds.forEach(placeId->{
				if(!placeId.equalsIgnoreCase(existingPlaceId)) {
					updatedplaceIds.add(placeId);
				}
			});
			if(updatedplaceIds.size()>0) {
				redisExternalService.set(key,updatedplaceIds.toString());
			}
		}
	}

	private void updateRedisData(CreateLocationRequest request) {
		String key = prepareRedisAndFirebaseKeyForGmbMsg(request.getEnterpriseId(),request.getAgentId());
		Optional<Object> redisValue = redisExternalService.get(key);
		if(!redisValue.isPresent()){
			LOGGER.info("No value found for key :{}",key);
			return;
		}
		String placeIdStr = (String) redisValue.get();
		placeIdStr = placeIdStr.replace("[","").replace("]","");
		List<String> placeIds = new ArrayList<>(Arrays.asList(placeIdStr.split(",")));
		LOGGER.info("Place Ids : {}",placeIds);
		if(CollectionUtils.isNotEmpty(placeIds)) {
			String reqPlaceId = StringUtils.isNotEmpty(request.getPlaceId()) ? request.getPlaceId().trim() : request.getPlaceId();
			placeIds.removeIf(placeId -> StringUtils.isNotEmpty(placeId) && placeId.trim().equalsIgnoreCase(reqPlaceId));
		}
		if(CollectionUtils.isNotEmpty(placeIds)) {
			LOGGER.info("Place ids : {} for key : {}",placeIds,key);
			redisExternalService.set(key, String.valueOf(placeIds));
			return;
		}
		LOGGER.info("Delete the key from redis :{}",key);
		redisExternalService.delete(key);
		// Check if there exists ANY invalid mapping
		pushToFirebaseLocationStatus(request.getAgentId(),request.getEnterpriseId());
	}

	private String prepareRedisAndFirebaseKeyForGmbMsg(Long enterpriseId, Integer agentId) {
		return LOCATIONLAUNCH + "/" + enterpriseId + "/" + agentId;
	}

	@Override
	public void setupLocation(CreateLocationRequest req) {
		LOGGER.info("setupLocation: req {}", req);
		GoogleMessagesAgent googleMessagesAgent = new GoogleMessagesAgent();
		BusinessGoogleMyBusinessLocation gmbPage = new BusinessGoogleMyBusinessLocation();
		try {
			googleMessagesAgent = agentRepo.findOne(req.getAgentId());
			if(Objects.isNull(googleMessagesAgent) || StringUtils.isEmpty(googleMessagesAgent.getStatus())
					|| !googleMessagesAgent.getStatus().equalsIgnoreCase(GoogleAgentStatus.LAUNCHED.name())){
				LOGGER.info("Agent is null or status is not launched");
				throw new BirdeyeSocialException("Error occurred while setup location :"+req.getPlaceId());
			}
			gmbPage = createLocation(req);
			requestLocationVerification(gmbPage, req);
			requestLocationLaunch(gmbPage, req);
		}catch (Exception e){
			LOGGER.info("Error occurred while setup location :{} with error",req.getPlaceId(),e);
//			throw new BirdeyeSocialException("Error occurred while setup location :"+req.getPlaceId());
		}finally {
			updateRedisData(req);
			if (Objects.isNull(googleMessagesAgent) || Objects.isNull(gmbPage)){
				createAuditRequest(req.getPlaceId(),null,null,null,
						"Error occured while location setup",req.getBrandName(),req.getAgentName(),req.getAgentId(),GoogleAgentStatus.LAUNCH_ERROR.name());
			}else {
				createAuditRequest(gmbPage.getPlaceId(), gmbPage.getLocationId(), gmbPage.getLocationName(), gmbPage.getgMsgLocationStatus(),
						gmbPage.getgMsgLocationComment(), googleMessagesAgent.getBrandName(), googleMessagesAgent.getAgentName(), googleMessagesAgent.getId(), googleMessagesAgent.getStatus());
			}
		}
	}

	@Override
	@Deprecated
	public void unLaunchLocation(LocationUnlaunchRequest request) throws Exception {
		LOGGER.info("requestLocationUnLaunch: request: {}", request);
		GoogleMessagesAgent googleMessagesAgent = agentRepo.findOne(request.getAgentId());
		BusinessGoogleMyBusinessLocation gmbPage = null;

		if (StringUtils.isNotEmpty(request.getPageId()))
			gmbPage = socialGMBRepo.findFirstByPlaceIdAndIsValid(request.getPageId(),1);

		if(gmbPage != null) {
			if (gmbPage.getgMsgLocationStatus() != null && gmbPage.getgMsgLocationStatus().equals(GoogleLocationStatus.UN_LAUNCHED.name())) {
				LOGGER.warn("RequestUnLaunchRequest: Agent already exists with UN-LAUNCHED status");
			} else {
				String locationName = gmbPage.getgMsgLocationName();
				if (StringUtils.isEmpty(locationName)) {
					final String brandName = googleMsgAgentService.getBrandName(request.getAgentId());
					if (StringUtils.isNotEmpty(brandName)) {
						final Location location = googleLocationService.getLocationByPlaceId(brandName, request.getPageId());
						if (location != null) {
							locationName = location.getName();
						} else {
							LOGGER.warn("Location is null and cannot be fetched...");
						}
					} else {
						LOGGER.warn("Brand name is null for enterpriseId {}", request.getEnterpriseId());
					}
				}
				if (StringUtils.isNotEmpty(locationName)) {
					try {
						if(GoogleLocationStatus.VERIFIED.name().compareTo(gmbPage.getgMsgLocationStatus())>=0) {
							googleLocationService.requestLocationUnLaunch(locationName);
							gmbPage.setgMsgLocationStatus(GoogleLocationStatus.UN_LAUNCHED.toString());
						}else{
							googleLocationService.deleteLocation(locationName);
							gmbPage.setgMsgLocationStatus(null);
						}
//						gmbPage.setAgentId(null);
						gmbPage.setIsMessagingEnabled(0);
						socialGMBRepo.saveAndFlush(gmbPage);
					} catch (Exception e) {
						LOGGER.error("unlaunch location: error", e);
						gmbPage.setgMsgLocationStatus(GoogleLocationStatus.LAUNCH_ERROR.toString());
						gmbPage.setgMsgLocationComment(e.getMessage().substring(0, Math.min(e.getMessage().length(), 300)));
//						gmbPage.setAgentId(null);
						socialGMBRepo.saveAndFlush(gmbPage);
						throw e;
					} finally {
						updateFirebaseLocationLaunchState(request.getEnterpriseId(),request.getAgentId());
						LOGGER.info(String.format("Sending validity check for location {} from %s","unLaunchLocation"),gmbPage.getLocationId());
						if (Objects.isNull(googleMessagesAgent)){
							createAuditRequest(null,request.getPageId(),null,null,
									"Error occured while unlaunch location ",null,null,request.getAgentId(),"UNLAUNCH_ERROR");
						}
						else {
							createAuditRequest(gmbPage.getPlaceId(), gmbPage.getLocationId(), gmbPage.getLocationName(), gmbPage.getgMsgLocationStatus(),
									gmbPage.getgMsgLocationComment(), googleMessagesAgent.getBrandName(), googleMessagesAgent.getAgentName(), request.getAgentId(), "LOCATION_UNLAUNCHED");
						}
						googleMyBusinessPageService.pushToKafkaForValidity(Constants.GMB, gmbPage.getLocationId());
					}
				} else {
					LOGGER.warn("LocationName is empty and cannot be fetched...");
				}
			}
		}
	}

	@Override
	public void unLaunchLocationAndUpdateAgentId(LocationUnlaunchRequest request) {
		LOGGER.info("requestLocationUnLaunch: request: {}", request);

		List<BusinessGoogleMyBusinessLocation> gmbPages = socialGMBRepo.findByLocationIdAndAgentId(request.getPageId(),request.getAgentId());
		if(CollectionUtils.isEmpty(gmbPages)){
			LOGGER.info("No page found for request : {}",request);
			throw new BirdeyeSocialException(ErrorCodes.REQUEST_ENTITY_NOT_FOUND, "No page found with given agent id and location id");
		}
		BusinessGoogleMyBusinessLocation page = gmbPages.get(0);
		String pageIds = page.getPlaceId();
		if (StringUtils.isEmpty(pageIds) || Objects.isNull(page.getAgentId())) {
			return;
		}
		GoogleMessagesAgent agent = agentRepo.findOne(page.getAgentId());
		if (Objects.isNull(agent) || StringUtils.isEmpty(agent.getStatus()) || GoogleAgentStatus.valueOf(agent.getStatus()).compareTo(GoogleAgentStatus.AGENT_CREATED) <= 0 ) {
			return;
		}
		if (StringUtils.isEmpty(agent.getBrandName()) || GoogleAgentStatus.UN_LAUNCHED.name().equalsIgnoreCase(agent.getStatus())) {
			LOGGER.warn("RequestUnLaunchRequest: Agent already exists with UN-LAUNCHED status : {}", agent.getId());
			// need to discuss this if required or not
			socialGMBRepo.updateLocationStatusAndAgentId(pageIds, GoogleAgentStatus.UN_LAUNCHED.name());
			updateFirebaseLocationLaunchState(page.getEnterpriseId(),request.getAgentId());
			return;
		}
		String brandName = agent.getBrandName();
		try {
			if(StringUtils.isEmpty(page.getgMsgLocationStatus())){
				LOGGER.warn("RequestUnLaunchRequest: Agent already exists with empty status : {}",page.getPlaceId());
				socialGMBRepo.updateAgentIdToNull(page.getPlaceId());
				return;
			}
			if (GoogleLocationStatus.UN_LAUNCHED.name().equalsIgnoreCase(page.getgMsgLocationStatus())) {
				LOGGER.warn("RequestUnLaunchRequest: Agent already exists with UN-LAUNCHED status : {}",page.getPlaceId());
				socialGMBRepo.updateLocationStatusAndAgentId(page.getPlaceId(), GoogleAgentStatus.UN_LAUNCHED.name());
				return;
			}
			String locationName = page.getgMsgLocationName();
			if (StringUtils.isEmpty(locationName)) {
				final Location location = googleLocationService.getLocationByPlaceId(brandName, page.getPlaceId());
				if (location != null) {
					locationName = location.getName();
				}
			}
			if (StringUtils.isEmpty(locationName)) {
				LOGGER.warn("LocationName is empty and cannot be fetched for page id : {}", page.getPlaceId());
				return;
			}
			try {
				if(GoogleLocationStatus.VERIFIED.name().compareTo(page.getgMsgLocationStatus())>=0) {
					googleLocationService.requestLocationUnLaunch(locationName);
					page.setgMsgLocationStatus(GoogleLocationStatus.UN_LAUNCHED.toString());
				}else{
					googleLocationService.deleteLocation(locationName);
					page.setgMsgLocationStatus(null);
				}
				page.setIsMessagingEnabled(0);
				page.setAgentId(null);
				socialGMBRepo.saveAndFlush(page);
			} catch (Exception e) {
				LOGGER.error("un-launch location: place id : {} with error", page.getPlaceId(), e);
				page.setgMsgLocationStatus(GoogleLocationStatus.LAUNCH_ERROR.toString());
				page.setgMsgLocationComment(e.getMessage().substring(0, Math.min(e.getMessage().length(), 300)));
				page.setAgentId(null);
				socialGMBRepo.saveAndFlush(page);
				throw e;
			} finally {
				LOGGER.info(String.format("Sending validity check for location {} from %s", "unLaunchLocation"), page.getLocationId());
				googleMyBusinessPageService.pushToKafkaForValidity(Constants.GMB, page.getLocationId());
			}
		}catch (Exception e){
			LOGGER.info("Error occurred while un-launch location : {}",e.getMessage());
		}finally {
			createAuditRequest(page.getPlaceId(),page.getLocationId(),page.getLocationName(),page.getgMsgLocationStatus(),
					page.getgMsgLocationComment(),agent.getBrandName(),agent.getAgentName(),request.getAgentId(),null);
			updateFirebaseLocationLaunchState(gmbPages.get(0).getEnterpriseId(), request.getAgentId());
		}
	}

	@Override
	@Deprecated
	public void unLaunchLocationByAgentId(LocationBulkUnlaunchRequest request,List<BusinessGoogleMyBusinessLocation> gmbPages) {
		LOGGER.info("requestLocationUnLaunch: request: {}", request);
		if(CollectionUtils.isEmpty(gmbPages)){
			return;
		}
		List<String> pageIds = gmbPages.stream().map(BusinessGoogleMyBusinessLocation::getPlaceId).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(pageIds) || Objects.isNull(request.getAgentId())) {
			return;
		}
		GoogleMessagesAgent agent = agentRepo.findOne(request.getAgentId());
		if (Objects.isNull(agent) || StringUtils.isEmpty(agent.getStatus()) || GoogleAgentStatus.valueOf(agent.getStatus()).compareTo(GoogleAgentStatus.AGENT_CREATED) <= 0 ) {
			return;
		}

		if (StringUtils.isEmpty(agent.getBrandName()) || GoogleAgentStatus.UN_LAUNCHED.name().equalsIgnoreCase(agent.getStatus())) {
			LOGGER.warn("RequestUnLaunchRequest: Agent already exists with UN-LAUNCHED status : {}", agent.getId());
			// need to discuss this if required or not
			socialGMBRepo.updateLocationStatus(pageIds, GoogleAgentStatus.UN_LAUNCHED.name());
			updateFirebaseLocationLaunchState(gmbPages.get(0).getEnterpriseId(),request.getAgentId());
			createAuditRequest(null,null,null,null,
					"All locations are un-launched",agent.getBrandName(),agent.getAgentName(),request.getAgentId(),agent.getStatus());
			return;
		}
		String brandName = agent.getBrandName();
		try {
			for (BusinessGoogleMyBusinessLocation gmbPage : gmbPages) {
				if ((StringUtils.isEmpty(gmbPage.getgMsgLocationStatus()))
						|| GoogleLocationStatus.UN_LAUNCHED.name().equalsIgnoreCase(gmbPage.getgMsgLocationStatus())) {
					LOGGER.warn("RequestUnLaunchRequest: Agent already exists with UN-LAUNCHED status");
					continue;
				}
				String locationName = gmbPage.getgMsgLocationName();
				if (StringUtils.isEmpty(locationName)) {
					final Location location = googleLocationService.getLocationByPlaceId(brandName, gmbPage.getPlaceId());
					if (location != null) {
						locationName = location.getName();
					}
				}
				if (StringUtils.isEmpty(locationName)) {
					LOGGER.warn("LocationName is empty and cannot be fetched for page id : {}", gmbPage.getPlaceId());
					continue;
				}
				try {
					if(GoogleLocationStatus.VERIFIED.name().compareTo(gmbPage.getgMsgLocationStatus())>=0) {
						googleLocationService.requestLocationUnLaunch(locationName);
						gmbPage.setgMsgLocationStatus(GoogleLocationStatus.UN_LAUNCHED.toString());
					}else{
						googleLocationService.deleteLocation(locationName);
						gmbPage.setgMsgLocationStatus(null);
					}
					gmbPage.setIsMessagingEnabled(0);
//					gmbPage.setAgentId(null);
					socialGMBRepo.saveAndFlush(gmbPage);
				} catch (Exception e) {
					LOGGER.error("un-launch location: place id : {} with error", gmbPage.getPlaceId(), e);
					gmbPage.setgMsgLocationStatus(GoogleLocationStatus.LAUNCH_ERROR.toString());
					gmbPage.setgMsgLocationComment(e.getMessage().substring(0, Math.min(e.getMessage().length(), 300)));
//					gmbPage.setAgentId(null);
					socialGMBRepo.saveAndFlush(gmbPage);
					throw e;
				} finally {
					LOGGER.info(String.format("Sending validity check for location {} from %s", "unLaunchLocation"), gmbPage.getLocationId());
					createAuditRequest(gmbPage.getPlaceId(),gmbPage.getLocationId(),gmbPage.getLocationName(),gmbPage.getgMsgLocationStatus(),
							gmbPage.getgMsgLocationComment(),agent.getBrandName(),agent.getAgentName(),request.getAgentId(),agent.getStatus());
					googleMyBusinessPageService.pushToKafkaForValidity(Constants.GMB, gmbPage.getLocationId());
				}
			}
		}catch (Exception e){
			LOGGER.info("Error occurred while un-launch location : {}",e.getMessage());
		}finally {
			updateFirebaseLocationLaunchState(gmbPages.get(0).getEnterpriseId(), request.getAgentId());
		}
	}


	@Deprecated
	private void updateFirebaseLocationLaunchState(Long enterpriseId,Integer agentId) {
		if (Objects.isNull(enterpriseId) || Objects.isNull(agentId)) {
			LOGGER.info("Enterprise id or agent id can not be null");
			return;
		}
		Set<BusinessGoogleMyBusinessLocation> erroneousPages = socialGMBRepo.findAllErroneousPageIdsByAgentId(agentId);
		pushGmbAutoMappingInFirebase(enterpriseId,Status.COMPLETE.getName());
		if (CollectionUtils.isNotEmpty(erroneousPages)) {
			LOGGER.info("unLaunch location: Invalid mappings found...");
			pushgMsgLocationLaunchInFirebase(enterpriseId,agentId, GoogleLocationResponseStatus.ERROR.name());
		} else {
			LOGGER.info("unLaunch location: No invalid mappings found for agent id : {}",agentId);
			boolean isValidLaunchedLocationPresent =
					socialGMBRepo.existsByAgentIdAndIsValidAndGMsgLocationStatus(agentId, 1, GoogleLocationStatus.LAUNCHED.name());
			LOGGER.info("unLaunch location: isValidLaunchedLocationPresent {} for agent id :{}", isValidLaunchedLocationPresent,agentId);
			if (isValidLaunchedLocationPresent) {
				pushgMsgLocationLaunchInFirebase(enterpriseId,agentId,GoogleLocationResponseStatus.COMPLETE.name());
			} else {
				pushgMsgLocationLaunchInFirebase(enterpriseId,agentId, GoogleLocationResponseStatus.UNLAUNCHED.name());
			}
		}
	}


	@Override
	@Async
	public void initSetupLocation(Long enterpriseId,GoogleMessagesAgent gmbAgent,List<BusinessGoogleMyBusinessLocation> gmPages, Boolean postAutoMapping) {
		if(Objects.isNull(gmbAgent) || StringUtils.isEmpty(gmbAgent.getStatus())
				|| GoogleAgentStatus.valueOf(gmbAgent.getStatus()).compareTo(GoogleAgentStatus.VERIFICATION_ERROR) < 0
				|| CollectionUtils.isEmpty(gmPages)){
			pushGmbAutoMappingInFirebase(enterpriseId,Status.COMPLETE.getName());
			LOGGER.warn("Agent not launched for enterprise {}", enterpriseId);
			return;
		}
		LOGGER.info("Agent found, Initiating Setup Location for enterprise: {} and agent id : {}",enterpriseId,gmbAgent.getId());
		List<String> keyList = gmPages.stream().map(BusinessGoogleMyBusinessLocation::getPlaceId).filter(Objects::nonNull).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(keyList)){
			LOGGER.info("Place ids for the given agent is empty for agent {}",gmbAgent.getId());
			return;
		}
		pushgMsgLocationLaunchInFirebase(enterpriseId,gmbAgent.getId(),GoogleLocationResponseStatus.INIT.name());
		// Redis key is set to agent id and values are list of location ids
		String key = prepareRedisAndFirebaseKeyForGmbMsg(enterpriseId,gmbAgent.getId());
		redisExternalService.set(key, keyList.toString());
		gmPages.forEach(gmbPage -> {
			CreateLocationRequest req = new CreateLocationRequest();
			req.setPlaceId(gmbPage.getPlaceId());
			req.setEnterpriseId(gmbPage.getEnterpriseId());
			req.setAgentId(gmbAgent.getId());
			req.setAgentName(gmbAgent.getAgentName());
			req.setBrandName(gmbAgent.getBrandName());
			req.setPostAutoMapping(postAutoMapping);
			producer.sendObjectV1(TOPIC_LOCATION_SETUP, req);
		});
	}

	@Override
	@Async
	public void initSetupLocationForBusinessIds(Integer agentId, Long enterpriseId, List<BusinessGoogleMyBusinessLocation> gmPages, Boolean postAutoMapping) {
		final GoogleMessagesAgent gmbAgent = googleMsgAgentService.getAgentById(agentId);
		if (gmbAgent != null && gmbAgent.getStatus() != null && gmbAgent.getStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCHED.toString())) {
			LOGGER.info("Agent found, Initiating Setup Location for enterprise: {}",enterpriseId);
			List<String> keyList = gmPages.stream().map(BusinessGoogleMyBusinessLocation::getPlaceId).collect(Collectors.toList());
			String key = LOCATIONLAUNCH + "/" + gmPages.get(0).getEnterpriseId();
			Optional<Object> redisValue = redisExternalService.get(key);
			redisExternalService.set(key, keyList.toString());
			gmPages.stream().forEach(gmbPage -> {
				CreateLocationRequest req = new CreateLocationRequest();
				req.setPlaceId(gmbPage.getPlaceId());
				req.setEnterpriseId(gmbPage.getEnterpriseId());
				req.setAgentName(gmbAgent.getAgentName());
				req.setBrandName(gmbAgent.getBrandName());
				req.setPostAutoMapping(postAutoMapping);
				req.setAgentId(agentId);
				producer.sendObject(TOPIC_LOCATION_SETUP, req);
			});
		} else {
			pushGmbAutoMappingInFirebase(enterpriseId,Status.COMPLETE.getName());
			LOGGER.warn("Agent not launched for enterprise", enterpriseId);
		}
	}

	@Override
	public List<?> fetchRawPages(List<String> integrationIds) {
		return socialGMBRepo.findByLocationIdIn(integrationIds);
	}

	@Override
	public List<Number> fetchRawPagesId(List<String> integrationIds) {
		return socialGMBRepo.findIdByLocationIn(integrationIds);
	}

	@Override
	public List<SocialElasticDto> fetchPagesEsDto() {
		List<?> businessGoogleMyBusinessLocations = socialGMBRepo.findByIsSelected(1);
		return SocialElasticUtil.getSocialEsDtoFromObjects(businessGoogleMyBusinessLocations,SocialChannel.GMB.getName());
	}

	@Override
	public List<SocialElasticDto> fetchPagesEsDto(Integer id) {
		BusinessGoogleMyBusinessLocation googleMyBusinessLocation = socialGMBRepo.findById(id);
		if(Objects.nonNull(googleMyBusinessLocation)) {
			return SocialElasticUtil.getSocialEsDtoFromObjects(Collections.singletonList(googleMyBusinessLocation), SocialChannel.GMB.getName());
		}
		return Collections.emptyList();
	}

	@Override
	public void submitFetchAccountRequest(ChannelAuthRequest authRequest,String type) {
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat("_group_").concat(String.valueOf(authRequest.getBusinessId()));
		boolean lock = redisService.tryToAcquireLock(key);
		BusinessGetPageRequest request = type.equalsIgnoreCase(Constants.RESELLER)?
				businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(authRequest.getBusinessId(),SocialChannel.GMB.getName(),CONNECT) :
				businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(authRequest.getBusinessId(), SocialChannel.GMB.getName(),CONNECT);
		LOGGER.info("[Redis Lock] Lock status, submitFetchAccountRequest : {} ",lock);
		if(lock) {
			try {
				GoogleAuthToken token = getGoogleToken(authRequest.getBusinessId(), authRequest.getGooglePlusCode(), authRequest.getRedirectUri());
				LOGGER.info("generated  GoogleAuthToken = {}", token);
				List<String> statusList = Arrays.asList(Status.ACCOUNT_INITIAL.getName(),Status.ACCOUNT_FETCHED.getName(),Status.INITIAL.getName(), Status.FETCHED.getName());
				if (Objects.isNull(request) || !statusList.contains(request.getStatus())) {
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(),CONNECT,Status.ACCOUNT_INITIAL.getName(), authRequest.getBusinessId());
					BusinessGetPageRequest businessGetPageRequest = submitGmbRequest(authRequest.getBusinessId(), authRequest.getBirdeyeUserId(), token,Status.ACCOUNT_INITIAL.getName(),type);
					googleMyBusinessPageService.fetchGmbAccounts(businessGetPageRequest, token,"db",authRequest.getBusinessId());
				}else{
					LOGGER.info("[GMB] BusinessGetPageRequest found with status {} for enterprise id {}",request.getStatus(),authRequest.getBusinessId());
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),request.getStatus(),authRequest.getBusinessId());
					redisService.release(key);
				}
			}catch(Exception e) {
				// Cleanup redis cache for error cases. e.g. Google Access Token
				redisService.release(key);
				LOGGER.error("[Redis Lock] (Google) Lock released for business {} {}", authRequest.getBusinessId(),e);
				BusinessGetPageRequest reqGMB = null;
				List<BusinessGetPageRequest> businessGetPageRequests = type.equalsIgnoreCase(Constants.RESELLER)?
						getRequestForReseller(authRequest.getBusinessId(),Status.ACCOUNT_INITIAL.getName(), SocialChannel.GMB.getName(), CONNECT):
						getRequestForBusiness(authRequest.getBusinessId(),Status.ACCOUNT_INITIAL.getName(), SocialChannel.GMB.getName(), CONNECT);;
				if(CollectionUtils.isNotEmpty(businessGetPageRequests)){
					reqGMB = businessGetPageRequests.get(0);
				}
				if (reqGMB != null) {
					reqGMB.setStatus(Status.CANCEL.getName());
					businessGetPageReqRepo.saveAndFlush(reqGMB);
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(),reqGMB.getRequestType(),Status.COMPLETE.getName(),authRequest.getBusinessId(),true);
				}else{
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(),CONNECT,Status.COMPLETE.getName(),authRequest.getBusinessId(),true);
				}
			}
		}else{
			LOGGER.info("[Redis Lock] (Google account) Lock is already acquired for business {}", authRequest.getBusinessId());
			handleFailureLock(request,SocialChannel.GMB.getName(),key,authRequest.getBusinessId(),CONNECT);
		}
	}

	@Override
	public SSOResponse getGoogleSSOAuthDetails(GoogleSSOAuthRequest authRequest) {
		SocialAppCredsInfo domainGoogleInfo = socialAppService.getGoogleAppSettings();
		try {
			GoogleAuthToken token = googleAuthenticationService.generateGoogleTokensUsingCode(domainGoogleInfo.getChannelClientId(), domainGoogleInfo.getChannelClientSecret(), authRequest.getGCode(), authRequest.getRedirectUri());
			if (Objects.isNull(token)) {
				throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN);
			}
			GoogleProfileResponse user = googlePlusService.getUserDetailsForGoogleUser(token.getAccess_token());
			if(Objects.nonNull(user)) {
				token.setUserId(user.getId());
				token.setUserName(user.getName());
				token.setEmail(user.getEmail());
			}
			SSOResponse ssoResponse = SSOResponse.builder()
					.accessToken(token.getAccess_token())
					.email(token.getEmail())
					.expiresIn(token.getExpires_in())
					.name(user.getName())
					.pictureUrl(user.getPicture())
					.build();
			return ssoResponse;
		} catch (BirdeyeSocialException e) {
			LOGGER.info("error while generating access token: {}", e.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN);
		} catch (Exception e) {
			LOGGER.info("error while generating access token: {}", e.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, e.getMessage());
		}
	}

	@Override
	public GoogleWhiteLabelAccountsResponse getGoogleWhiteLabelAccounts(GoogleWhiteLabelAccountsRequest request) {
		GoogleWhiteLabelAccountsResponse response = new GoogleWhiteLabelAccountsResponse();
		if(Objects.isNull(request) || CollectionUtils.isEmpty(request.getDomains())) {
			return response;
		}

		List<String> whiteLabelUrls = request.getDomains();
		List<GoogleRedirectUrlInfo> whiteLabelUrlsFromDb = googleRedirectUrlRepo.findAll();
		Set<String> uniqueWhiteLabelUrlsFromDb = whiteLabelUrlsFromDb.stream().map(s->s.getGoogleRedirectUrlInfo()).collect(Collectors.toSet());
		Map<String, Integer> whiteLabelMap = new HashMap<>();
		whiteLabelUrls.forEach(url -> {
			if(uniqueWhiteLabelUrlsFromDb.contains("https://"+url)) {
				whiteLabelMap.put(url,1);
			} else {
				whiteLabelMap.put(url,2);
			}
		});

		return new GoogleWhiteLabelAccountsResponse(whiteLabelMap);
	}

	@Override
	public void addWhiteLabelAccounts(List<String> urls) {
		if(CollectionUtils.isEmpty(urls)) return;
		Set<String> uniqueWhiteLabelUrls = new HashSet<>();
		for(String url: urls) {
			try {
				if(url.endsWith("/dashboard/social/callback?active=google")) {
					URI uri = new URI(url);
					String host = uri.getHost();
					String whiteLabelUrl = "https://"+host;
					uniqueWhiteLabelUrls.add(whiteLabelUrl);
				}
			} catch (Exception e) {
				LOGGER.info("cannot get domain from the url: {}", url);
			}
		}
		List<GoogleRedirectUrlInfo> googleRedirectUrlInfoList = new ArrayList<>();
		uniqueWhiteLabelUrls.forEach(url->{
			GoogleRedirectUrlInfo googleRedirectUrlInfo = new GoogleRedirectUrlInfo();
			googleRedirectUrlInfo.setGoogleRedirectUrlInfo(url);
			googleRedirectUrlInfo.setCreatedAt(new Date());
			googleRedirectUrlInfo.setUpdatedAt(new Date());
			LOGGER.info(url);
			googleRedirectUrlInfoList.add(googleRedirectUrlInfo);
		});

		googleRedirectUrlRepo.save(googleRedirectUrlInfoList);
	}

	@Override
	public OpenUrlFetchPageResponse submitFetchAccountRequestOpenUrl(ChannelAuthOpenUrlRequest authRequest, Long businessId) {
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(authRequest.getFirebaseKey()));
		boolean lock = redisService.tryToAcquireLock(key);

		BusinessGetPageOpenUrlRequest request = businessGetPageOpenUrlReqRepo.findFirstByFirebaseKeyOrderByCreatedDescIdDesc(authRequest.getFirebaseKey());
		LOGGER.info("[Redis Lock] Lock status, submitFetchAccountRequestOpenUrl : {} ",lock);

		if(lock) {
			try {
				GoogleAuthToken token = getGoogleToken(businessId, authRequest.getGooglePlusCode(), authRequest.getRedirectUri());
				LOGGER.info("generated  GoogleAuthToken = {}", token);
				List<String> statusList = Arrays.asList(Status.ACCOUNT_INITIAL.getName(),Status.ACCOUNT_FETCHED.getName(),Status.INITIAL.getName(), Status.FETCHED.getName());
				if (Objects.isNull(request) || !statusList.contains(request.getStatus())) {
					//pushCheckStatusInFirebase(SocialChannel.GMB.getName(),CONNECT,Status.ACCOUNT_INITIAL.getName(), businessId);
					nexusService.updateMapInFirebase("socialOpenUrl/"+ authRequest.getFirebaseKey(),authRequest.getFirebaseKey(),Status.ACCOUNT_INITIAL.getName());
					BusinessGetPageOpenUrlRequest businessGetPageOpenUrlRequest = submitGmbOpenUrlRequest(businessId, token,Status.ACCOUNT_INITIAL.getName(), authRequest.getFirebaseKey());
					googleMyBusinessPageService.fetchGmbAccountsForOpenUrl(businessGetPageOpenUrlRequest, token,"db",businessId, authRequest.getFirebaseKey());
					OpenUrlFetchPageResponse response = new OpenUrlFetchPageResponse();
					response.setFirebaseKey(authRequest.getFirebaseKey());
					response.setEmail(businessGetPageOpenUrlRequest.getEmail());
					return response;
				}else{
					LOGGER.info("[GMB] BusinessGetPageOpenUrlRequest found with status {} for enterprise id {}",request.getStatus(),businessId);
					//pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),request.getStatus(),businessId);
					nexusService.updateMapInFirebase("socialOpenUrl/"+ authRequest.getFirebaseKey(),authRequest.getFirebaseKey(),request.getStatus());
					redisService.release(key);
					OpenUrlFetchPageResponse response = new OpenUrlFetchPageResponse();
					response.setFirebaseKey(authRequest.getFirebaseKey());
					return response;

				}
			}catch(Exception e) {
				// Cleanup redis cache for error cases. e.g. Google Access Token
				redisService.release(key);
				LOGGER.error("[Redis Lock] (Google) Lock released for business {} {}",businessId,e);
				BusinessGetPageOpenUrlRequest reqGMB = null;
				List<BusinessGetPageOpenUrlRequest> businessGetPageOpenUrlRequests = getRequestForBusinessOpenUrl(businessId,Status.ACCOUNT_INITIAL.getName(), SocialChannel.GMB.getName(), CONNECT, authRequest.getFirebaseKey());
				if(CollectionUtils.isNotEmpty(businessGetPageOpenUrlRequests)){
					reqGMB = businessGetPageOpenUrlRequests.get(0);
				}
				if (reqGMB != null) {
					reqGMB.setStatus(Status.CANCEL.getName());
					businessGetPageOpenUrlReqRepo.saveAndFlush(reqGMB);
					//pushCheckStatusInFirebase(SocialChannel.GMB.getName(),reqGMB.getRequestType(),Status.COMPLETE.getName(),businessId,true);
					nexusService.updateMapInFirebase("socialOpenUrl/"+ authRequest.getFirebaseKey(),authRequest.getFirebaseKey(),Status.CANCEL.getName());
				}else{
					//pushCheckStatusInFirebase(SocialChannel.GMB.getName(),CONNECT,Status.COMPLETE.getName(),businessId,true);
					nexusService.updateMapInFirebase("socialOpenUrl/"+ authRequest.getFirebaseKey(),authRequest.getFirebaseKey(),Status.CANCEL.getName());
				}
				OpenUrlFetchPageResponse response = new OpenUrlFetchPageResponse();
				response.setFirebaseKey(authRequest.getFirebaseKey());
				return response;
			}
		}else{
			LOGGER.info("[Redis Lock] (Google account) Lock is already acquired for business {}", businessId);
			handleFailureLockForOpenUrl(request,SocialChannel.GMB.getName(),key,businessId,CONNECT, authRequest.getFirebaseKey());
			OpenUrlFetchPageResponse response = new OpenUrlFetchPageResponse();
			response.setFirebaseKey(authRequest.getFirebaseKey());
			return response;
		}
	}

	@Override
	public 	void fetchAccountsFreemium(FreemiumSetupRequest authRequest){
		BusinessGetPageRequest businessGetPageRequest=new BusinessGetPageRequest();
		try {
			//todo add check for duplicate session id in setup request-completed
			BusinessGetPageRequest request=businessGetPageService.findByFreemiumSessionId(authRequest.getFreemiumSessionId());
			if (Objects.nonNull(request)){
				throw new BirdeyeSocialException (ErrorCodes.DUPLICATE_SESSION_ID,authRequest.getFreemiumSessionId().toString());
			}
			LOGGER.info("Request Received to fetch gmb account for freemium");
			GoogleAuthToken token = getGoogleToken(authRequest.getGooglePlusCode(), authRequest.getRedirectUri());
			LOGGER.info("generated  GoogleAuthToken = {}", token);
			//TODO to make account_init to init for status-completed
			businessGetPageRequest = submitFreemiumRequest(token,Status.INITIAL.getName(),"freemium",authRequest.getFreemiumSessionId());
			//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageRequest.getRequestType(),businessGetPageRequest.getStatus(),Long.parseLong(authRequest.getFreemiumSessionId().toString()));
			List<GMBAccountDTO> gmbAccountDTOList=googleMyBusinessPageService.fetchGmbAccountForFreemium(businessGetPageRequest, token);
			//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageRequest.getRequestType(),businessGetPageRequest.getStatus(),Long.parseLong(authRequest.getFreemiumSessionId().toString()));
			LOGGER.info("Fetch pages for google accounts with buinessgetPagerequest id :{}",businessGetPageRequest.getId());
			//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageRequest.getRequestType(),Status.INITIAL.getName(), Long.parseLong(authRequest.getFreemiumSessionId().toString()));
			//todo add status for business get page request initialize-Completed
			//businessGetPageRequest.setStatus(Status.INITIAL.getName());
			Map<String, GoogleMyBusinessPagesDTO> pagesMap = new HashMap<>();
			//TODO to make call async request via nifi for parallel processing of accounts -- refer reconnect flow
			if(Objects.nonNull(gmbAccountDTOList)) {
				gmbAccountDTOList.forEach(gmbAcc -> {
					Map<String, GoogleMyBusinessPagesDTO> data = googleMyBusinessPageService.processAllGMBLocationsForAccount(gmbAcc, token);
					if (Objects.nonNull(data) && !data.isEmpty()){
						pagesMap.putAll(data);
					}
				});
			}
			LOGGER.info("pagesMap :{}",pagesMap);
			List<GoogleMyBusinessPagesDTO> gmbPages= new ArrayList<>(pagesMap.values());
			///myBusinessPages.parallelStream().forEach(page -> saveLocation(page, requestId, userId,gmbAccountId,email));
			final int pageRequestId=businessGetPageRequest.getId();
			final String email=businessGetPageRequest.getEmail();
			LOGGER.info("pageRequestId :{}",pageRequestId);
			gmbPages.parallelStream().forEach(page->prepareFreemiumPages(page,pageRequestId,email));
			LOGGER.info("No of gmbPages :{}",gmbPages.size());
			//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageRequest.getRequestType(),Status.FETCHED.getName(),Long.parseLong(authRequest.getFreemiumSessionId().toString()),false);
			businessGetPageRequest.setStatus(Status.FETCHED.getName());
			List<String> placeIds = gmbPages.stream().map(GoogleMyBusinessPagesDTO::getPlaceId).collect(Collectors.toList());
			// Google messaging is deprecated
			/*if(Objects.nonNull(businessGetPageRequest.getEnterpriseId()) && CollectionUtils.isNotEmpty(placeIds)) {
				producer.sendWithKey(Constants.UPDATE_GMSG_LOCATION_STATE, businessGetPageRequest.getEnterpriseId().toString(),
						new UpdateLocationStateRequest(businessGetPageRequest.getEnterpriseId(), placeIds,false));
			}
			 */
		} catch (GoogleJsonResponseException googleExp) {
			//TODO to mark status as failed instead of cancel in case of failure
			LOGGER.error("Error occured while fetching data for the freemium user:{}",googleExp.getMessage());
			//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageRequest.getRequestType(),Status.FAILED.getName(), Long.parseLong(authRequest.getFreemiumSessionId().toString()),true);
			businessGetPageRequest.setStatus(Status.FAILED.getName());
			throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_COMPLETE_REQUEST);
		}
		catch (Exception e) {
			// Todo mark cancel state in business get page request as well-completed
			LOGGER.error("error occurred while completing setup account request: ",e);
			//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), businessGetPageRequest.getRequestType(),Status.FAILED.getName(),Long.parseLong(authRequest.getFreemiumSessionId().toString()),true);
			businessGetPageRequest.setStatus(Status.FAILED.getName());
			if(e instanceof TooManyRequestException)
				//throw new BirdeyeSocialException(ErrorCodes.TOO_MANY_REQUESTS, e.getMessage());
				throw new TooManyRequestException(((TooManyRequestException) e).getCode(),e.getMessage(),((TooManyRequestException) e).getRetryAfterSeconds());

			throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED); // Todo work on exception handling-Review required
		}
		finally {
			if (Objects.nonNull(businessGetPageRequest.getId())) {
				businessGetPageService.saveAndFlush(businessGetPageRequest);
			}
		}
	}

	public  FreemiumFetchPageResponse fetchPages(Integer SessionId){
		//todo fetch on the basis of last business get page request-completed
		//todo add validation to only fetch in case of fetched status otherwise error will be thrown in case of failed status- request is invalid with status {status}-completed
		BusinessGetPageRequest lastRequest=businessGetPageService.findByFreemiumSessionId(SessionId);
		if(!lastRequest.getStatus().equalsIgnoreCase(Status.FETCHED.getName())){
			LOGGER.info("Request is not in fetched state but in {} state",lastRequest.getStatus());
			throw new BirdeyeSocialException(ErrorCodes.REQUEST_IS_INVALID_STATUS_NOT_IN_FETCHED_STATE);
		}
		List<SocialFreemiumLocations> pages=socialFreemiumRepository.findByRequestId(lastRequest.getId());
		List<String> locationIds=pages.stream().map(v->v.getLocationId()).collect(Collectors.toList());
		List<BusinessGoogleMyBusinessLocation> businessPages=socialGMBRepo.findByLocationIdIn(locationIds);
		Map<String,Long> businessPagesMap= businessPages.stream().filter(v->Objects.nonNull(v.getEnterpriseId())).
		collect(Collectors.toMap(v->v.getLocationId(),v->v.getEnterpriseId()));
		List<ChannelAccountInfo> accountInfo = new ArrayList<>();
		pages.stream().forEach(gmb -> {
			ChannelAccountInfo accInfo = new ChannelAccountInfo();
			accInfo.setAddress(gmb.getSingleLineAddress());
			accInfo.setId(gmb.getLocationId());
			accInfo.setImage(gmb.getPictureUrl() != null ? gmb.getPictureUrl() : gmb.getCoverImageUrl());
			accInfo.setLink(gmb.getLocationMapUrl());
			accInfo.setValidType(gmb.getIsValid() == 1 ? "Valid" : "Invalid");
			//accInfo.setDisabled((gmb.getIsSelected() != null && gmb.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
			if (businessPagesMap.containsKey(gmb.getLocationId())) {
				accInfo.setDisabled(Boolean.TRUE);
			} else {
				accInfo.setDisabled(Boolean.FALSE);
			}
			accInfo.setPageName(gmb.getLocationName());
			accInfo.setAccountName(gmb.getAccountName());
			accInfo.setPostalCode(gmb.getPostalCode());
			accInfo.setCity(gmb.getCity());
			accInfo.setState(gmb.getState());
			accInfo.setCountryCode(gmb.getCountry_code());
			accInfo.setPrimaryPhone(gmb.getPrimaryPhone());
			accInfo.setRegularHours(JSONUtils.fromJSON(gmb.getRegularHours(), GMBBusinessHour.class));
			accInfo.setDescription(gmb.getDescription());
			accInfo.setPlaceId(gmb.getPlaceId());
			accInfo.setWebsiteLink(gmb.getWebsiteUrl());
			accInfo.setCategories(JSONUtils.fromJSON(gmb.getCategories(), GMBPageCategories.class));
			accInfo.setBirdeyeConnected(businessPagesMap.containsKey(gmb.getLocationId()));
			accountInfo.add(accInfo);
		});
		//TODO to check the fetched pages must not be present in the google raw table, in case present mark the disabled as TRUE-completed
		FreemiumFetchPageResponse freemiumFetchPageResponse=new FreemiumFetchPageResponse();
		freemiumFetchPageResponse.setData(accountInfo);
		return freemiumFetchPageResponse;
	}

	@Transactional
	@Override
	public Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer pageNumber, Integer pageSize,String search) {
		Map<String, Object> data = new HashMap<>();
		Map<String ,Object> getResellerPages = new HashMap<>();
		List<ChannelAccountInfo> gmbAcctInfo;
		new PaginatedGMBResponse();
		PaginatedGMBResponse gmbPages;

		org.springframework.data.domain.PageRequest pageRequest = new org.springframework.data.domain.PageRequest(pageNumber, pageSize, Sort.Direction.ASC,Constants.IS_SELECTED);
		if(Objects.nonNull(search) && !search.isEmpty()) {
			gmbPages = googleMyBusinessPageService.findByRequestIdAndSearchStr(businessGetPageRequest.getId().toString(),pageRequest,search);
		}else{
			if (showAccount(businessGetPageRequest.getResellerId())) {
				if (businessGetPageRequest.getGmbAccount() != null) {
					GoogleAccount googleAccount = null;
					List<GoogleAccount> googleAccountList = googleAccountService.findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(businessGetPageRequest.getGmbAccount(),businessGetPageRequest.getId().toString());
					if(CollectionUtils.isNotEmpty(googleAccountList)) {
						googleAccount = googleAccountList.get(0);
					}
					if (Objects.isNull(googleAccount)) {
						return null;
					} else {
						gmbPages = googleMyBusinessPageService.getGMBResellerPagesByRequestIdAndGmbAccountId(businessGetPageRequest.getId().toString(), googleAccount.getId(), pageRequest);
						data.put("gmb-group", googleAccount.getAccountType().equalsIgnoreCase("PERSONAL") ? "Ungrouped" : googleAccount.getAccountName());
					}
				} else {
					gmbPages = googleMyBusinessPageService.findByRequestId(businessGetPageRequest.getId().toString(), pageRequest);
				}
			} else {
				gmbPages = googleMyBusinessPageService.findByRequestId(businessGetPageRequest.getId().toString(), pageRequest);
			}
		}

		if(CollectionUtils.isNotEmpty(gmbPages.getPages())){
			gmbAcctInfo = getAccountInfoForGmb(gmbPages.getPages(), businessGetPageRequest.getResellerId());
			if(CollectionUtils.isNotEmpty(gmbAcctInfo)) {
				getResellerPages.put(SocialChannel.GMB.getName(),  gmbAcctInfo);
			}
		}

		data.put("pageType", getResellerPages);
		data.put("totalCount", gmbPages.getTotalElements());
		data.put("pageCount", gmbPages.getTotalPages());
		data.put("gmbAccountSize",businessGetPageRequest.getTotalAccount());

		return data;
	}

	@Override
	public void refreshGmbUserAccount(String userEmail, Long parentId,String type){
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat("_group_").concat(String.valueOf(parentId));
		boolean lock = redisService.tryToAcquireLock(key);
		BusinessGetPageRequest request ;
		if(type.equalsIgnoreCase(Constants.RESELLER)){
			request = businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId,SocialChannel.GMB.getName(),CONNECT);
		}else{
			request =  businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId, SocialChannel.GMB.getName(),CONNECT);
		}
		LOGGER.info("[Redis Lock] (Google account refresh) Lock status, submitFetchAccountRequest : {} ",lock);
		if(lock) {
			try{
				if(request.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName())){
					request.setStatus(Status.CANCEL.getName());
					request.setErrorLog("User triggered refresh to fetch new accounts");
					businessGetPageService.saveAndFlush(request);
					List<GoogleAccount> googleAccounts = googleAccountService.findByUserAndActive(userEmail,1);
					GoogleAuthToken googleAuthToken = googleAuthenticationService.getGoogleAuthTokens(googleAccounts.get(0).getRefreshToken());
					googleAuthToken.setRefreshTokenId(googleAccounts.get(0).getRefreshToken());
					googleAuthToken.setEmail(userEmail);
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(),CONNECT,Status.ACCOUNT_REFRESH.getName(), parentId);
					BusinessGetPageRequest businessGetPageRequest = submitGmbRequest(parentId, request.getBirdeyeUserId(), googleAuthToken,Status.ACCOUNT_REFRESH.getName(),type);
					googleMyBusinessPageService.fetchGmbAccounts(businessGetPageRequest, googleAuthToken,"API",parentId);
				}else{
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(), request.getRequestType(),request.getStatus(),parentId);
					throw new BirdeyeSocialException(ErrorCodes.BUSINESS_GET_PAGE_REQUEST_STATUS_MOVED);
				}
			}catch (Exception ex){
				redisService.release(key);
				LOGGER.error("[Redis Lock] (Google account refresh) Lock released for business {}", parentId);
				BusinessGetPageRequest reqGMB = null;
				List<BusinessGetPageRequest> businessGetPageRequests =  type.equalsIgnoreCase(Constants.RESELLER)?
						getRequestForReseller(parentId,Status.ACCOUNT_REFRESH.getName(), SocialChannel.GMB.getName(), CONNECT):
						getRequestForBusiness(parentId,Status.ACCOUNT_REFRESH.getName(), SocialChannel.GMB.getName(), CONNECT);
				if(CollectionUtils.isNotEmpty(businessGetPageRequests)){
					reqGMB = businessGetPageRequests.get(0);
				}
				if (reqGMB != null) {
					reqGMB.setStatus(Status.CANCEL.getName());
					businessGetPageReqRepo.saveAndFlush(reqGMB);
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(),reqGMB.getRequestType(),Status.COMPLETE.getName(),parentId,true);
				}else{
					pushCheckStatusInFirebase(SocialChannel.GMB.getName(),CONNECT,Status.COMPLETE.getName(),parentId,true);
				}
			}
		}else{
			LOGGER.info("[Redis Lock] (Google account refresh) Lock is already acquired for business {}", parentId);
			handleFailureLock(request,SocialChannel.GMB.getName(),key,parentId,CONNECT);
		}
	}

	@Override
	public void refreshGmbUserAccountForOpenUrl(String userEmail, Long parentId, String firebaseKey) {
		String key = SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(firebaseKey));
		boolean lock = redisService.tryToAcquireLock(key);

		BusinessGetPageOpenUrlRequest request = businessGetPageOpenUrlReqRepo.findFirstByFirebaseKeyOrderByCreatedDescIdDesc(firebaseKey);
		LOGGER.info("[Redis Lock] Lock status, submitFetchAccountRequestOpenUrl : {} ",lock);

		if(lock) {
			try {
				if (Objects.nonNull(request) && request.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName())) {
					request.setStatus(Status.CANCEL.getName());
					request.setErrorLog("User triggered refresh to fetch new accounts");
					businessGetPageOpenUrlReqRepo.saveAndFlush(request);
					List<GoogleAccount> googleAccounts = googleAccountService.findByUserAndActive(userEmail,1);
					GoogleAuthToken googleAuthToken = googleAuthenticationService.getGoogleAuthTokens(googleAccounts.get(0).getRefreshToken());
					googleAuthToken.setRefreshTokenId(googleAccounts.get(0).getRefreshToken());
					googleAuthToken.setEmail(userEmail);
					nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,Status.ACCOUNT_REFRESH.getName());
					BusinessGetPageOpenUrlRequest businessGetPageOpenUrlRequest = submitGmbOpenUrlRequest(parentId, googleAuthToken,Status.ACCOUNT_REFRESH.getName(), firebaseKey);
					googleMyBusinessPageService.fetchGmbAccountsForOpenUrl(businessGetPageOpenUrlRequest, googleAuthToken,"API",parentId, firebaseKey);
				}else{
					LOGGER.info("[GMB] BusinessGetPageOpenUrlRequest found with status {} for enterprise id {}",request.getStatus(),parentId);
					//pushCheckStatusInFirebase(SocialChannel.GMB.getName(),request.getRequestType(),request.getStatus(),businessId);
					nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,request.getStatus());
					redisService.release(key);
					throw new BirdeyeSocialException(ErrorCodes.BUSINESS_GET_PAGE_REQUEST_STATUS_MOVED);
				}
			}catch(Exception e) {
				// Cleanup redis cache for error cases. e.g. Google Access Token
				redisService.release(key);
				LOGGER.error("[Redis Lock] (Google) Lock released for business {} {}",parentId,e);
				BusinessGetPageOpenUrlRequest reqGMB = null;
				List<BusinessGetPageOpenUrlRequest> businessGetPageOpenUrlRequests = getRequestForBusinessOpenUrl(parentId,Status.ACCOUNT_REFRESH.getName(), SocialChannel.GMB.getName(), CONNECT, firebaseKey);
				if(CollectionUtils.isNotEmpty(businessGetPageOpenUrlRequests)){
					reqGMB = businessGetPageOpenUrlRequests.get(0);
				}
				if (reqGMB != null) {
					reqGMB.setStatus(Status.CANCEL.getName());
					businessGetPageOpenUrlReqRepo.saveAndFlush(reqGMB);
					//pushCheckStatusInFirebase(SocialChannel.GMB.getName(),reqGMB.getRequestType(),Status.COMPLETE.getName(),businessId,true);
					nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,Status.CANCEL.getName());
				}else{
					//pushCheckStatusInFirebase(SocialChannel.GMB.getName(),CONNECT,Status.COMPLETE.getName(),businessId,true);
					nexusService.updateMapInFirebase("socialOpenUrl/"+ firebaseKey,firebaseKey,Status.CANCEL.getName());
				}
			}
		}else{
			LOGGER.info("[Redis Lock] (Google account) Lock is already acquired for business {}", parentId);
			handleFailureLockForOpenUrl(request,SocialChannel.GMB.getName(),key,parentId,CONNECT, firebaseKey);
		}
	}

	@Override
	public void gmbBackStatus(Long parentId,String type) {
		BusinessGetPageRequest request = type.equalsIgnoreCase(Constants.RESELLER)?
				businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId,SocialChannel.GMB.getName(),CONNECT):
				businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId, SocialChannel.GMB.getName(),CONNECT);
		if(Objects.isNull(request)){
			LOGGER.info("no business get pages request found for business :{}",parentId);
			throw new BirdeyeSocialException(ErrorCodes.NO_BUSINESS_GET_PAGE_REQUEST_FOUND);
		}else if(!(request.getStatus().equalsIgnoreCase(Status.FETCHED.getName()) || request.getStatus().equalsIgnoreCase(Status.NO_PAGES_FOUND.getName()))){
			LOGGER.info("business get page request can not be changed as its status moved to for business {} {} ",request.getStatus(),parentId);
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_GET_PAGE_REQUEST_STATUS_MOVED);
		}else{
			redisService.release(SocialChannel.GOOGLE_PLUS_GMB.getName().concat(String.valueOf(parentId)));
			request.setStatus(Status.ACCOUNT_FETCHED.getName());
			businessGetPageService.saveAndFlush(request);
			LOGGER.info("Status Changed to :{} for parentId :{}",Status.ACCOUNT_FETCHED.getName(),parentId);
			pushCheckStatusInFirebase(SocialChannel.GMB.getName(), request.getRequestType(),Status.ACCOUNT_FETCHED.getName(),parentId);
		}
	}

	@Override
	public void gmbBackStatusForOpenUrl(Long parentId) {
		BusinessGetPageOpenUrlRequest request = businessGetPageOpenUrlReqRepo.findFirstByEnterpriseIdAndChannelOrderByCreatedDescIdDesc(parentId, SocialChannel.GMB.getName());
		if(Objects.isNull(request)){
			LOGGER.info("no business get pages request found for business :{}",parentId);
			throw new BirdeyeSocialException(ErrorCodes.NO_BUSINESS_GET_PAGE_REQUEST_FOUND);
		}else if(!(request.getStatus().equalsIgnoreCase(Status.FETCHED.getName()) || request.getStatus().equalsIgnoreCase(Status.NO_PAGES_FOUND.getName()))){
			LOGGER.info("business get page request can not be changed as its status moved to for business {} {} ",request.getStatus(),parentId);
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_GET_PAGE_REQUEST_STATUS_MOVED);
		}else{
			redisService.release(request.getFirebaseKey());
			request.setStatus(Status.ACCOUNT_FETCHED.getName());
			businessGetPageOpenUrlReqRepo.saveAndFlush(request);
			LOGGER.info("Status Changed to :{} for parentId :{}",Status.ACCOUNT_FETCHED.getName(),parentId);
			//pushCheckStatusInFirebase(SocialChannel.GMB.getName(), request.getRequestType(),Status.ACCOUNT_FETCHED.getName(),parentId);
			nexusService.updateMapInFirebase("socialOpenUrl/"+ request.getFirebaseKey(),request.getFirebaseKey(),Status.ACCOUNT_FETCHED.getName());
		}
	}

	@Override
	public GmbAccountInfo getGmbAccountForOpenUrl(Long parentId, String firebaseKey) {
		GmbAccountInfo gmbAccountInfo = new GmbAccountInfo();
		BusinessGetPageOpenUrlRequest businessGetPageOpenUrlRequest = businessGetPageOpenUrlReqRepo.findFirstByFirebaseKeyOrderByCreatedDescIdDesc(firebaseKey);
		if(Objects.isNull(businessGetPageOpenUrlRequest)){
			throw new BirdeyeSocialException(ErrorCodes.NO_BUSINESS_GET_PAGE_REQUEST_FOUND);
		}else if(!businessGetPageOpenUrlRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName())){
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_GET_PAGE_REQUEST_STATUS_MOVED);
		} else {
			List<GoogleAccount> googleAccounts = googleAccountService.findByUserAndActive(businessGetPageOpenUrlRequest.getEmail(),1);
			gmbAccountInfo.setAccounts(GoogleAccountUtil.convertEntityToDto(googleAccounts));
			gmbAccountInfo.setUser(businessGetPageOpenUrlRequest.getEmail());
			gmbAccountInfo.setShowRefresh("db".equalsIgnoreCase(businessGetPageOpenUrlRequest.getType()));
		}
		LOGGER.info("Returning the gmb response for business id {}",parentId);
		return gmbAccountInfo;
	}

	@Override
	public GmbAccountInfo getGmbAccount(Long parentId,String type) {
		GmbAccountInfo gmbAccountInfo = new GmbAccountInfo();
		BusinessGetPageRequest businessGetPageRequest = type.equalsIgnoreCase(Constants.RESELLER) ?
				businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId,SocialChannel.GMB.getName(),CONNECT):
				businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId,SocialChannel.GMB.getName(),CONNECT);
		if(Objects.isNull(businessGetPageRequest)){
			throw new BirdeyeSocialException(ErrorCodes.NO_BUSINESS_GET_PAGE_REQUEST_FOUND);
		}else if(!businessGetPageRequest.getStatus().equalsIgnoreCase(Status.ACCOUNT_FETCHED.getName())){
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_GET_PAGE_REQUEST_STATUS_MOVED);
		}else{
			List<GoogleAccount> googleAccounts = googleAccountService.findByUserAndActive(businessGetPageRequest.getEmail(),1);
			gmbAccountInfo.setAccounts(GoogleAccountUtil.convertEntityToDto(googleAccounts));
			gmbAccountInfo.setUser(businessGetPageRequest.getEmail());
			gmbAccountInfo.setShowRefresh("db".equalsIgnoreCase(businessGetPageRequest.getType()));
		}
		LOGGER.info("Returning the gmb response for business id {}",parentId);
		return gmbAccountInfo;
	}

	@Override
	public void checkGmbTokenAndUpdate(List<Integer> rawIds,Boolean sync) {
		List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = socialGMBRepo.findByIdIn(rawIds);
		if(CollectionUtils.isNotEmpty(businessGoogleMyBusinessLocations)){
			LOGGER.info("Total raw pages fetched for gmb {}",businessGoogleMyBusinessLocations.size());
			Map<Integer, List<BusinessGoogleMyBusinessLocation>> map = this.fetchPagesByTokenValidity(businessGoogleMyBusinessLocations);
			List<BusinessGoogleMyBusinessLocation> inValidPages = map.get(0);

			List<BusinessGoogleMyBusinessLocation> validPages = map.get(1);

			if(CollectionUtils.isNotEmpty(validPages)){
				LOGGER.info("Total valid gmb pages found {}",validPages.size());
				if(!sync){
					return;
				}
				validPages.forEach(businessGoogleMyBusinessLocation -> {
					BusinessGMBLocation businessGMBLocation = businessGMBPageRepo.findFirstByLocationId(businessGoogleMyBusinessLocation.getLocationId());
					if(Objects.nonNull(businessGMBLocation)){
						LOGGER.info("found location for gmb location id marking valid in both mapping and raw table {}",businessGoogleMyBusinessLocation.getLocationId());
						businessGoogleMyBusinessLocation.setIsValid(1);
						socialGMBRepo.save(businessGoogleMyBusinessLocation);
						businessGMBLocation.setRefreshTokenId(businessGoogleMyBusinessLocation.getRefreshTokenId());
						businessGMBLocation.setLocationUrl(businessGoogleMyBusinessLocation.getLocationUrl());
						businessGMBLocation.setIsValid(1);
						businessGMBPageRepo.save(businessGMBLocation);
					}else{
						LOGGER.info("no mapping found for fb page {}",businessGoogleMyBusinessLocation.getLocationId());
					}
				});
			}

			if(CollectionUtils.isNotEmpty(inValidPages)){
				LOGGER.info("Total inValid gmb pages found {}",inValidPages.size());
				if(!sync){
					return;
				}
				inValidPages.forEach(businessGoogleMyBusinessLocation -> {
					BusinessGMBLocation businessGMBLocation = businessGMBPageRepo.findFirstByLocationId(businessGoogleMyBusinessLocation.getLocationId());
					if(Objects.nonNull(businessGMBLocation)){
						LOGGER.info("found location for gmb location id marking invalid in both mapping and raw table {}",businessGoogleMyBusinessLocation.getLocationId());
						businessGoogleMyBusinessLocation.setIsValid(0);
						socialGMBRepo.save(businessGoogleMyBusinessLocation);
						commonService.sendGmbSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(businessGoogleMyBusinessLocation),
								businessGoogleMyBusinessLocation.getUserId(), businessGoogleMyBusinessLocation.getBusinessId(), businessGoogleMyBusinessLocation.getEnterpriseId());

						businessGMBLocation.setIsValid(0);
						businessGMBLocation.setLocationUrl(businessGoogleMyBusinessLocation.getLocationUrl());
						businessGMBLocation.setRefreshTokenId(businessGoogleMyBusinessLocation.getRefreshTokenId());
						businessGMBPageRepo.save(businessGMBLocation);
					}else{
						LOGGER.info("no mapping found for fb page {}",businessGoogleMyBusinessLocation.getLocationId());
					}
				});

			}
		}
	}

	public Map<Integer, List<BusinessGoogleMyBusinessLocation>> fetchPagesByTokenValidity(List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations)
	{
		Map<Integer, List<BusinessGoogleMyBusinessLocation>> map = new HashMap<>();
		List<BusinessGoogleMyBusinessLocation> validPages = new ArrayList<>();
		List<BusinessGoogleMyBusinessLocation> inValidPages = new ArrayList<>();
		businessGoogleMyBusinessLocations.forEach(rawPage -> {
			try {
				LOGGER.info("fetching access token for gmb location id and rawPage : {} {}", rawPage.getId(), rawPage.getLocationId());
				String accessToken = googleAuthenticationService.getGoogleAccessToken(rawPage.getRefreshTokenId());
				if (StringUtils.isNotEmpty(accessToken)) {
					validPages.add(rawPage);
				} else {
					inValidPages.add(rawPage);
				}
			} catch (Exception ex) {
				LOGGER.error("Exception occurred while fetching access token for gmb location id, ex {} {}", rawPage.getLocationId(), ex.getMessage());
				inValidPages.add(rawPage);
			}
		});
		map.put(0, inValidPages);
		map.put(1, validPages);
		return map;
	}


	@Override
	@Deprecated
//	@Cacheable(value = CACHE_VALUE, key = "#enterpriseId == null ? #businessId.toString()+'_bus' : #enterpriseId.toString()+'_en' ", unless = "#result == null")
	public Boolean isGoogleMessageEnabled(Integer businessId,Long enterpriseId) {
		return false;
//		Boolean isMessengerEnabled = false;
//		if(businessId == null) {
//			businessId = businessCoreService.getBusinessId(enterpriseId);
//		}
//		BusinessOptionsDTO businessOptionsDTO = businessCoreService.getBusinessOptions(businessId);
//		//TODO: combine above calls (Dependency on core)
//		if (businessOptionsDTO != null &&
//				businessOptionsDTO.getIsGoogleMessageEnabled() != null && businessOptionsDTO.getIsGoogleMessageEnabled() == 1) {
//			isMessengerEnabled = true;
//		}
//		return isMessengerEnabled;
	}

	@Override
	public FetchPageResponse getIntegrationPage(BusinessGetPageRequest businessGetPageRequest, Long enterpriseId) {
		FetchPageResponse fetchPageResponse = new FetchPageResponse();
		Map<String, List<ChannelAccountInfo>> data = getPages(businessGetPageRequest, enterpriseId);
		if(showAccount(businessGetPageRequest.getEnterpriseId())){
			if(businessGetPageRequest.getGmbAccount()!=null){
				GoogleAccount googleAccount = null;
				List<GoogleAccount> googleAccountList = googleAccountService.findByAccountIdAndBusinessGetPageReqIdOrderByIdDesc(businessGetPageRequest.getGmbAccount(),businessGetPageRequest.getId().toString());
				if(CollectionUtils.isNotEmpty(googleAccountList)) {
					googleAccount = googleAccountList.get(0);
				}
				if(Objects.nonNull(googleAccount)){
					fetchPageResponse.setGmbGroup(googleAccount.getAccountType().equalsIgnoreCase("PERSONAL")?"Ungrouped":googleAccount.getAccountName());
				}
			}
		}
		if(Objects.nonNull(data) && data.containsKey(SocialChannel.GMB.getName())) {
			Boolean allPagesMapped = data.get(SocialChannel.GMB.getName()).stream()
					.allMatch(ChannelAccountInfo::getIsMapped);
			fetchPageResponse.setAllPagesMapped(allPagesMapped);
		}
		fetchPageResponse.setPageTypes(data);
		fetchPageResponse.setGmbAccountSize(businessGetPageRequest.getTotalAccount());
		return fetchPageResponse;
	}

	@Override
	@Deprecated
	public void updateLocationState(UpdateLocationStateRequest request) {
		LOGGER.info("updateLocationState request enterprise: {} & placeIds: {}", request.getEnterpriseId(), request.getPlaceIds());
		final List<GoogleMessagesAgent> gmbAgents = googleMsgAgentService.findByEnterpriseIdIn(request.getEnterpriseId()) ;
		if(CollectionUtils.isEmpty(gmbAgents)){
			return;
		}
		gmbAgents.forEach(gmbAgent -> {
			String locationName , locationStatus;
			List<BusinessGoogleMyBusinessLocation> updateRawPages = new ArrayList<>();
			if (Objects.isNull(gmbAgent) || StringUtils.isEmpty(gmbAgent.getStatus())
					|| GoogleAgentStatus.valueOf(gmbAgent.getStatus()).compareTo(GoogleAgentStatus.AGENT_CREATED) <= 0) {
				LOGGER.info("Agent is not launched for agent id : {}",gmbAgent.getId());
				agentRepo.updateGMBSyncStatus(gmbAgent.getId(), GMBLocationJobStatus.COMPLETE, Timestamp.valueOf(LocalDateTime.now().plusHours(24)));
				return;
			}
			try {
				LOGGER.info("Agent Launched for enterprise: {}, and agent id :{} starting fetch location state",request.getEnterpriseId(),gmbAgent.getId());
				List<Location> locations = googleLocationService.getAllLocations(gmbAgent.getBrandName());
				if(CollectionUtils.isEmpty(locations)){
					LOGGER.info("Unable to get location for agent id : {}",gmbAgent.getId());
					agentRepo.updateGMBSyncStatus(gmbAgent.getId(), GMBLocationJobStatus.COMPLETE, Timestamp.valueOf(LocalDateTime.now().plusHours(24)));
					return;
				}
				List<String> placeIds = locations.stream().filter(v -> StringUtils.isNotEmpty(v.getPlaceId()))
						.map(Location::getPlaceId).collect(Collectors.toList());

				List<BusinessGoogleMyBusinessLocation> gmbAllPages = socialGMBRepo.findByPlaceIdInAndIsValid(placeIds, 1);

				if(CollectionUtils.isEmpty(gmbAllPages)) {
					LOGGER.info("No valid pages found for location, exiting the process");
					return;
				}

				Map<String, BusinessGoogleMyBusinessLocation> rawPages = gmbAllPages.stream().
						collect(Collectors.toMap(BusinessGoogleMyBusinessLocation::getPlaceId, Function.identity()));

				for (Location location : locations) {
					if (!request.getPlaceIds().contains(location.getPlaceId())) {
						continue;
					}
					LocationLaunch launch = googleLocationService.getLocationLaunchState(location.getName());
					if (AgentLaunchState.LAUNCH_STATE_LAUNCHED.name().equalsIgnoreCase(launch.getLaunchState())) {
						LOGGER.info("Location launch state: {}, for enterprise : {} and place ID: {}",
								launch.getLaunchState(),request.getEnterpriseId(), location.getPlaceId());
						locationName = location.getName();
						locationStatus = GoogleLocationStatus.LAUNCHED.name();
					} else {
						LocationVerification verification = googleLocationService.getLocationVerificationState(location.getName());
						LOGGER.info("Location verification state: {}, for agent id  : {} and place ID: {}", verification.getVerificationState(),
								gmbAgent.getId(), location.getPlaceId());
						locationName = location.getName();
						if (AgentVerificationState.VERIFICATION_STATE_VERIFIED.name().equalsIgnoreCase(verification.getVerificationState())) {
							locationStatus = GoogleLocationStatus.VERIFIED.name();
						} else if (AgentVerificationState.VERIFICATION_STATE_UNVERIFIED.name().equalsIgnoreCase(verification.getVerificationState())
						|| AgentVerificationState.VERIFICATION_STATE_SUSPENDED_IN_GMB.name().equalsIgnoreCase(verification.getVerificationState())) {
							locationStatus = GoogleLocationStatus.UN_VERIFIED.name();
						} else {
							locationStatus = GoogleLocationStatus.CREATED.name();
						}
					}
					BusinessGoogleMyBusinessLocation rawPage = rawPages.get(location.getPlaceId());
					if(!GoogleLocationStatus.UN_VERIFIED.name().equalsIgnoreCase(locationStatus) &&
							(Objects.isNull(rawPage.getAgentId()) || Objects.equals(rawPage.getAgentId(), gmbAgent.getId()))) {
						if (!StringUtils.equals(locationName, rawPage.getgMsgLocationStatus())) {
							LOGGER.info("Mapping updated for location {}, from {} to {} ", locationName, rawPage.getgMsgLocationStatus(), locationStatus);
						}
						rawPage.setgMsgLocationName(locationName);
						rawPage.setgMsgLocationStatus(locationStatus);
						rawPage.setAgentId(gmbAgent.getId());
						updateRawPages.add(rawPage);
						LOGGER.info(String.format("Sending validity check for location {} from %s", "updateLocationState"), rawPage.getLocationId());
					}else if(request.isUnverifiedFromGoogle() && GoogleLocationStatus.UN_VERIFIED.name().equalsIgnoreCase(locationStatus)
							&& (Objects.isNull(rawPage.getAgentId()) || Objects.equals(rawPage.getAgentId(), gmbAgent.getId()))){
						if (!StringUtils.equals(locationName, rawPage.getgMsgLocationName())) {
							LOGGER.info("Mapping updated for location {}, from {} to {} ", locationName, rawPage.getgMsgLocationStatus(), locationStatus);
						}
						rawPage.setgMsgLocationName(locationName);
						rawPage.setgMsgLocationStatus(locationStatus);
						rawPage.setAgentId(null);
						updateRawPages.add(rawPage);
					}
					googleMyBusinessPageService.pushToKafkaForValidity(Constants.GMB, rawPage.getLocationId());
				}
				socialGMBRepo.save(updateRawPages);
				socialGMBRepo.flush();
				// update location sync status for agent id
				agentRepo.updateGMBSyncStatus(gmbAgent.getId(), GMBLocationJobStatus.COMPLETE, Timestamp.valueOf(LocalDateTime.now().plusHours(24)));
				pushToFirebaseLocationStatus(gmbAgent.getId(),gmbAgent.getEnterpriseId());
			} catch (Exception ex) {
				// update location sync status for enterprise id
				agentRepo.updateGMBSyncStatus(gmbAgent.getId(), GMBLocationJobStatus.FAILED, Timestamp.valueOf(LocalDateTime.now().plusHours(24)));
				LOGGER.warn("Error while updating location state for enterprise: {}, error: {}", request.getEnterpriseId(), ex.getMessage());
				pushgMsgLocationLaunchInFirebase(gmbAgent.getEnterpriseId(), gmbAgent.getId(),GoogleLocationResponseStatus.ERROR.name());
			}
		});
	}

	public void pushToFirebaseLocationStatus (Integer agentId, Long enterpriseId){
		Set<BusinessGoogleMyBusinessLocation> erroneousMappedPages = socialGMBRepo.findAllNotLaunchedPageIdsByAgentId(agentId);
		if (CollectionUtils.isNotEmpty(erroneousMappedPages)) {
			LOGGER.warn("updateRedisData: Invalid mappings found for agent id : {}",agentId);
			pushgMsgLocationLaunchInFirebase(enterpriseId,agentId,GoogleLocationResponseStatus.ERROR.name());
		} else {
			LOGGER.warn("updateRedisData: No invalid mappings found for agent id : {}",agentId);
			pushgMsgLocationLaunchInFirebase(enterpriseId,agentId,GoogleLocationResponseStatus.COMPLETE.name());
		}
	}
	@Override
	public void updateLocationStateByAgentId(UpdateLocationStateRequest request, Integer agentId) {
		LOGGER.info("updateLocationState request enterprise: {} & placeId: {}", request.getEnterpriseId(), request.getPlaceIds());
		final GoogleMessagesAgent gmbAgent = googleMsgAgentService.getAgentById(agentId);

		String locationName;
		String locationStatus;
		List<BusinessGoogleMyBusinessLocation> updateRawPages = new ArrayList<>();
		if (gmbAgent != null && gmbAgent.getStatus() != null && gmbAgent.getStatus().equalsIgnoreCase(GoogleLocationStatus.LAUNCHED.toString())) {
			try {
				LOGGER.info("Agent Launched for enterprise: {}, starting fetch location state", request.getEnterpriseId());
				List<Location> locations = googleLocationService.getAllLocations(gmbAgent.getBrandName());
				for (Location location : locations) {
					if (request.getPlaceIds().contains(location.getPlaceId())) {
						LocationLaunch launch = googleLocationService.getLocationLaunchState(location.getName());
						if (AgentLaunchState.LAUNCH_STATE_LAUNCHED.name().equalsIgnoreCase(launch.getLaunchState())) {
							LOGGER.info("Location launch state: {}, for enterprise : {} and place ID: {}", launch.getLaunchState(),
									request.getEnterpriseId(), location.getPlaceId());
							locationName = location.getName();
							locationStatus = GoogleLocationStatus.LAUNCHED.name();
						} else {
							LocationVerification verification = googleLocationService.getLocationVerificationState(location.getName());
							LOGGER.info("Location verification state: {}, for enterprise : {} and place ID: {}", verification.getVerificationState(),
									request.getEnterpriseId(), location.getPlaceId());
							if (AgentVerificationState.VERIFICATION_STATE_VERIFIED.name().equalsIgnoreCase(verification.getVerificationState())) {
								locationName = location.getName();
								locationStatus = GoogleLocationStatus.VERIFIED.name();
							} else {
								locationName = location.getName();
								locationStatus = GoogleLocationStatus.CREATED.name();
							}
						}
						// check isValid check is required here.
						List<BusinessGoogleMyBusinessLocation> rawPages = socialGMBRepo.findByPlaceIdAndIsValid(location.getPlaceId(), 1);
						if (CollectionUtils.isNotEmpty(rawPages)) {
							BusinessGoogleMyBusinessLocation rawPage = rawPages.get(0);
							if (!StringUtils.equals(locationName, rawPage.getgMsgLocationStatus())) {
								LOGGER.info("Mapping updated for location {}, from {} to {} ", locationName, rawPage.getgMsgLocationStatus(), locationStatus);
							}
							rawPage.setgMsgLocationName(locationName);
							rawPage.setgMsgLocationStatus(locationStatus);
							updateRawPages.add(rawPage);
						}
					}
				}
				socialGMBRepo.save(updateRawPages);
				LOGGER.info(String.format("Sending validity check for location {} from %s","updateLocationState"),updateRawPages);
				updateRawPages.forEach(page -> {
					googleMyBusinessPageService.pushToKafkaForValidity(Constants.GMB, page.getLocationId());
					// update location sync status for enterprise id
					agentRepo.updateGMBSyncStatusById(page.getId(), GMBLocationJobStatus.COMPLETE, Timestamp.valueOf(LocalDateTime.now().plusHours(24)));
				});
				socialGMBRepo.flush();
			} catch (Exception ex) {
				// update location sync status for enterprise id
				updateRawPages.forEach(page -> agentRepo.updateGMBSyncStatusById(page.getId(), GMBLocationJobStatus.FAILED, Timestamp.valueOf(LocalDateTime.now().plusHours(24))));
				LOGGER.warn("Error while updating location state for enterprise: {}, error: {}", request.getEnterpriseId(), ex.getMessage());
				return;
			}
		} else {
			LOGGER.info("Agent is not in launched state, location status is not updated for enterprise id {}, place ids {}", request.getEnterpriseId(), request.getPlaceIds());
			updateRawPages.forEach(page -> agentRepo.updateGMBSyncStatusById(page.getId(), GMBLocationJobStatus.COMPLETE, Timestamp.valueOf(LocalDateTime.now().plusHours(24))));
		}
	}

	@Override
	@Async
	public void migrateGmbPermission(List<Integer> rawIds, Boolean sync) {
		List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = socialGMBRepo.findByIdIn(rawIds);
		if(CollectionUtils.isNotEmpty(businessGoogleMyBusinessLocations)){
			LOGGER.info("Total raw pages fetched for gmb permission update {}",businessGoogleMyBusinessLocations.size());
			businessGoogleMyBusinessLocations.parallelStream().filter(page -> Objects.nonNull(page.getRefreshTokenId())).
			forEach(page->{
				try{
					GoogleAuthToken googleAuthToken = googleAuthenticationService.getGoogleAuthTokens(page.getRefreshTokenId());
					LOGGER.info("Token details fetched for page {} {}",page.getId(),googleAuthToken);
					if(Objects.nonNull(googleAuthToken) && StringUtils.isNotBlank(googleAuthToken.getScope()))
						page.setPermissions(commonService.extractGMBPermission(googleAuthToken.getScope()));
				}catch (Exception ex){
					LOGGER.info("Exception occurred while updating permission for gmb page {}",page.getId(),ex);
				}
			});
			socialGMBRepo.save(businessGoogleMyBusinessLocations);
			LOGGER.info(String.format("Sending validity check for location {} from %s","migrateGmbPermission"),businessGoogleMyBusinessLocations);
			businessGoogleMyBusinessLocations.forEach(page -> googleMyBusinessPageService.pushToKafkaForValidity(Constants.GMB,page.getLocationId()));
		}
	}


	@Override
	public Integer getIntegrationStatus(Long accountId, List<Integer> businessIds) {
		// Fetch relevent data from google table
		List<BusinessGMBLocationRawRepository.BGL> response = socialGMBRepo.getValidIntegrations(accountId,
				businessIds);
		List<Integer> socialBusinessIds = response.stream().map(BusinessGMBLocationRawRepository.BGL::getBusinessId)
				.collect(Collectors.toList());
		Long unMapped = businessIds.stream().filter(l -> !socialBusinessIds.contains(l)).count();
		return unMapped.intValue();
	}

	public void savePlatformMapping(LocationPageMappingRequest locationPageMappingRequest) {
		List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = socialGMBRepo.findByLocationId(locationPageMappingRequest.getPageId());
		List<BusinessGMBLocation> businessGMBLocations = businessGMBPageRepo.findByLocationId(locationPageMappingRequest.getPageId());
		if(CollectionUtils.isNotEmpty(businessGoogleMyBusinessLocations)){
			if(CollectionUtils.isEmpty(businessGMBLocations)){
				this.savePlatformMapping(businessGoogleMyBusinessLocations.get(0),locationPageMappingRequest.getLocationId(),null);
			}else{
				LOGGER.info("[GMB] Mapping already found in platform table , hence updating data for {}",locationPageMappingRequest);
				BusinessGoogleMyBusinessLocation rawPage = businessGoogleMyBusinessLocations.get(0);
				businessGMBLocations.stream().forEach(businessGMBLocation -> reconnectGMBLocationMapping(rawPage,businessGMBLocation));
			}
		}
	}

	@Override
	public void removePlatformMapping(List<LocationPageMappingRequest> locationPageMappings) {
		LOGGER.info("remove Platform GMB page mappings with locationPageMappings {}", locationPageMappings);
		Set<String> pageIds = locationPageMappings.stream().map(LocationPageMappingRequest::getPageId).collect(Collectors.toSet());
		removeEntry(pageIds);
	}

	@Override
	public void removePlatformEntry(List<ChannelPageRemoved> input) {
		LOGGER.info("remove Platform GMB page for {}", input);
		Set<String> pageIds = input.stream().map(ChannelPageRemoved::getPageId).collect(Collectors.toSet());
		removeEntry(pageIds);
	}

	@Override
	public void updateGmbBusinessIsValid(SocialEsValidRequest socialEsValidRequest) {
		LOGGER.info("Request received to update is valid in platform for gmb {}",socialEsValidRequest);
		List<BusinessGMBLocation> businessGMBLocations = businessGMBPageRepo.findByLocationId(socialEsValidRequest.getIntegrationId());
		if(CollectionUtils.isNotEmpty(businessGMBLocations)){
			businessGMBLocations.forEach(businessGMBLocation -> businessGMBLocation.setIsValid(socialEsValidRequest.getIsValid()));
			businessGMBPageRepo.save(businessGMBLocations);
		}else{
			LOGGER.info("No pages found to update is valid for paltform db for {}",socialEsValidRequest);
		}
	}

	private void removeEntry(Set<String> pageIds) {
		List<BusinessGMBLocation> existingPages = businessGMBPageRepo.findByLocationIdIn(pageIds);
		if(CollectionUtils.isNotEmpty(existingPages)){
			businessGMBPageRepo.delete(existingPages);
			businessGMBPageRepo.flush();
		}else{
			LOGGER.info("No pages found to remove from paltform db for pages {}",pageIds);
		}
	}

	@Override
	public void updatePlatformMapping(SocialChannelSyncRequest socialChannelSyncRequest) {
		LOGGER.info("[GMB] Request received to update Platform mapping socialChannelSyncRequest {}", socialChannelSyncRequest);
		List<BusinessGoogleMyBusinessLocation> businessGoogleMyBusinessLocations = socialGMBRepo.findByLocationId(socialChannelSyncRequest.getIntegrationIdentifier());
		List<BusinessGMBLocation> existingPages = businessGMBPageRepo.findByLocationId(socialChannelSyncRequest.getIntegrationIdentifier());
		LOGGER.info("[GMB] businessGoogleMyBusinessLocations: {} \nexistingPages: {}",businessGoogleMyBusinessLocations,existingPages);
		if(CollectionUtils.isNotEmpty(businessGoogleMyBusinessLocations) && CollectionUtils.isNotEmpty(existingPages)) {
			if (existingPages.size() > 1) {
				LOGGER.info("[GMB] multiple mapping exists for page {}", socialChannelSyncRequest.getIntegrationIdentifier());
			}
			Optional<BusinessGoogleMyBusinessLocation> bGMBAccount = businessGoogleMyBusinessLocations.stream().filter(e->Objects.nonNull(e.getBusinessId())).findFirst();
			if(bGMBAccount.isPresent()){
				BusinessGoogleMyBusinessLocation rawPage = bGMBAccount.get();
				existingPages.forEach(businessGMBLocation -> reconnectGMBLocationMapping(rawPage,businessGMBLocation));
			} else  {
				LOGGER.error("No business found with GMB account {}",socialChannelSyncRequest.getIntegrationIdentifier());
			}
		}
	}

	private void reconnectGMBLocationMapping(BusinessGoogleMyBusinessLocation rawPage,BusinessGMBLocation mappedPage) {
		LOGGER.info("[GMB] updating platform mapping for page {}",rawPage.getLocationId());
		mappedPage.setIsValid(rawPage.getIsValid());
		mappedPage.setRefreshTokenId(rawPage.getRefreshTokenId());
		mappedPage.setLocationMapUrl(rawPage.getLocationMapUrl());
		mappedPage.setLocationId(rawPage.getLocationId());
		mappedPage.setLocationUrl(rawPage.getLocationUrl());
		mappedPage.setLocationName(rawPage.getLocationName());
		mappedPage.setAddress(rawPage.getSingleLineAddress());
		if (rawPage.getIsValid() == 1) {
			mappedPage.setErrorLog("");
		}
		mappedPage.setUpdatedBy(rawPage.getUpdatedBy());
		mappedPage.setUpdatedAt(new Date());
		mappedPage.setErrorLog(rawPage.getErrorLog());

		mappedPage.setBusinessId(rawPage.getBusinessId());

		businessGMBPageRepo.saveAndFlush(mappedPage);
	}
	
	// Listing support.
	@Override
	public ConnectedPages getConnectedPages(Long enterpriseId, Map<Integer, BusinessEntity> idToBusinessMap) {
		ConnectedPages connectedPage = new ConnectedPages();
		List<BusinessGoogleMyBusinessLocation> connectedGmbPages = null;
			connectedGmbPages = googleMyBusinessPageService.connectedGmbPagesForAnEnterprise(enterpriseId);
		List<String> gmbPageIds = new ArrayList<>();
		Map<String, BusinessEntity> gmbToBusinessMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(connectedGmbPages)) {
			connectedGmbPages.stream().forEach(page -> gmbPageIds.add(page.getLocationId()));
			List<BusinessGMBLocation> existingGMBPages = businessGMBPageRepo.findByLocationIdIn(gmbPageIds);
			existingGMBPages.stream().forEach(page -> gmbToBusinessMap.put(page.getLocationId(), idToBusinessMap.get(page.getBusinessId())));
		}
		ChannelPageDetails gmbAccountInfo = getGMBPageInfo(connectedGmbPages, gmbToBusinessMap);
		Map<String, ChannelPageDetails> pageTypes = new TreeMap<>(Collections.reverseOrder());
		pageTypes.put(SocialChannel.GOOGLEPLUS.getName(), new ChannelPageDetails());
		pageTypes.put(SocialChannel.GMB.getName(), gmbAccountInfo);
		connectedPage.setPageTypes(pageTypes);
		return connectedPage;
	}

	@Override
	public void updateLocationStatusAndName(UpdateGMBLocationAndNameRequest req) {
		LOGGER.info("Request received to update status or name for placeId {}", req.getPlaceId());
		if (StringUtils.isNotEmpty(req.getPlaceId())) {
			BusinessGoogleMyBusinessLocation bgml = socialGMBRepo.findGMBByPlaceId(req.getPlaceId());
			if (bgml == null) {
				throw new BirdeyeSocialException(ErrorCodes.GMB_PLACE_ID_NOT_FOUND,"Place id not found for " + req.getPlaceId());
			}else{
				try{
					if(req.getgMsgLocationName() != null){
						bgml.setgMsgLocationName(req.getgMsgLocationName());
					}
					if(req.getgMsgLocationStatus().isEmpty()){
						bgml.setgMsgLocationStatus(null);
					}else{
						bgml.setgMsgLocationStatus(req.getgMsgLocationStatus());
					}
					socialGMBRepo.save(bgml);
					LOGGER.info(String.format("Sending validity check for location {} from %s","updateLocationStatusAndName"),bgml.getLocationId());
					googleMyBusinessPageService.pushToKafkaForValidity(Constants.GMB,bgml.getLocationId());
				}catch(Exception e){
					LOGGER.info("Exception in updating location status "+e);
				}
			}
		}else{
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT,"Place id cannot not be null");
		}
	}


	@Override
	public void launchLocations(LocationsLaunchAndUnLaunchRequest req) {
		LOGGER.info("Request received to launch locations for business_ids {}", req);
		List<Integer> businessIds = null;
		if (req.getEnterpriseId() == null) {
			throw new BirdeyeSocialException("Enterprise Id can not be null");
		}
		if (req.getType().equalsIgnoreCase("SMB")) {
			businessIds = Collections.singletonList(businessCoreService.getBusinessId(req.getEnterpriseId()));
		} else {
			if (CollectionUtils.isNotEmpty(req.getBusinessNumbers())) {
				businessIds = req.getBusinessNumbers().stream().map(i -> businessCoreService.getBusinessId(i)).collect(Collectors.toList());
			} else {
				LOGGER.error("Business ids can not be null");
			}
		}
		if (CollectionUtils.isEmpty(businessIds)) {
			return;
		}
		List<String> placeIdList = socialGMBRepo.getPlaceIdsForBusinesses(businessIds);
		GoogleMessagesAgent agent = googleMsgAgentService.findByAgentId(req.getAgentId());
		if(Objects.isNull(agent)){
			return;
		}
		List<String> placeIds = socialGMBRepo.findPlaceIdByAgentIdAndIsSelected(agent.getId());
		if(CollectionUtils.isEmpty(placeIds)){
			return;
		}
		CreateLocationRequest clr = new CreateLocationRequest();
		clr.setEnterpriseId(req.getEnterpriseId());
		clr.setAgentName(agent.getAgentName());
		clr.setBrandName(agent.getBrandName());
		clr.setAgentId(agent.getId());
		clr.setPostAutoMapping(false);
		for (String placeId : placeIds) {
			if(placeIdList.contains(placeId)){
				clr.setPlaceId(placeId);
				try {
					setupLocation(clr);
				} catch (Exception e) {
					LOGGER.error("Unexpected action occurred while launching locations {}", e.getMessage());
				}
			}
		}
	}

	@Override
	@Deprecated
	public void unLaunchLocations(LocationsLaunchAndUnLaunchRequest req){
		LOGGER.info("Request received to unlaunch locations for business_ids {}", req);
		List<Integer> businessIds = null;
		if (req.getEnterpriseId() == null) {
			throw new BirdeyeSocialException("Enterprise Id can not be null");
		}
		if (req.getType().equalsIgnoreCase("SMB")) {
			businessIds = Collections.singletonList(businessCoreService.getBusinessId(req.getEnterpriseId()));
		} else {
			if (CollectionUtils.isNotEmpty(req.getBusinessNumbers())) {
				businessIds = req.getBusinessNumbers().stream().map(i -> businessCoreService.getBusinessId(i)
				).collect(Collectors.toList());
			} else {
				LOGGER.error("Business ids can not be null");
			}
		}
		if (CollectionUtils.isEmpty(businessIds)) {
			LOGGER.info("Business ids can not be null");
			return;
		}
		List<BusinessGoogleMyBusinessLocation> pages = socialGMBRepo.findByBusinessIdIn(businessIds);
		Map<Integer,List<BusinessGoogleMyBusinessLocation>> agentPageMap = prepareAgentAndGmbPageMap(pages);
		if (CollectionUtils.isEmpty(agentPageMap.values())) {
			return;
		}
		for(Map.Entry<Integer,List<BusinessGoogleMyBusinessLocation>> map : agentPageMap.entrySet()) {
			try {
				LocationBulkUnlaunchRequest request = new LocationBulkUnlaunchRequest(map.getKey(),true);
				unLaunchLocationByAgentId(request,map.getValue());
			} catch (Exception e) {
				LOGGER.error("Unexpected action occurred while un-launching locations {}", e.getMessage());
			}
		}
	}

	public List<String> getPlaceIds(Long enterpriseId) {
		return socialGMBRepo.findPlaceIdsByEnterpriseId(enterpriseId);
	}


	@Override
	public void updateAgentState(Long enterpriseId) {
		LOGGER.info("Working on request to update the agent state for enterprise id {}", enterpriseId);
		List<GoogleMessagesAgent> googleMessagesAgents = agentRepo.findByEnterpriseIdIn(enterpriseId);
		if(CollectionUtils.isEmpty(googleMessagesAgents)){
			LOGGER.info("No record found for enterprise id : {}",enterpriseId);
			return;
		}
		googleMessagesAgents.forEach(googleMessagesAgent -> {
			if(StringUtils.isEmpty(googleMessagesAgent.getStatus())
					|| GoogleAgentStatus.valueOf(googleMessagesAgent.getStatus()).compareTo(GoogleAgentStatus.AGENT_CREATED)<0
			|| GoogleAgentStatus.valueOf(googleMessagesAgent.getStatus()).equals(GoogleAgentStatus.UN_LAUNCHED)){
				agentRepo.updateGMBSyncStatus(googleMessagesAgent.getId(), GMBLocationJobStatus.COMPLETE, Timestamp.valueOf(LocalDateTime.now().plusHours(24)));
				return;
			}
			Integer agentId = googleMessagesAgent.getId();
			String agentStatus;
			try{
				AgentLaunch launch = googleBizCommService.getAgentLaunchState(googleMessagesAgent.getAgentName().concat("/launch"));
				if ((!Objects.isNull(launch.getBusinessMessages())
						&& Objects.nonNull(launch.getBusinessMessages().getLaunchDetails())
						&& AgentLaunchState.LAUNCH_STATE_LAUNCHED.name().equalsIgnoreCase(launch.getBusinessMessages().getLaunchDetails().get("LOCATION").getLaunchState()))
						|| (!Objects.isNull(launch.getVerifiedCalls())
						&& AgentLaunchState.LAUNCH_STATE_LAUNCHED.name().equalsIgnoreCase(launch.getVerifiedCalls().getLaunchState()))) {
					LOGGER.info("Agent launch state: {}, for enterprise : {}", AgentLaunchState.LAUNCH_STATE_LAUNCHED.name(), enterpriseId);
					agentStatus = GoogleLocationStatus.LAUNCHED.name();
				} else {
					AgentVerification verification = googleBizCommService.getAgentVerification(googleMessagesAgent.getAgentName().concat("/verification"));
					LOGGER.info("Agent verification state: {}, for enterprise : {}", verification.getVerificationState(), enterpriseId);
					agentStatus = AgentVerificationState.VERIFICATION_STATE_VERIFIED.name().equalsIgnoreCase(verification.getVerificationState()) ?
							GoogleAgentStatus.VERIFIED.name() : GoogleAgentStatus.AGENT_CREATED.name();
				}

				if (StringUtils.isEmpty(googleMessagesAgent.getStatus()) || !StringUtils.equals(googleMessagesAgent.getStatus(), agentStatus)) {
					LOGGER.info("Updating agent status for agent id {} to {}", agentId, agentStatus);
					googleMessagesAgent.setStatus(agentStatus);
					agentRepo.saveAndFlush(googleMessagesAgent);
				} else {
					LOGGER.info("Agent status is up to date for agent id {}, {}", agentId, agentStatus);
				}
			} catch(Exception e) {
				LOGGER.error("Error while updating agent state for agentId id {}, {}", agentId, e.getMessage());
				agentRepo.updateGMBSyncStatus(googleMessagesAgent.getId(), GMBLocationJobStatus.FAILED, Timestamp.valueOf(LocalDateTime.now().plusHours(24)));
			}
		});

	}

	@Override
	public boolean getGMBPermission(Integer accountId, List<String> modules) {
		LOGGER.info("Get gmb post permission for account id : {}",accountId);
		List<BusinessGoogleMyBusinessLocation> businessLocations = socialGMBRepo.findByShortAccountIdAndBusinessIdNotNull(accountId);
		return checkPermission(businessLocations,modules);
	}

	@Override
	public boolean getGMBPostPermission(List<BusinessGoogleMyBusinessLocation> gmbPages, List<String> modules) {
		return checkPermission(gmbPages,modules);
	}

	private boolean checkPermission(List<BusinessGoogleMyBusinessLocation> businessLocations, List<String> modules) {
		if(CollectionUtils.isEmpty(businessLocations)){
			LOGGER.info("BusinessGoogleMyBusinessLocation list is null");
			return true;
		}
		List<String> gmbPermissions = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(modules)) {
			for (String module : modules) {

				SocialModulePermission socialModulePermission = socialModulePermissionService
						.getPermissionsForChannelAndModule(SocialChannel.GOOGLE.getId(), module);
				if(Objects.nonNull(socialModulePermission) && Objects.nonNull(socialModulePermission.getPermissionsNeeded())) {
					List<String> modulePermissions = Arrays
							.asList(socialModulePermission.getPermissionsNeeded().split(","));
					gmbPermissions.addAll(modulePermissions);
				}
			}
			LOGGER.debug("Permissions needed for modules: {}: {}",modules,gmbPermissions);
			for(BusinessGoogleMyBusinessLocation businessLocation : businessLocations){
				if(businessLocation.getIsValid() == 0 || Objects.isNull(businessLocation.getPermissions())){
					return false;
				}
				List<String> permissions = Arrays.stream(businessLocation.getPermissions().replaceAll("\\s","").split(",")).collect(Collectors.toList());
				LOGGER.debug("Permissions available for GMB account: {}: {}",businessLocation.getAccountId(),permissions);
				if(!new HashSet<>(permissions).containsAll(gmbPermissions)){
					return false;
				}
			}
		}
		return true;
	}

	private void prepareFreemiumPages(GoogleMyBusinessPagesDTO page,Integer requestId,String email) {
		//Todo add request Id-(Completed) and fetch pages on the basis of request Id-(completed)
		if (page != null) {
			LOGGER.info("GoogleMyBusinessPagesDTO: {}",page);
			SocialFreemiumLocations businessPage = new SocialFreemiumLocations();
			businessPage.setAccountId(page.getAccountId());
			businessPage.setAccountName(page.getAccountName());
			businessPage.setAccountStatus(page.getAccountStatus());
			businessPage.setAccountType(page.getAccountType());
			businessPage.setCoverImageUrl(page.getCoverImageUrl());
			businessPage.setGooglePlusId(page.getGooglePlusId());
			businessPage.setIsSelected(0);
			businessPage.setIsVerified(page.getIsVerified());
			businessPage.setLocationId(page.getLocationId());
			businessPage.setLocationName(page.getLocationName());
			businessPage.setLocationState(page.getLocationState());
			businessPage.setLocationUrl(page.getLocationUrl());
			businessPage.setLocationMapUrl(page.getLocationMapUrl());
			businessPage.setPictureUrl(page.getPictureUrl());
			businessPage.setPlaceId(page.getPlaceId());
			businessPage.setPrimaryPhone(page.getPrimaryPhone());
			businessPage.setRefreshTokenId(page.getRefreshTokenId());
			businessPage.setSingleLineAddress(page.getSingleLineAddress());
			businessPage.setUserId(page.getUserId());
			businessPage.setUserName(businessPage.getUserName());
			businessPage.setWebsiteUrl(page.getWebsiteUrl());
			businessPage.setIsValid(isPageValid(businessPage) ? 1 : 0);
			businessPage.setBusinessPageRequestId(requestId);
			if(Objects.nonNull(page.getStorefrontAddress())) {
				businessPage.setPostalCode(page.getStorefrontAddress().getPostalCode());
				businessPage.setCity(page.getStorefrontAddress().getLocality());
				businessPage.setState(page.getStorefrontAddress().getAdministrativeArea());
				businessPage.setCountry_code(page.getStorefrontAddress().getRegionCode());
			}
			businessPage.setScope(page.getScope());
			businessPage.setRegularHours(JSONUtils.toJSON(page.getRegularHours()));
			businessPage.setDescription(page.getDescription());
			businessPage.setCategories(JSONUtils.toJSON(page.getCategories()));
			socialFreemiumRepository.saveAndFlush(businessPage);
			//Todo add validity check same as reseller for locations-completed
			//validityCheckForGMB(businessPage,email);
		}
	}

	public boolean isPageValid(SocialFreemiumLocations gmbPage) {
		LOGGER.info("GMB location state for the page is {}", gmbPage);
		if ( gmbPage != null && gmbPage.getLocationState() != null && !gmbPage.getLocationState().isEmpty() ) {
			if(Objects.isNull(gmbPage.getPlaceId())) {
				return false;
			}
			GMBLocationState locationStateObj = JSONUtils.fromJSON(gmbPage.getLocationState(), GMBLocationState.class);
			if ( Objects.isNull(locationStateObj) ||
					Boolean.TRUE.equals(locationStateObj.getIsSuspended()) ||
					Boolean.TRUE.equals(locationStateObj.getIsDuplicate()) ||
					Boolean.FALSE.equals(locationStateObj.getHasVoiceOfMerchant()) ||
					Boolean.TRUE.equals(locationStateObj.getIsDisabled())) {
				return false;
			}
			return true;
		}
		return false;
	}

	@Override
	public GMBLocationMessageAgentResponse getAllLocations(Long businessNumber) {
		try {
			GMBLocationMessageAgentResponse response = new GMBLocationMessageAgentResponse();
			List<GMBLocationMessageAgent> gmbLocationMessageAgentList = new ArrayList<>();
			List<BusinessGoogleMyBusinessLocation> googleMyBusinessLocations = socialGMBRepo.findByEnterpriseIdAndBusinessIdNotNull(businessNumber);
			if(CollectionUtils.isEmpty(googleMyBusinessLocations)) {
				LOGGER.info("[GMB] No location details found for enterprise {}", businessNumber);
				return response;
			}
			List<Integer> businessIds = googleMyBusinessLocations.stream().map(BusinessGoogleMyBusinessLocation::getBusinessId).collect(Collectors.toList());
			List<BusinessBizLiteDto> bizLiteDtos = businessCoreService.getBusinessLiteDtoByBusinessIds(businessIds);
			if(CollectionUtils.isEmpty(bizLiteDtos)){
				return response;
			}
			Map<Integer,BusinessBizLiteDto> businessBizLiteDtoMap = bizLiteDtos.stream().collect(Collectors.toMap(BusinessBizLiteDto::getId,Function.identity()));
			LOGGER.info("[GMB] location details fetched for enterprise {} with response {}", businessNumber, bizLiteDtos);

			googleMyBusinessLocations.forEach(page -> {
				GMBLocationMessageAgent gmbLocationMessageAgent = new GMBLocationMessageAgent();
				// TODO: 19/01/23 @Navroj remove find first -- done
				BusinessBizLiteDto businessBizLiteDto = businessBizLiteDtoMap.get(page.getBusinessId());
				if(Objects.nonNull(businessBizLiteDto)) {
					gmbLocationMessageAgent.setBusinessAlias(Objects.isNull(businessBizLiteDto.getAlias1()) ? businessBizLiteDto.getDisplayName() :  businessBizLiteDto.getAlias1());
					gmbLocationMessageAgent.setBusinessId(businessBizLiteDto.getId());
					gmbLocationMessageAgent.setBusinessNumber(businessBizLiteDto.getBusinessNumber());
					gmbLocationMessageAgent.setBusinessName(businessBizLiteDto.getDisplayName());
				}
				gmbLocationMessageAgent.setIsSelectable(Objects.isNull(page.getAgentId()));
				gmbLocationMessageAgentList.add(gmbLocationMessageAgent);
			});
			response.setData(gmbLocationMessageAgentList);
			return response;
		} catch (Exception e) {
			LOGGER.info("Something went wrong while fetching locations for enterpriseId {} with error {}", businessNumber, e);
			// TODO: 19/01/23 error code should not be null -- done
			throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED, e.getMessage());
		}
	}

	@Override
	public SocialGenericResponse deleteBrandInformation(Integer brandId) {
		SocialGenericResponse response = new SocialGenericResponse();
		String comments;
		GoogleMessagesAgent agent = agentRepo.findOne(brandId);
		if(Objects.isNull(agent) || StringUtils.isEmpty(agent.getStatus())) {
			LOGGER.info("No agent found with id {}", brandId);
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_AGENT_UPDATE_ERROR);
		}
		try {
			GoogleAgentStatus status = GoogleAgentStatus.valueOf(agent.getStatus());
			LOGGER.info("Delete request for agent id : {} with status : {}",brandId,status);
			if(status.compareTo(GoogleAgentStatus.VERIFIED) >= 0){
				throw new BirdeyeSocialException(ErrorCodes.VERIFIED_AGENT_DELETE,"Verified agent can not be deleted");
			}
			if(status.compareTo(GoogleAgentStatus.BRAND_CREATED) == 0 || status.compareTo(GoogleAgentStatus.AGENT_CREATION_ERROR)==0){
				googleBizCommService.deleteBrand(agent.getBrandName());
				googleMsgAgentService.deleteAgent(agent);
			}else if(status.compareTo(GoogleAgentStatus.AGENT_CREATED)==0 || status.compareTo(GoogleAgentStatus.VERIFICATION_ERROR)==0){
				googleBizCommService.deleteAgent(agent.getAgentName());
				googleMsgAgentService.deleteAgent(agent);
			}else {
				googleMsgAgentService.deleteAgent(agent);
			}
			socialGMBRepo.updateAgentIdByBusinessIdsNull(agent.getId()); // To set locations as null while delete brand
			// TODO: 18/01/23 @Navroj -- done
			response.setSuccess(true);
			return response;
		} catch (GoogleJsonResponseException googleExp) {
			LOGGER.error("Google Exception in delete agent", googleExp);
			// TODO: 25/01/23 @Navroj -- done
			comments = googleExp.getDetails().getMessage();
			agent.setComments(comments);
			agent.setStatus(GoogleAgentStatus.LAUNCH_ERROR.name());
			agentRepo.save(agent);
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_BRAND_CREATE_ERROR
					,permissionMappingService.getDataByChannelAndModuleAndPermissionCode(SocialChannel.GMB.getName(),SocialMessageModule.GMB_MESSAGING,ErrorCodes.GOOGLE_MESSAGES_BRAND_CREATE_ERROR.value()));
		} catch (Exception exp) {
			LOGGER.error("Exception in delete agent", exp);
			comments = com.birdeye.social.utils.StringUtils.isNotEmpty(exp.getMessage())
					? exp.getMessage().substring(0, Math.min(300, exp.getMessage().length()))
					: "Exception in agent delete";
			agent.setComments(comments);
			agent.setStatus(GoogleAgentStatus.LAUNCH_ERROR.name());
			agentRepo.save(agent);
			throw new BirdeyeSocialException(ErrorCodes.GOOGLE_MESSAGES_BRAND_CREATE_ERROR,
					permissionMappingService.getDataByChannelAndModuleAndPermissionCode(SocialChannel.GMB.getName(),SocialMessageModule.GMB_MESSAGING,ErrorCodes.GOOGLE_MESSAGES_BRAND_CREATE_ERROR.value()));
		}
		finally{
			if (Objects.nonNull(agent)) {
				createAuditRequest(null, null, null, null,
						null, agent.getBrandDisplayName(), agent.getAgentDisplayName(), brandId, agent.getStatus());
			}
			else{
				createAuditRequest(null, null, null, null,
						null, null, null, brandId, "NO_BRAND_FOUND");
			}
		}
	}

	@Override
	public void updateLocationStateOfLocations(Integer agentId, String locationStatus){
		socialGMBRepo.updateLocationStateForPages(agentId, locationStatus);
	}

	@Override
	public void setDefaultMessageAndLogo(GoogleMessagesAgent messagesAgent, AgentResponse agentResponse, Long enterpriseId) {
		agentResponse.setBrandName(com.birdeye.social.utils.StringUtils.isEmpty(messagesAgent.getBrandDisplayName())
				? businessCoreService.getBusinessLiteByNumber(enterpriseId).getBusinessName(): messagesAgent.getBrandDisplayName());
		agentResponse.setWelcomeMessage(com.birdeye.social.utils.StringUtils.isEmpty(messagesAgent.getWelcomeMsg())
				? CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultWelcomeMessage() : messagesAgent.getWelcomeMsg());
		agentResponse.setOfflineMessage(com.birdeye.social.utils.StringUtils.isEmpty(messagesAgent.getOfflineMsg())
				? CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultOfflineMessageMessage(): messagesAgent.getOfflineMsg());
		String defaultLogoUrl = com.birdeye.social.utils.StringUtils.isEmpty(messagesAgent.getLogoUrl())
				? CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultLogoUrl():messagesAgent.getLogoUrl() ;
		agentResponse.setLogoUrl(defaultLogoUrl);
	}

	private Map<Integer,List<BusinessLocations>> getLocationDetails (List<BusinessGoogleMyBusinessLocation> gmbDetails,List<Integer> agentIds) {
		Map<Integer,List<BusinessLocations>> agentIdAndBusinessLocationsMap = new HashMap<>();
		LOGGER.info("Set location details for the agents ids : {}",agentIds);
//		List<BusinessGoogleMyBusinessLocation> gmbDetails = googleMyBusinessPageService.findByAgentIds(agentIds);
		if(CollectionUtils.isEmpty(gmbDetails)) {
			LOGGER.warn("No pages found for the given agents : {}",agentIds);
			return agentIdAndBusinessLocationsMap;
		}
		Map<Integer,BusinessGoogleMyBusinessLocation> businessIdAndGMBMap = gmbDetails.stream()
				.filter(page -> Objects.nonNull(page.getBusinessId())).collect(Collectors.toMap(BusinessGoogleMyBusinessLocation::getBusinessId,Function.identity()));
		if(CollectionUtils.isEmpty(businessIdAndGMBMap.keySet())){
			LOGGER.warn("No mapped pages found for the given agents : {}",agentIds);
			return agentIdAndBusinessLocationsMap;
		}
		List<BusinessBizLiteDto> businessDetails =  businessCoreService.getBusinessLiteDtoByBusinessIds(new ArrayList<>(businessIdAndGMBMap.keySet()));
		businessDetails.forEach(data -> {
			BusinessLocations location =  new BusinessLocations();
			location.setBusinessAlias(Objects.isNull(data.getAlias1()) ? data.getName() : data.getAlias1());
			location.setBusinessId(data.getId());
			if(businessIdAndGMBMap.containsKey(data.getId())) {
				BusinessGoogleMyBusinessLocation businessLocation = businessIdAndGMBMap.get(data.getId());
				location.setAgentId(businessLocation.getAgentId());
				location.setIsValid(Objects.equals(businessLocation.getgMsgLocationStatus(),GoogleAgentStatus.LAUNCHED.name())
						&& businessLocation.getIsValid() == 1);
				if (!location.getIsValid()) {
					if (Objects.isNull(businessLocation.getgMsgLocationStatus())) {
						location.setErrorMessage("Location is valid but not launched");
					} else {
						location.setErrorMessage((GoogleAgentStatus.UN_LAUNCHED.name().compareTo(businessLocation.getgMsgLocationStatus()) <= 0)
								? "Location is un-launched or error launching in agent" : "Location is invalid");
					}
				}
			}
			location.setBusinessNumber(data.getBusinessNumber());
			location.setName(data.getName());
			agentIdAndBusinessLocationsMap.computeIfAbsent(location.getAgentId(), k -> new ArrayList<>()).add(location);
		});
		LOGGER.info("Added location to response for agent ids : {}",agentIds);
		return agentIdAndBusinessLocationsMap;
	}

	public AgentDetailsResponse getAllAgentDetails(Long enterpriseId){
		LOGGER.info("GET agent list for enterprise id :{}",enterpriseId);
		AgentDetailsResponse response = new AgentDetailsResponse();
		List<GoogleMessagingResponse> googleMessagingResponses =new ArrayList<>();
		try {
			List<GoogleMessagesAgent> agentDetails = googleMsgAgentService.findAllByEnterpriseId(enterpriseId);
			LOGGER.info("Size of agent list for enterprise id :{} is :{}",enterpriseId,agentDetails.size());
			if(CollectionUtils.isEmpty(agentDetails)) {
				return response;
			}
			List<Integer> agentIds = agentDetails.stream().map(GoogleMessagesAgent::getId).collect(Collectors.toList());
			List<BusinessGoogleMyBusinessLocation> gmbDetails = googleMyBusinessPageService.findByAgentIds(agentIds);
			Map<Integer,Integer> agentIdAndUnmapCount = prepareMapOfAgentAndUnmapCount(gmbDetails);
			Map<Integer,List<BusinessLocations>> agentIdAndBusinessLocationsMap = getLocationDetails(gmbDetails,agentIds);
			agentDetails.forEach(agent -> {
				GoogleMessagingResponse data = new GoogleMessagingResponse();
				data.setId(agent.getId());
				data.setName(agent.getWidgetName());
				data.setBrandName(StringUtils.isEmpty(agent.getBrandDisplayName())
						? businessCoreService.getBusinessLiteByNumber(enterpriseId).getBusinessName(): agent.getBrandDisplayName());
				data.setWelcomeMessage(StringUtils.isEmpty(agent.getWelcomeMsg())
						? CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultWelcomeMessage() : agent.getWelcomeMsg());
				data.setOfflineMessage(StringUtils.isEmpty(agent.getOfflineMsg())
						? CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultOfflineMessageMessage(): agent.getOfflineMsg());
				String defaultLogoUrl = StringUtils.isEmpty(agent.getLogoUrl())
						? CacheManager.getInstance().getCache(SystemPropertiesCache.class).getDefaultLogoUrl():agent.getLogoUrl() ;
				data.setLogoUrl(defaultLogoUrl);
				data.setBusinessLocations(agentIdAndBusinessLocationsMap.get(agent.getId()));
				data.setIsVerified(isVerified(agent));
				data.setBrandCreated(isBrandCreated(agent));
				Integer unMapCount = agentIdAndUnmapCount.get(agent.getId());
				data.setUnMappedCount(Objects.isNull(unMapCount) ? 0 : unMapCount);
				setAgentStatus(data,agent);
				googleMessagingResponses.add(data);
			});
			response.setData(googleMessagingResponses);
			LOGGER.info("Return agent response for enterprise id : {}",enterpriseId);
			return response;
		} catch(Exception e) {
			throw new BirdeyeSocialException(ErrorCodes.UNKNOWN_ERROR_OCCURRED,e.getLocalizedMessage());
		}
	}

	private Map<Integer, Integer> prepareMapOfAgentAndUnmapCount(List<BusinessGoogleMyBusinessLocation> gmbDetails) {
		Map<Integer, Integer> agentIdAndUnmapCount = new HashMap<>();
		if(CollectionUtils.isEmpty(gmbDetails)){
			return agentIdAndUnmapCount;
		}
		gmbDetails.forEach(page -> {
			if(Objects.isNull(page.getBusinessId())){
				if(agentIdAndUnmapCount.containsKey(page.getAgentId())){
					agentIdAndUnmapCount.put(page.getAgentId(),agentIdAndUnmapCount.get(page.getAgentId())+1);
				}else{
					agentIdAndUnmapCount.put(page.getAgentId(),1);
				}
			}
		});
		return agentIdAndUnmapCount;
	}

	private Boolean isVerified(GoogleMessagesAgent agent) {
		if(Objects.isNull(agent) || StringUtils.isEmpty(agent.getStatus())){
			return false;
		}
		GoogleAgentStatus googleAgentStatus = GoogleAgentStatus.valueOf(agent.getStatus());
		return googleAgentStatus.compareTo(GoogleAgentStatus.VERIFICATION_ERROR) > 0;
	}

	private Boolean isBrandCreated(GoogleMessagesAgent agent) {
		if(Objects.isNull(agent) || StringUtils.isEmpty(agent.getStatus())){
			return false;
		}
		GoogleAgentStatus googleAgentStatus = GoogleAgentStatus.valueOf(agent.getStatus());
		return googleAgentStatus.compareTo(GoogleAgentStatus.BRAND_CREATED) >= 0;
	}

	private void setAgentStatus(GoogleMessagingResponse response, GoogleMessagesAgent agent) {
		String finalStatus = AgentStatus.DRAFT.getName();
		String errorMessage;
		if (StringUtils.isEmpty(agent.getStatus())) {
			response.setStatus(finalStatus);
			return;
		}
		GoogleAgentStatus googleAgentStatus = GoogleAgentStatus.valueOf(agent.getStatus());
		if (googleAgentStatus.compareTo(GoogleAgentStatus.VERIFICATION_ERROR) < 0) {
			finalStatus = AgentStatus.DRAFT.getName();
		} else if (googleAgentStatus.compareTo(GoogleAgentStatus.LAUNCHED) < 0) {
			finalStatus = AgentStatus.NOT_ACTIVE.getName();
			if (StringUtils.isNotEmpty(agent.getStatus())) {
				GoogleAgentStatus agentStatus = GoogleAgentStatus.valueOf(agent.getStatus());
				SocialMessageModule messageModule =  SocialMessageModule.GMB_MESSAGING;
				switch (agentStatus) {
					case VERIFICATION_ERROR:
						errorMessage = permissionMappingService.getDataByChannelAndModuleAndPermissionName(GMB,messageModule,GoogleAgentStatus.VERIFICATION_ERROR.name());
						break;

					case VERIFIED:
						errorMessage = permissionMappingService.getDataByChannelAndModuleAndPermissionName(GMB,messageModule,GoogleAgentStatus.VERIFIED.name());
						break;

					case LAUNCH_ERROR:
						errorMessage = permissionMappingService.getDataByChannelAndModuleAndPermissionName(GMB,messageModule,GoogleAgentStatus.LAUNCH_ERROR.name());
						break;

					case UN_LAUNCHED:
						errorMessage = permissionMappingService.getDataByChannelAndModuleAndPermissionName(GMB,messageModule,GoogleAgentStatus.UN_LAUNCHED.name());
						break;

					default:
						errorMessage = "Agent is in Draft state";
				}
				response.setErrorMessage(errorMessage);
			}
		} else if (googleAgentStatus.compareTo(GoogleAgentStatus.LAUNCHED) == 0) {
			finalStatus = AgentStatus.ACTIVE.getName();
		}
		response.setStatus(finalStatus);
	}

	@Override
	public void validateSocialServiceTokens(SocialTokenValidationDTO payload) {
		GoogleRefreshToken refreshToken = googleRefreshTokenRepo.findOne(payload.getId());
		if (refreshToken == null || StringUtils.isEmpty(refreshToken.getRefreshToken())) {
			return;
		}
		LOGGER.info("[GooglePlus Job] Initiating GooglePlus valdation for: {}", refreshToken);
		refreshToken.setLastScannedOn(new Date());
		GMBPageDTO valid = socialPostGooglePlusService.validateRefreshToken(refreshToken);
		if (!valid.isValid()) {
			refreshToken.setIsValid(0);
			LOGGER.info("[GooglePlus Job] GooglePlus Refresh token is invalid: {}", refreshToken);
		}
		List<BusinessGoogleMyBusinessLocation> existingPages = googleMyBusinessPageService.getGMBPagesByRefreshTokenId(refreshToken.getId());

		LOGGER.info("[GooglePlus Job] size of list of pages picked for validation check are {}", existingPages.size());

		for(BusinessGoogleMyBusinessLocation page: existingPages) {
			if (!valid.isValid()) {
				page.setIsValid(0);
				commonService.sendPageDisconnectAuditEvent(page, null);
			}
		}
		socialGMBRepo.save(existingPages);
		googleRefreshTokenRepo.saveAndFlush(refreshToken);
	}

	@Override
	public void upsertAutoLaunchStatus(Long enterpriseId, boolean enabled) {
		SocialBusinessProperty socialBusinessProperty = socialBusinessPropertyService.findByEnterpriseId(enterpriseId);
		if(Objects.isNull(socialBusinessProperty)){
			socialBusinessProperty = new SocialBusinessProperty();
			socialBusinessProperty.setAutoLaunchedEnable(enabled ? 1 : 0);
			socialBusinessProperty.setEnterpriseId(enterpriseId);
		}else{
			evictSocialBusinessProperty(enterpriseId);
			socialBusinessProperty.setAutoLaunchedEnable(enabled ? 1 : 0);
		}
		socialBusinessPropertyService.save(socialBusinessProperty);
	}

	@Override
	@Cacheable(value = "getGoogleAccessTokenCache", key = "#locationId", unless = "#result == null")
	public String getGoogleAccessToken(String locationId) {
		String accessToken;
		List<BusinessGoogleMyBusinessLocation> googleMyBusinessLocations = socialGMBRepo.findByLocationId(locationId);
		if(CollectionUtils.isEmpty(googleMyBusinessLocations)){
			return "Unable to get access token";
		}
		BusinessGoogleMyBusinessLocation location = googleMyBusinessLocations.get(0);
		accessToken = googleAccessTokenCache.getGoogleAccessToken(location);
		return accessToken;
	}

	@Override
	public void checkGMBPageValid(SocialAuditRequest socialAuditRequest) {
		LOGGER.info("Request received to check validity of the external id : {} with error : {}",socialAuditRequest.getExternalId(),socialAuditRequest.getErrorResponse());
		String locationId = socialAuditRequest.getExternalId();
		List<BusinessGoogleMyBusinessLocation> businessLocations = socialGMBRepo.findByLocationId(locationId);
		if(CollectionUtils.isEmpty(businessLocations)) {
			LOGGER.info("No location found with id: {}",locationId);
			return;
		}
		BusinessGoogleMyBusinessLocation businessLocation = businessLocations.get(0);
		if(businessLocation.getIsValid() == 0){
			LOGGER.info("Location is already marked as invalid with id: {}",locationId);
			return;
		}
		try {
			String accessToken = googleAccessTokenCache.getGoogleAccessTokenByRefreshToken(businessLocation);
			googleMyBusinessPageService.getVoiceOfMerchant(accessToken,businessLocation,false);
		}catch (BirdeyeSocialException e){
			if(Objects.equals(e.getCode(),ErrorCodes.REQUEST_ENTITY_NOT_FOUND.value())
					|| Objects.equals(e.getCode(),ErrorCodes.CALLER_DOES_NOT_HAVE_PERMISSION.value())){
				businessLocation.setIsValid(0);
				businessLocation.setErrorLog(e.getMessage());
			}
		}finally {
			socialGMBRepo.save(businessLocation);
		}
	}

	public void evictSocialBusinessProperty(Long enterpriseId) {
		LOGGER.info("socialBusinessProperty evict for enterpriseId id {}", enterpriseId);
		cacheService.clearCacheByKey(SOCIAL_BUSINESS_PROPERTY,String.valueOf(enterpriseId));
	}

	public void validityCheckForGMB(SocialFreemiumLocations businessPage,String email) {
		if(Objects.isNull(businessPage.getLocationId())){
			LOGGER.info("Location id can not be null");
			return;
		}
		LOGGER.info("Request received to update validity column for location ids: {}",businessPage.getLocationId());
		if(Objects.isNull(businessPage)){
			LOGGER.info("Unable to get page for location id:{}",businessPage.getLocationId());
			return;
		}
		BusinessGoogleMyBusinessLocation page=createPageInMyBusiessLocations(businessPage,email);
		try{
			String accessToken = googleAuthenticationService.getGoogleAccessToken(page.getRefreshTokenId());
			googleMyBusinessPageService.getVoiceOfMerchant(accessToken,page,false);
		}catch (Exception e){
			LOGGER.info("Unable to get access token for account id:{}",page.getLocationId());
		}
		//checkValidity(page);
		businessPage.setIsValid(page.getIsValid());
		businessPage.setIsVerified(page.getIsVerified());
		businessPage.setLocationState(page.getLocationState());
		businessPage.setLocationId(page.getLocationId());
		socialFreemiumRepository.saveAndFlush(businessPage);
		LOGGER.info("Successfully saved to db");

	}

	public void checkValidity(BusinessGoogleMyBusinessLocation pages){
		if(Objects.nonNull(pages)){
			LOGGER.info("Page ids to update validity : {}",pages.getLocationId());
			try {
				Long enterpriseId = pages.getEnterpriseId();
				Boolean googleMessageEnabled = false;
				if(Objects.nonNull(enterpriseId))
					googleMessageEnabled = isGoogleMessageEnabled(null,enterpriseId);
				Boolean finalGoogleMessageEnabled = googleMessageEnabled;
				getValidity(pages,finalGoogleMessageEnabled);
			}catch (Exception e){
				LOGGER.info("Error from core unable to update validity type {}",e.getMessage());
			}
		}
	}

	@Override
	public String googleAuthUrlV2(String origin, Boolean isLogin) throws Exception {
		ConsumerTokenAndSecret appCreds = commonService.getAppKeyAndTokenV2( "googleplus");
		return googleAuthenticationService.getAuthenticationUrlForGoogleV2(origin,appCreds.getToken(), isLogin);
	}

	@Override
	public List<ApprovalPageInfo> findByGMBLocationId(String pageId) {
		return socialGMBRepo.findByLocationIdLite(pageId);
	}

	@Override
	public List<String> findByBusinessIdIn(List<Integer> businessIds) {
		return socialGMBRepo.findDistinctPageIdByBusinessIdIn(businessIds);
	}

	@Override
	public void removeGMBPageByPageIds(List<String> pagesIds) {
		List<BusinessGoogleMyBusinessLocation> page = socialGMBRepo.findByLocationIdIn(pagesIds);
		if (CollectionUtils.isEmpty(page)) {
			LOGGER.info("No pages found for page ids : {}", pagesIds);
			return;
		}
		removeGMBPages(page);
	}

	@Override
	public List<SocialBusinessPageInfo> findByBusinessIds(List<Integer> businessIds) {
		return socialGMBRepo.findSocialBusinessPageInfoByBusinessIdIn(businessIds);
	}

	@Override
	public void initiateDPSync() {
		LOGGER.info("Request received to initiate profile pic sync for gmb accounts");
		try {
			LOGGER.info("gmb profile image sync is not supported currently");
//			List<BusinessGoogleMyBusinessLocation> gmbAccounts = socialGMBRepo.findByIsValidAndEnterpriseIdNotNull(1);
//			gmbAccounts.forEach(gmbAccount -> {
//				DpSyncRequest dpSyncRequest = new DpSyncRequest();
//				dpSyncRequest.setId(gmbAccount.getId());
//				kafkaProducer.sendObjectV1(GMB_DP_SYNC_TOPIC,dpSyncRequest);
//			});

		} catch (Exception e) {
			LOGGER.info("Exception while initiating gmb Dp sync request: ",e);
		}
	}

	@Override
	public void syncGmbDP(DpSyncRequest gmbDpSyncRequest) {
		LOGGER.info("Request received to sync dp for IG account with id: {}",gmbDpSyncRequest.getId());
		try {
//			BusinessGoogleMyBusinessLocation gmbAccount = socialGMBRepo.findById(gmbDpSyncRequest.getId());
//
//			if(Objects.nonNull(profileInfo) && Objects.nonNull(profileInfo.getProfile_picture_url())) {
//				gmbAccount.setPictureUrl(profileInfo.getProfile_picture_url());
//				socialGMBRepo.saveAndFlush(gmbAccount);
//				commonService.uploadPageImageToCDN(gmbAccount);
//			}

		} catch (Exception e) {
			LOGGER.info("Exception while syncing dp for IG account with id: {}",gmbDpSyncRequest.getId(),e);
		}
	}

	@Override
	public List<Integer> getMappedResellerLeafLocations(List<Integer> resellerLeafLocationIds) {
		if(CollectionUtils.isNotEmpty(resellerLeafLocationIds)) {
			return socialGMBRepo.findAllIdByBusinessIdIn(resellerLeafLocationIds);
		}

		return Collections.emptyList();
  }
	private void updateGMBLocationForReseller(BusinessGoogleMyBusinessLocation gmbLocation, String type, Integer locationId) {
	    if (Constants.RESELLER.equals(type)) {
	        BusinessLiteDTO businessLiteDTO = iBusinessCoreService.getBusinessLite(locationId, false);
	        gmbLocation.setShortAccountId(businessLiteDTO.getAccountId());

	        if (Objects.isNull(businessLiteDTO.getEnterpriseId())) {
	            gmbLocation.setEnterpriseId(businessLiteDTO.getBusinessNumber());
	        } else {
	            gmbLocation.setEnterpriseId(businessLiteDTO.getEnterpriseNumber());
	        }
	    }
	}

	/**
	 * @param requestIds
	 */
	@Override
	public List<String> getMappedRequestIds(Set<String> requestIds) {
		return socialGMBRepo.findDistinctRequestIdByRequestIdIn(requestIds);
	}

}