package com.birdeye.social.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.dao.SocialPostsAssetsRepository;
import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.entities.SocialPostsAssets;
import com.birdeye.social.model.MediaData;
import com.birdeye.social.model.PostAssetsData;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PostAssetServiceImpl implements PostAssetService {
    @Autowired
    private SocialPostsAssetsRepository socialPostsAssetsRepository;

    @Autowired
    private ISocialPostsAssetService socialPostsAssetService;

    @Override
    public List<MediaData> getMediaDataForCal(String imageIds, Long enterpriseLongId, String type){
        List<MediaData> listOfMediaData = new ArrayList<>();

        String[] idsConversion = imageIds.split(",");
        List<Integer> ids = new ArrayList<>();
        for (String id : idsConversion) {
            ids.add(Integer.parseInt(id));
        }
        List<SocialPostsAssets> postAssets = socialPostsAssetsRepository.findByIds(ids.stream().collect(Collectors.toSet()));
        for (SocialPostsAssets media : postAssets) {
            String url = Objects.isNull(media.getImageUrl()) ? media.getVideoUrl() : media.getImageUrl();
            String fullUrl = socialPostsAssetService.getCompleteCdnUrlFromBaseUrl(url, String.valueOf(enterpriseLongId));
            listOfMediaData.add(new MediaData(fullUrl, media.getAssetMetaData()));
        }
        return listOfMediaData;
    }

    @Override
    public List<String> getMediaRequestForCal(String imageIds, Long enterpriseBizNum, String type){
        List<String> listOfUrls = new ArrayList<>();

        String[] idsConversion = imageIds.split(",");
        List<Integer> ids = new ArrayList<>();
        for (String id : idsConversion) {
            ids.add(Integer.parseInt(id));
        }
        List<String> urlsByIds = Constants.IMAGE.equals(type) ? socialPostsAssetsRepository.findImageUrlsByIds(ids) : socialPostsAssetsRepository.findVideoUrlsByIds(ids);
        for (String url : urlsByIds) {
            listOfUrls.add(socialPostsAssetService.getCompleteCdnUrlFromBaseUrl(url, String.valueOf(enterpriseBizNum)));
        }
        return listOfUrls;
    }

    @Override
    public PostAssetsData setSocialPostsAssetsDataForCal(String assetIds, Long parentId, String type){

        String[] idsConversion = assetIds.split(",");
        List<Integer> ids = new ArrayList<>();
        for (String id : idsConversion) {
            ids.add(Integer.parseInt(id));
        }
        List<SocialPostsAssets> socialPostAssets = socialPostsAssetsRepository.findByIds(ids.stream().collect(Collectors.toSet()));
        PostAssetsData postAssetsData = new PostAssetsData();
        if (Constants.IMAGE.equals(type)) {
            List<MediaData> images = new ArrayList<>();
            for (SocialPostsAssets socialPostsAsset : socialPostAssets) {
                String fullUrl = socialPostsAssetService.getCompleteCdnUrlFromBaseUrl(socialPostsAsset.getImageUrl(), String.valueOf(parentId));
                images.add(new MediaData(fullUrl, socialPostsAsset.getAssetMetaData()));
            }
            postAssetsData.setImages(images);
        } else {
            List<MediaData> videos = new ArrayList<>();
            List<String> videoThumbnailUrls = new ArrayList<>();
            for (SocialPostsAssets socialPostsAsset : socialPostAssets) {
                String fullUrl = socialPostsAssetService.getCompleteCdnUrlFromBaseUrl(socialPostsAsset.getVideoUrl(), String.valueOf(parentId));
                videos.add(new MediaData(fullUrl, socialPostsAsset.getAssetMetaData()));
                videoThumbnailUrls.add(socialPostsAsset.getVideoThumbnail());
            }
            postAssetsData.setVideos(videos);
            postAssetsData.setVideoThumbnailUrls(videoThumbnailUrls);
        }
        return postAssetsData;
    }

    @Override
    public List<SocialPostsAssets> getPostsAssetsById(Map<Integer, SocialPostsAssets> postAssetsMap, String imageIds) {
        List<SocialPostsAssets> socialPostsAssetsList = new ArrayList<>();
        if(StringUtils.isEmpty(imageIds)) return socialPostsAssetsList;
        String[] ids = imageIds.split(",");
        for(String id: ids) {
            Integer assetId = Integer.parseInt(id);
            SocialPostsAssets postAsset = postAssetsMap.get(assetId);
            if(Objects.nonNull(postAsset)) socialPostsAssetsList.add(postAsset);
        }

        return socialPostsAssetsList;
    }

    @Override
    public List<SocialPostsAssets> getPostsAssetsById(Map<Integer, SocialPostsAssets> postAssetsMap, List<Integer> assetIds) {
        List<SocialPostsAssets> socialPostsAssetsList = new ArrayList<>();
        if(assetIds.isEmpty()) return socialPostsAssetsList;
        for(Integer assetId: assetIds) {
            SocialPostsAssets postAsset = postAssetsMap.get(assetId);
            if(Objects.nonNull(postAsset)) socialPostsAssetsList.add(postAsset);
        }
        return socialPostsAssetsList;
    }

    @Override
    public Map<Integer, SocialPostsAssets> getPostAssetsForList(List<SocialPost> socialMasterPostList) {
        Map<Integer, SocialPostsAssets> responseMap = new HashMap<>();

        if(CollectionUtils.isEmpty(socialMasterPostList)) return responseMap;

        List<Integer> ids = new ArrayList<>();
        for(SocialPost masterPost: socialMasterPostList) {
            String imageIds = masterPost.getImageIds();
            String videoIds = masterPost.getVideoIds();
            String compressedImageIds = masterPost.getCompressedImageIds();
            if(StringUtils.isNotEmpty(imageIds)) {
                String[] idsConversion = imageIds.split(",");
                for (String id : idsConversion) {
                    ids.add(Integer.parseInt(id));
                }
            }
            if(StringUtils.isNotEmpty(videoIds)) {
                String[] idsConversion = videoIds.split(",");
                for (String id : idsConversion) {
                    ids.add(Integer.parseInt(id));
                }
            }
            if(StringUtils.isNotEmpty(compressedImageIds)) {
                String[] idsConversion = compressedImageIds.split(",");
                for (String id : idsConversion) {
                    ids.add(Integer.parseInt(id));
                }
            }
        }

        if(CollectionUtils.isNotEmpty(ids)) {
            List<SocialPostsAssets> socialPostsAssetsList = socialPostsAssetService.findByIds(ids.stream().collect(Collectors.toSet()));
            if(CollectionUtils.isNotEmpty(socialPostsAssetsList)) {
                responseMap = socialPostsAssetsList.stream().collect(Collectors.toMap(s->s.getId(), s->s));
            }
        }

        return responseMap;
    }

    @Override
    public List<MediaData> getMediaDataV2(List<SocialPostsAssets> postAssets, Long enterpriseLongId, String type){
        List<MediaData> listOfMediaData = new ArrayList<>();

        for (SocialPostsAssets media : postAssets) {
            String url = Objects.isNull(media.getImageUrl())
                    ? socialPostsAssetService.getCompleteVideoUrlFromPostAsset(media, enterpriseLongId.toString())
                    : socialPostsAssetService.getCompleteImageUrlFromPostAsset(media, enterpriseLongId.toString());
            listOfMediaData.add(new MediaData(url, media.getAssetMetaData()));
        }
        return listOfMediaData;
    }

    @Override
    public List<String> getMediaRequestV2(List<SocialPostsAssets> postsAssets, Long enterpriseBizNum, String type){
        List<String> listOfUrls = new ArrayList<>();
        if(CollectionUtils.isEmpty(postsAssets)) return listOfUrls;
        return Constants.IMAGE.equals(type)
                ? postsAssets.stream().map(asset -> socialPostsAssetService.getCompleteImageUrlFromPostAsset(asset, enterpriseBizNum.toString())).collect(Collectors.toList())
                : postsAssets.stream().map(asset -> socialPostsAssetService.getCompleteVideoUrlFromPostAsset(asset, enterpriseBizNum.toString())).collect(Collectors.toList());
    }

    @Override
    public PostAssetsData setSocialPostsAssetsDataV2(List<SocialPostsAssets> socialPostAssets, Long parentId, String type){

        PostAssetsData postAssetsData = new PostAssetsData();
        if(CollectionUtils.isEmpty(socialPostAssets)) return postAssetsData;
        if (Constants.IMAGE.equals(type)) {
            List<MediaData> images = new ArrayList<>();
            for (SocialPostsAssets socialPostsAsset : socialPostAssets) {
                String fullUrl = socialPostsAssetService.getCompleteImageUrlFromPostAsset(socialPostsAsset, parentId.toString());
                images.add(new MediaData(fullUrl, socialPostsAsset.getAssetMetaData()));
            }
            postAssetsData.setImages(images);
        } else {
            List<MediaData> videos = new ArrayList<>();
            List<String> videoThumbnailUrls = new ArrayList<>();
            for (SocialPostsAssets socialPostsAsset : socialPostAssets) {
                String fullUrl = socialPostsAssetService.getCompleteVideoUrlFromPostAsset(socialPostsAsset, parentId.toString());
                videos.add(new MediaData(fullUrl, socialPostsAsset.getAssetMetaData()));
                if(StringUtils.isNotEmpty(socialPostsAsset.getVideoThumbnail())) {
                    videoThumbnailUrls.add(socialPostsAsset.getVideoThumbnail());
                }
            }
            postAssetsData.setVideos(videos);
            postAssetsData.setVideoThumbnailUrls(videoThumbnailUrls);
        }
        return postAssetsData;
    }
}
