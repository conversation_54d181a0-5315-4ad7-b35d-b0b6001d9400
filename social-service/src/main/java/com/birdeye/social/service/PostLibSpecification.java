package com.birdeye.social.service;
/**
 * <AUTHOR>
 *
 */
import com.birdeye.social.constant.AiGeneratedPostRecommendedType;
import com.birdeye.social.constant.FilterPostType;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.PostLibMaster;
import com.birdeye.social.model.FilterPageRequest;
import com.birdeye.social.model.PostLibFilter;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class PostLibSpecification{
    public static Specification<PostLibMaster> getMasterPostLib(PostLibFilter request, List<Integer> taggedPostsIds, Integer enterpriseId,
                                                                Date startDate, Date endDate, List<Integer> socialChannelIds) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            // filter to remove AI Posts
            predicates.add(criteriaBuilder.or(
                    criteriaBuilder.equal(root.get("aiSuggested"), 0),
                    criteriaBuilder.isNull(root.get("aiSuggested"))
            ));
            predicates.add(criteriaBuilder.equal(root.get("enterpriseId"), enterpriseId));
            if (CollectionUtils.isNotEmpty(socialChannelIds)) {
                predicates.add(criteriaBuilder.isTrue(
                        root.join("postLib").get("sourceId").in(socialChannelIds)
                ));
            }

            if(StringUtils.isNotEmpty(request.getSearchText())) {
                String text = request.getSearchText();
                text = text.replace("%", "\\%").replace("_", "\\_").replace("'", "\\'");
                predicates.add(criteriaBuilder.isTrue(
                        criteriaBuilder.like(root.get("postText"), "%" + text + "%")
                ));
            }

            if (Objects.nonNull(startDate)) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdDate"), startDate));
            }

            if (Objects.nonNull(endDate)) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createdDate"), endDate));
            }

            if (CollectionUtils.isNotEmpty(request.getCreators())) {
                predicates.add(root.get("createdBy").in(request.getCreators()));
            }
            predicates.add(criteriaBuilder.or(
                    criteriaBuilder.equal(root.get("notRecommended"), AiGeneratedPostRecommendedType.RECOMMENDED.getValue()),
                    criteriaBuilder.isNull(root.get("notRecommended"))
            ));

            List<FilterPostType> postType = request.getPostType();
            Predicate aiPostPredicate = null;
            Predicate mediaTypePredicate = null;

            if (CollectionUtils.isNotEmpty(postType)) {
                if (postType.contains(FilterPostType.AI_ASSISTED_POSTS) ||
                        postType.contains(FilterPostType.MANUAL_POSTS) ||
                        postType.contains(FilterPostType.AI_SUGGESTED_POSTS)) {

                    if (postType.contains(FilterPostType.AI_ASSISTED_POSTS) &&
                            postType.contains(FilterPostType.MANUAL_POSTS) &&
                            postType.contains(FilterPostType.AI_SUGGESTED_POSTS)) {
                        aiPostPredicate = criteriaBuilder.or(
                                criteriaBuilder.equal(root.get("aiPost"), 1),  // AI-Assisted
                                criteriaBuilder.equal(root.get("aiPost"), 0),  // Manual
                                criteriaBuilder.equal(root.get("aiSuggested"), 1)   // AI Suggested
                        );
                    } else if (postType.contains(FilterPostType.AI_ASSISTED_POSTS)) {
                        aiPostPredicate = criteriaBuilder.equal(root.get("aiPost"), 1); // AI-Assisted
                    } else if (postType.contains(FilterPostType.MANUAL_POSTS)) {
                        aiPostPredicate = criteriaBuilder.equal(root.get("aiPost"), 0); // Manual
                    } else if (postType.contains(FilterPostType.AI_SUGGESTED_POSTS)) {
                        aiPostPredicate = criteriaBuilder.equal(root.get("aiSuggested"), 1); // AI Suggested
                    }
                }

                if (postType.contains(FilterPostType.TEXT_ONLY_POSTS) || postType.contains(FilterPostType.IMAGE_ONLY_POSTS)
                        || postType.contains(FilterPostType.VIDEO_ONLY_POSTS)) {
                    Predicate textOnlyCriteria = criteriaBuilder.and(
                            criteriaBuilder.or(root.get("imageIds").isNull(), criteriaBuilder.equal(root.get("imageIds"), "")),
                            criteriaBuilder.or(root.get("videoIds").isNull(), criteriaBuilder.equal(root.get("videoIds"), ""))
                    );
                    Predicate imageOnlyCriteria = criteriaBuilder.and(root.get("imageIds").isNotNull(), criteriaBuilder.notEqual(root.get("imageIds"), ""));
                    Predicate videoOnlyCriteria = criteriaBuilder.and(root.get("videoIds").isNotNull(), criteriaBuilder.notEqual(root.get("videoIds"), ""));

                    if (postType.contains(FilterPostType.TEXT_ONLY_POSTS)
                            && postType.contains(FilterPostType.IMAGE_ONLY_POSTS)
                            && postType.contains(FilterPostType.VIDEO_ONLY_POSTS)) {
                        mediaTypePredicate = criteriaBuilder.or(textOnlyCriteria, imageOnlyCriteria, videoOnlyCriteria);
                    } else if (postType.contains(FilterPostType.TEXT_ONLY_POSTS)
                            && postType.contains(FilterPostType.IMAGE_ONLY_POSTS)) {
                        mediaTypePredicate = criteriaBuilder.or(textOnlyCriteria, imageOnlyCriteria);
                    } else if (postType.contains(FilterPostType.IMAGE_ONLY_POSTS)
                            && postType.contains(FilterPostType.VIDEO_ONLY_POSTS)) {
                        mediaTypePredicate = criteriaBuilder.or(imageOnlyCriteria, videoOnlyCriteria);
                    } else if (postType.contains(FilterPostType.TEXT_ONLY_POSTS)
                            && postType.contains(FilterPostType.VIDEO_ONLY_POSTS)) {
                        mediaTypePredicate = criteriaBuilder.or(textOnlyCriteria, videoOnlyCriteria);
                    } else if (postType.contains(FilterPostType.TEXT_ONLY_POSTS)) {
                        mediaTypePredicate = textOnlyCriteria;
                    } else if (postType.contains(FilterPostType.IMAGE_ONLY_POSTS)) {
                        mediaTypePredicate = imageOnlyCriteria;
                    } else if (postType.contains(FilterPostType.VIDEO_ONLY_POSTS)) {
                        mediaTypePredicate = videoOnlyCriteria;
                    }
                }

                if (aiPostPredicate != null || mediaTypePredicate != null) {
                    Predicate combinedPredicate = (aiPostPredicate != null && mediaTypePredicate != null)
                            ? criteriaBuilder.or(aiPostPredicate, mediaTypePredicate)
                            : (aiPostPredicate != null ? aiPostPredicate : mediaTypePredicate);
                    predicates.add(combinedPredicate);
                }
            }
            if (CollectionUtils.isNotEmpty(taggedPostsIds)) {
                predicates.add(root.get("id").in(taggedPostsIds));
            }

            query.distinct(true);

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
