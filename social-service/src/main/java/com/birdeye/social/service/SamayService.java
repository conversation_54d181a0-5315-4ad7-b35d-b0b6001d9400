package com.birdeye.social.service;

import com.birdeye.social.dto.SamayScheduleEventRequest;
import com.birdeye.social.entities.report.BusinessPosts;

public interface SamayService {

    boolean pushMessageToSamayScheduler(SamayScheduleEventRequest samayRequest);

    boolean cancelEventToSamayScheduler(Object samayRequest);

    void processPendingPostThroughSamay(Integer processingPostId);

    void sendEventToSamayForStoryInsightsRefresh(BusinessPosts bp);
}
