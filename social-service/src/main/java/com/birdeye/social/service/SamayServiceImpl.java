package com.birdeye.social.service;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.KafkaTopicEnum;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dto.SamayScheduleEventRequest;
import com.birdeye.social.dto.report.PostInsightsEventRequest;
import com.birdeye.social.entities.report.BusinessPosts;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SamayUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("samayService")
@Slf4j
public class SamayServiceImpl implements SamayService {

    @Autowired
    private KafkaProducerService kafkaService;


    @Override
    public boolean pushMessageToSamayScheduler(SamayScheduleEventRequest samayRequest) {
       return kafkaService.sendObjectV1("social-scheduling-event", samayRequest);
    }

    @Override
    public boolean cancelEventToSamayScheduler(Object cancelRequest) {
        return kafkaService.sendObjectV1("cancel-social-post-scheduling-event", cancelRequest);
    }

    public void processPendingPostThroughSamay(Integer processingPostId) {
        log.info("Send retry request to samay for processingPostId: {} and channel facebook", processingPostId);
        int processPendingPostTime = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getIntegerProperty(SystemPropertiesCache.PROCESS_PENDING_POST_TIME, 20);
        long scheduleDateToEpoch = (System.currentTimeMillis() / 1000) + processPendingPostTime;

        Map<String, Object> request = new HashMap<>();
        request.put("processingPostId", processingPostId);

        SamayScheduleEventRequest samayRequest = SamayUtils.getDefaultSamayRequest(JSONUtils.toJSON(request), null, null,
                scheduleDateToEpoch, KafkaTopicEnum.PROCESS_PENDING_POST.getName());

        pushMessageToSamayScheduler(samayRequest);
    }


    /**
     * Sends an event to Samay scheduler for refreshing Instagram/fb story insights
     *
     * @param bp The BusinessPosts object containing post information
     * @return
     */
    public void sendEventToSamayForStoryInsightsRefresh(BusinessPosts bp) {
        log.info("Scheduling story insights refresh for business post ID: {}, source ID: {}", bp.getId(), bp.getSourceId());
        int storyInsightsRefreshTime = CacheManager.getInstance()
                .getCache(SystemPropertiesCache.class)
                .getIntegerProperty(SystemPropertiesCache.STORY_INSIGHTS_REFRESH_REMAINING_MINUTES, 10);

        PostInsightsEventRequest eventRequest = new PostInsightsEventRequest();
        List<Integer> postIds = Collections.singletonList(bp.getId());
        eventRequest.setSourceId(bp.getSourceId());
        eventRequest.setChannel(SocialChannel.getSocialChannelNameById(bp.getSourceId()));
        eventRequest.setPostId(postIds);
        eventRequest.setIsFreshRequest(false);

        long  scheduleDateToEpoch = bp.getPublishDate().getTime() - ((long) storyInsightsRefreshTime * 60 * 1000);

        SamayScheduleEventRequest samayRequest = SamayUtils.getDefaultSamayRequest(JSONUtils.toJSON(eventRequest), null, null,
                scheduleDateToEpoch, KafkaTopicEnum.BUSINESS_POST_TOPIC.getName());

        pushMessageToSamayScheduler(samayRequest);
    }
}
