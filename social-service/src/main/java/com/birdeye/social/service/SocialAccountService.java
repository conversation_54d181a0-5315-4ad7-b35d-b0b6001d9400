package com.birdeye.social.service;

import com.birdeye.social.businessCore.BusinessStatusData;
import com.birdeye.social.constant.SocialSetupAuditEnum;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.entities.*;
import com.birdeye.social.model.*;
import com.birdeye.social.model.instagram.TokenUpdateRequest;
import com.birdeye.social.model.tiktok.arbor.TiktokAuthUrlResponse;
import com.birdeye.social.model.usage.InboxStatusResponseComplete;
import com.birdeye.social.platform.messages.OAuthRequestMessage;
import com.birdeye.social.sro.*;
import com.birdeye.social.sro.socialReseller.SocialBusinessStatusRequest;
import com.birdeye.social.sro.socialReseller.SocialBusinessStatusResponse;
import org.springframework.scheduling.annotation.Async;

import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

public interface SocialAccountService
{
	Map<String, Object> saveLocationPageMapping(String channel,Integer locationId,String fbPageId, Integer userId, Boolean force, Long enterpriseId) throws Exception;

	boolean isPageBusinessPlaceIdSame(String channel, Integer locationId, String pageId, Integer userId) throws Exception;
	
	void removePageMappings(String channel, List<LocationPageMappingRequest> pageMappings, boolean unlink, Long enterpriseId) throws Exception;

	void removeMappingForPageIds(RemovePageMappingRequest removePageMappingRequest, Long enterpriseId) throws Exception;

	void cleanupPageMappings(String channel, List<FacebookRemovePageMappingCleanupRequest> pageMappings) throws Exception;
	
	Map<String, ChannelPageCount> getChannelPageCounts(Long businessId) throws Exception;
	
	ReviewOptionsInfo getReviewShareOptions(String channel, Long enterpriseId, Integer startIndex, Integer count, Integer userId);
	
	ReviewOptionDto updateReviewSharingOptions(Integer businessId, String pageId, Boolean autoPostingEnabled, Integer starVal,
			Integer postVal, String channel, Integer userId) throws Exception;

	LocationPageMapping getLocationMappingPages(String channel, Long businessId, Integer startIndex, Integer endIndex, String context, Integer sort,Integer userId, Integer accountId) throws Exception;

	LocationPageMapping getLocationMappingPagesV2(String channel,LocationMappingRequest request, Integer businessId) throws Exception;

	void removePage(String channel, List<LocationPageMappingRequest> input, Long businessId, Integer userId, Long enterpriseId) throws Exception;

	AutoSuggesterPagesResponse findUnmappedPages(String channel, Long locationId) throws Exception;

	void updateInvalidPage(String channel, String pageId);

	String getGoogleAuthUrl(Long businessId, Boolean redirectToSetup) throws Exception;

	String getYoutubeAuthUrl(Long businessId, Boolean redirectToSetup,String domainName, String origin) throws Exception;

	void cancelRequest(String channel, Long businessId, Boolean forceCancel);


	void submitGetPageRequest(String channel, ChannelAuthRequest authRequest) throws Exception;

	ChannelPageInfo connectPagesV1(String channel, Long enterpriseId, Map<String, List<String>> pageIds, Integer accountId) throws Exception;

	OAuthRequestMessage generateTwitterAuthenticationURL(Long businessId) throws Exception;

	void removeInactiveIntegration(Long enterpriseId);
	
	ChannelLocationInfo getSingleLocationMappingPages(String channel, Integer businessId) throws Exception;

	void updateInvalidPageCounter(String pageId, Object details);


	void markFBIntegrationInvalid();

	void reconnectAllPage(String channel, ChannelAllPageReconnectRequest request, Long businessId, Integer userId,String type) throws Exception;

	@Deprecated
	ChannelPageInfo getIntegrationRequestInfo(String channel, Long businessId, Boolean reconnectFlag) throws Exception;

	CheckStatusResponse getIntegrationRequestStatus(String channel, Long businessId, Boolean reconnectFlag);

	LastReconnectDetails getIntegrationSummary(String channel, Long businessId, Boolean reconnectFlag);

	void updateFBTokenPermissionsForAllPages();

	void backupPages(List<Long> enterpriseId);

	List<Integer> migrate(Integer limit);

	ConnectedPages getPages(String channel, Long enterpriseId, Integer userId, String type);

	SmbChannelData allSourcePageForSMB(Long businessId);

	Map<String, LocationPageListInfo> getPageAllSource(Long businessId);

	ChannelConnectedPageInfo checkForConnectedPages(Long accountId, String channel);

	ChannelSetupStatus getChannelWiseSetupStatus(Long accountId, String channel);

	ChannelSetupStatus getChannelWisePageConnectionStatus(Long accountId, String channel);

	BusinessIntegrationStatus getBusinessIntegrationStatus(Integer businessId);
	
	void processAutoMappingInitRequest(AutoMappingRequest request);

	void processAutoMappingMatchedRequest(AutoMappingMatchedRequest request);

	void processDisconnectedLocations(String channel);

	@Async
	void performActionOnDisconnected(ChannelDisconnectRequest channelDisconnectRequest);

	void updateFirebaseForAutoMapping();

	SmbUnmappedDataResponse getUnmappedPagesForSmb(String channel);
    
	void removeInactiveBusinessIntegration(BusinessStatusData request);

	BrandDataResponse getBrandData(Long enterpriseId);

	InboxStatusResponse checkgMsgStatus(Long enterpriseId);

	void setupAgent(SetupAgentRequest req) throws Exception;

	void updateAgent(UpdateAgentRequest req) throws Exception;

	void createBrand(CreateBrandRequest req) throws Exception;

	GoogleMessagesAgentLiteDto getAgent(AgentRequest agentRequest) throws Exception;

	void unLaunchAgent(AgentUnLaunchRequest request) throws Exception;

	void editWidget(EditWidgetRequest req);

	void saveGoogleMessagingAudit(CreateGoogleMessagingAuditRequest req);

	void moveBusinessLocation(LocationMovementDto locationMovementdto);

	void restoreRules(BusinessStatusData request);

	String getConnectCTA(BusinessLiteDTO businessInfo, String channel);

	OAuthRequestMessage generateTwitterAuthenticationURL(Long businessId, String origin) throws Exception;

	UnmappedLocationMappingResponse getUnmappedLocationCount(UnmappedLocationMappingReq request);

	void handleGbmLocationMovement(LocationMovementDto locationMovementDto) throws Exception;

	List<SocialElasticDto> fetchRawPagesSocialEsDto(List<String> integrationIds, String channel);

	List<Number> fetchRawPagesId(List<String> integrationIds,String channel);

	List<SocialElasticDto> fetchRawPagesSocialEsDtoByChannel(String channel);

	FetchPageResponse getIntegrationPage(String channel, Long businessId);

    void initiateGmbAccountFetch(ChannelAuthRequest authRequest,String type);

	SSOResponse googleSSOAuth(GoogleSSOAuthRequest googleSSOAuthRequest);
	SSOResponse fbSSOAuth(FacebookSSOAuthRequest fbSSOAuthRequest);

	GoogleWhiteLabelAccountsResponse getWhiteLabelAccounts(GoogleWhiteLabelAccountsRequest request);

	void addWhiteLabelAccounts(List<String> urls);


	void initiatePageFetchFreemium(FreemiumSetupRequest authRequest);

	FreemiumFetchPageResponse fetchFreemiumPages(Integer SessionId);

	ChannelPageInfo connectFreemiumpage(FreemiumConnectRequest freemiumConnectRequest);

	FreemiumStatusResponse fetchStatus(Integer sessionId);

	GmbAccountInfo getGmbAccount(Long businessId,String type);

    void gmbPageFetchByAccount(GMBAccountDTO gmbAccountDTO,Long businessId,String type);

    void refreshGmbUserAccount(String userEmail, Long parentId,String type);

    void gmbBackStatus(Long businessId);

	InboxStatusResponseComplete getMessengerStatus(InboxStatus request);

	void auditSocialPage(SocialSetupAudit socialSetupAudit, String channel);

	void updateAccessToken(TokenUpdateRequest request);

	String getFbIdByBusinessId (Integer businessId);

	Map<String, Object> getByBusinessId(String facebookPageId, List<String> fields);
	
	//Hacky 
	ConnectedPages getConnectedPages(String channel, Long enterpriseId, Integer userId);

	ChannelStatusResponse getChannelIntegrationStatus(String channel, SocialFilterRequest request);

    void updateLaunchStatus(UpdateLaunchStatusAgentRequest req);

    void updateStatus(List<String> requestIds);

	void setUpAgentsForReseller(LaunchAgentDTO launchAgentDTO) throws Exception;

	void updatedFbPagesWithPhone();

	AgentAccountResponse getAccount(String agentId);
	void createAgent(SetupAgentRequest request,String brandName) throws Exception;

	SocialChannelsByLocationResponse getAllChannelsByBusinessId(Integer businessId);

	SocialLocationsMappedResponse getAllMappedLocation(Long enterpriseId);

	AgentResponse createWidget(CreateWidgetRequest request);

	void relaunchLocation(CreateLocationRequest req);

	void validateToken(SocialTokenValidationDTO payload);
	
	void markPageAsInvalid(SocialTokenValidationDTO payload);

	void reconnectValidPages(AccessTokenUpdateRequest updateRequest);

	void processAdsEvent(FacebookEventRequest request, String channel);

	String googleAuthSetupUrl(String origin) throws Exception;

	String googleAuthLoginUrl(String domain) throws Exception;

	void initiateDPSync(String channel);

	void syncDP(String channel, DpSyncRequest dpSyncRequest);

	void removePagesByIds(DeleteEventRequest deleteEventRequest);
	AccountValidationResponse getAllChannelWiseSetupStatus(AccountValidationRequest request);

    SocialBusinessStatusResponse getStatusOfBusinessWithChannel(SocialBusinessStatusRequest request);
	void unsubscribeAccountInit(String channel, Integer months, SocialSetupAuditEnum action);

	void unsubscribePage(SocialSetupAudit socialSetupAudit);

	List<SocialElasticDto> fetchRawPagesSocialEsDtoByChannelAndId(String channel, Integer rawId);

	ConnectedPages getPagesForPostReconnect(String channel, Long enterpriseId, Integer userId, String type, SocialPostPageConnectRequest request);

	void updateAddress(SocialScanEventDTO socialScanEventDTO, String channel);

	LocationPageMappingAllChannels getLocationMappingPagesAllChannels(LocationMappingRequest input, Integer accountId) throws Exception;

	TiktokAuthUrlResponse integrationAuthUrl(String channel, String origin) throws URISyntaxException;

	ResellerLeafLocationResponse getAllLocations(ResellerLeafLocationRequest requestMap, String channel, Integer accountId, Integer userId, Integer page, Integer size);

	PaginatedConnectedPages getPaginatedPages(String channel, GetPagesRequest request);

	SocialModulePermissionStatusResponse postPermissionStatus(Long enterpriseId, List<String> modules, String channel);

    void restorePosts(List<Long> enterpriseIds);

	void checkInvalidStatePageRequest(CheckInvalidGetPageState checkInvalidGetPageState);
}

