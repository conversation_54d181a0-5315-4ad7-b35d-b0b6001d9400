package com.birdeye.social.service;

import static java.util.stream.Collectors.groupingBy;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.assetlibrary.SocialAssetLibraryConstants;
import com.birdeye.social.model.notification.SocialNotificationAudit;
import com.birdeye.social.sro.SocialEsSyncDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.*;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.script.mustache.SearchTemplateResponse;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.elasticdto.SocialElasticDto;
import com.birdeye.social.elasticdto.builder.SocialElasticDtoBuilder;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.model.ChannelPageRemoved;
import com.birdeye.social.model.LinkedinConnectRequest;
import com.birdeye.social.model.SocialConnectPageRequest;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.SocialEsValidRequest;
import com.birdeye.social.utils.JSONUtils;
import com.fasterxml.jackson.annotation.JsonInclude;

import freemarker.template.Configuration;
import freemarker.template.Template;

@Service
public class SocialEsServiceImpl implements ISocialEsService {

    private static final Logger LOG = LoggerFactory.getLogger(SocialEsServiceImpl.class);
    public static final String INDEX = "social";

    public static final String AUDIT_INDEX = "notification_audit";

    public static final String ASSET_LIB_INDEX = "asset_lib";

    @Autowired
    private EsService esService;

    @Autowired
    private Configuration freeMarkerConfig;

    @Autowired
    private SocialAccountService socialAccountService;

    @Autowired
    private SocialLinkedinService socialLinkedinService;

    @Override
    public void addConnectedPage(SocialConnectPageRequest socialConnectPageRequest) throws IOException {
        LOG.info("Request received to add connected page in elastic search for channel with request : {} {}", socialConnectPageRequest.getChannel(), JSONUtils.toJSON(socialConnectPageRequest));
        List<SocialElasticDto> socialElasticDtos = new ArrayList<>();
        if (SocialChannel.LINKEDIN.getName().equalsIgnoreCase(socialConnectPageRequest.getChannel())) {
            socialElasticDtos = socialLinkedinService.fetchRawPagesSocialEsDto(socialConnectPageRequest.getLinkedinConnectRequests());
        } else {
            socialElasticDtos = socialAccountService.fetchRawPagesSocialEsDto(socialConnectPageRequest.getIntegrationIds(), socialConnectPageRequest.getChannel());
        }
        if (CollectionUtils.isNotEmpty(socialElasticDtos)) {
            uploadBulkDocument(socialElasticDtos);
        }
    }

    @Override
    public void addMapping(LocationPageMappingRequest socialMappingPageRequest, String channel) throws IOException {
        LOG.info("Request received to add business mapping for connected page in elastic search for channel with request : {} {}", channel, JSONUtils.toJSON(socialMappingPageRequest));
        List<Number> rawIds = new ArrayList<>();
        List<String> pages = new ArrayList<>();
        pages.add(socialMappingPageRequest.getPageId());
        if (SocialChannel.LINKEDIN.name().equalsIgnoreCase(channel)) {
            rawIds = socialLinkedinService.fetchRawPagesId(pages, socialMappingPageRequest.getType());
        } else {
            rawIds = socialAccountService.fetchRawPagesId(pages, channel);
        }
        if (CollectionUtils.isNotEmpty(rawIds) && CollectionUtils.size(rawIds) == 1) {
            Integer rawId = (Integer) rawIds.get(0);
            GetResponse getResponse = esService.fetchEsDocumentByDocId(channel + "_" + rawId.toString(), INDEX);
            if (getResponse.isExists()) {
                SocialElasticDto socialElasticDto = new SocialElasticDtoBuilder().with($ -> $.business_id = socialMappingPageRequest.getLocationId().toString()).build();
                updateEs(channel, rawId, socialElasticDto);
            } else {
                LOG.info("No document found in elastic search to add mapping hence adding document for channel with request : {} {}", channel, JSONUtils.toJSON(socialMappingPageRequest));
                List<SocialElasticDto> socialElasticDtos;
                if (SocialChannel.LINKEDIN.name().equalsIgnoreCase(channel)) {
                    LinkedinConnectRequest linkedinConnectRequest = new LinkedinConnectRequest();
                    linkedinConnectRequest.setType(socialMappingPageRequest.getType());
                    linkedinConnectRequest.setId(socialMappingPageRequest.getPageId());
                    socialElasticDtos = socialLinkedinService.fetchRawPagesSocialEsDto(Arrays.asList(linkedinConnectRequest));
                } else {
                    socialElasticDtos = socialAccountService.fetchRawPagesSocialEsDto(pages, channel);
                }

                SocialElasticDto socialElasticDto = socialElasticDtos.get(0);
                SocialElasticDtoBuilder socialElasticDtoBuilder = socialElasticDto.toBuilder();
                socialElasticDto = socialElasticDtoBuilder.with($ -> $.business_id = socialMappingPageRequest.getLocationId().toString()).build();
                socialElasticDtos.clear();
                socialElasticDtos.add(socialElasticDto);
                uploadBulkDocument(socialElasticDtos);
            }
        }
    }

    @Override
    public void removeMapping(List<LocationPageMappingRequest> input, String channel) throws IOException {
        LOG.info("Request received to remove business mapping for connected page in elastic search for channel with request : {} {}", channel, JSONUtils.toJSON(input));
        AtomicReference<List<Number>> rawIds = new AtomicReference();
        if (SocialChannel.LINKEDIN.name().equalsIgnoreCase(channel)) {
            Map<String, List<LocationPageMappingRequest>> pageRequestMapByType = input.stream().collect(groupingBy(LocationPageMappingRequest::getType));
            for (Map.Entry<String, List<LocationPageMappingRequest>> entry : pageRequestMapByType.entrySet()) {
                String pageType = entry.getKey();
                List<LocationPageMappingRequest> pageRequests = entry.getValue();
                List<String> pageIds = pageRequests.stream().map(LocationPageMappingRequest::getPageId).collect(Collectors.toList());
                List<Number> businessLinkedinPages = socialLinkedinService.fetchRawPagesId(pageIds, pageType);
                if (CollectionUtils.isNotEmpty(rawIds.get())) {
                    rawIds.get().addAll(businessLinkedinPages);
                } else {
                    rawIds.set(businessLinkedinPages);
                }
            }
        } else {
            List<String> pages = input.stream().map(LocationPageMappingRequest::getPageId).collect(Collectors.toList());
            rawIds.set(socialAccountService.fetchRawPagesId(pages, channel));
        }
        for (Number rawId : rawIds.get()) {
            GetResponse getResponse = esService.fetchEsDocumentByDocId(channel + "_" + rawId.toString(), INDEX);
            if (getResponse.isExists()) {
                Map<String, Object> params = new HashMap<>();
                params.put("business_id", null);
                esService.updateDocument(params, INDEX, channel + "_" + rawId.toString());
            } else {
                LOG.info("No document found in elastic search to remove mapping hence adding document for channel with request : {} {}", channel, JSONUtils.toJSON(input));
                addDocument(input, channel);
            }
        }
    }

    @Override
    public void removePage(List<ChannelPageRemoved> input) throws IOException {
        if (CollectionUtils.isEmpty(input)) {
            LOG.info("received blank object in es remove page");
            return;
        }
        String channel = input.get(0).getChannelName();
        AtomicReference<List<Integer>> rawIds = new AtomicReference();
        LOG.info("Request received to remove page from elastic search for channel with request : {} {}", channel,
                 JSONUtils.toJSON(input));

        List<Integer> pages = input.stream().map(ChannelPageRemoved::getRawId).collect(Collectors.toList());
        rawIds.set(pages);

        for (Number rawId : rawIds.get()) {
            String id = channel + "_" + rawId.toString();
            esService.deleteDocument(INDEX, id);
        }

    }

    @Override
    public List<SocialElasticDto> fetchAllMappedPagesForAccount(String source, Integer locCount, long accountId) {
        List<SocialElasticDto> socialElasticDtos = new ArrayList<>();
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("channel", source);
        dataMap.put("size", locCount);
        dataMap.put("accountId", String.valueOf(accountId));
        try {
            Template t = freeMarkerConfig.getTemplate("mapped_locations_source_es.ftl");
            String esQuery = FreeMarkerTemplateUtils.processTemplateIntoString(t, dataMap);
            SearchTemplateResponse searchResponse = esService.searchByTemplate(esQuery, INDEX, null);
            SearchHit[] searchHits = searchResponse.getResponse().getHits().getHits();
            if (searchHits.length > 0) {
                for (SearchHit hit : searchHits) {
                    SocialElasticDto socialElasticDto = JSONUtils.fromJSON(hit.getSourceAsString(),
                                                                           SocialElasticDto.class);
                    socialElasticDtos.add(socialElasticDto);
                }
            }
        } catch (Exception e) {
            LOG.error("Error fetching data from ES for source {} and accountId {} ", source, accountId);
            throw new BirdeyeSocialException("ES Call failed for mapped pages of account ", e);
        }
        return socialElasticDtos;
    }

    @Override
    public List<SocialElasticDto> fetchAllIntegratedDisconnectedPages(String[] sourceArray) throws IOException {
        LOG.info("Request received to fetchAllIntegratedDisconnectedPages from elastic search for channels {}", sourceArray);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        TermsQueryBuilder channelTerm = new TermsQueryBuilder("channel", sourceArray);
        TermQueryBuilder isValidTerm = new TermQueryBuilder("is_valid", 0);
        boolQueryBuilder.must(QueryBuilders.existsQuery("business_id"));
        boolQueryBuilder.must(channelTerm);
        boolQueryBuilder.must(isValidTerm);
        return getData(boolQueryBuilder);
    }

    @Override
    public List<SocialElasticDto> fetchAllMappedPagesForAccount(String[] sourceArray, List<String> businessIds) throws IOException {
        LOG.info("Request received to fetchAllMappedPagesForAccount from elastic search for channels {}", sourceArray);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        TermsQueryBuilder channelTerm = new TermsQueryBuilder("channel", sourceArray);
        TermsQueryBuilder businessIdTerms = new TermsQueryBuilder("business_id", businessIds);
        boolQueryBuilder.must(channelTerm);
        boolQueryBuilder.must(businessIdTerms);
        return getData(boolQueryBuilder);
    }

    private List<SocialElasticDto> getData(AbstractQueryBuilder queryBuilder) throws IOException {
        List<SocialElasticDto> socialElasticDtos = new ArrayList<>();
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.size(1000);
        searchRequest.scroll(TimeValue.timeValueSeconds(30));
        searchRequest.source(searchSourceBuilder);
        searchRequest.indices(INDEX);
        SearchResponse searchResponse = esService.search(searchRequest);
        String scrollId = searchResponse.getScrollId();
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        if (searchHits.length > 0) {
            for (SearchHit hit : searchHits) {
                SocialElasticDto socialElasticDto = JSONUtils.fromJSON(hit.getSourceAsString(), SocialElasticDto.class);
                socialElasticDtos.add(socialElasticDto);
            }
        } else {
            clearScrollRequest(scrollId);
            return socialElasticDtos;
        }
        boolean hasNext = true;
        while (hasNext) {
            SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
            scrollRequest.scroll(TimeValue.timeValueSeconds(30));
            SearchResponse searchScrollResponse = esService.scroll(scrollRequest);
            scrollId = searchScrollResponse.getScrollId();
            searchHits = searchScrollResponse.getHits().getHits();
            if (searchHits.length > 0) {
                for (SearchHit hit : searchHits) {
                    SocialElasticDto socialElasticDto = JSONUtils.fromJSON(hit.getSourceAsString(), SocialElasticDto.class);
                    socialElasticDtos.add(socialElasticDto);
                }
            } else {
                hasNext = false;
                clearScrollRequest(scrollId);
            }
        }
        return socialElasticDtos;
    }

    private void clearScrollRequest(String scrollId) throws IOException {
        ClearScrollRequest request = new ClearScrollRequest();
        request.addScrollId(scrollId);
        esService.clearScroll(request);
    }


    @Override
    public void updatePageValidity(SocialEsValidRequest socialEsValidRequest) throws IOException {
        GetResponse getResponse = esService.fetchEsDocumentByDocId(socialEsValidRequest.getChannel() + "_" + socialEsValidRequest.getRawId().toString(), INDEX);
        if (getResponse.isExists()) {
            LOG.info("page found on es update is valid for page {} {} {}", socialEsValidRequest.getChannel(), socialEsValidRequest.getRawId(), socialEsValidRequest.getIsValid());
            Map<String, Object> params = new HashMap<>();
            params.put("is_valid", socialEsValidRequest.getIsValid());
            esService.updateDocument(params, INDEX, socialEsValidRequest.getChannel() + "_" + socialEsValidRequest.getRawId().toString());
        } else {
            LOG.info("no page found on es to update is valid for page {} {} {}", socialEsValidRequest.getChannel(), socialEsValidRequest.getRawId(), socialEsValidRequest.getIsValid());
        }
    }

    @Override
    public void syncEsData(String channel) throws IOException {
        List<SocialElasticDto> socialElasticDtos = socialAccountService.fetchRawPagesSocialEsDtoByChannel(channel);
        LOG.info("Total documents fetched from database for channel {} is {}", channel, socialElasticDtos.size());
        Integer esbatchSize = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getIntegerProperty("es.index.batch.size", 2000);
        for (List<SocialElasticDto> elasticDtos : com.google.common.collect.Lists.partition(socialElasticDtos, esbatchSize)) {
            LOG.info("sending batch for uploading document for channel {}", channel);
            long startTime = System.currentTimeMillis();
            uploadBulkDocument(elasticDtos);
            LOG.info("time taken to index batch is {}", System.currentTimeMillis() - startTime);
        }
    }

    @Override
    public void deleteEsData(String channel) throws IOException {
        LOG.info("request received to delete es document for channel {}", channel);
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        TermsQueryBuilder channelTerm = new TermsQueryBuilder("channel", channel);
        boolQueryBuilder.must(channelTerm);
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        BulkByScrollResponse bulkByScrollResponse = esService.deleteDocumentByQuery(boolQueryBuilder, INDEX);
        LOG.info("total document deleted for channel {} is {}", channel, bulkByScrollResponse.getDeleted());
    }

    @Override
    public void uploadAuditDocument(List<SocialNotificationAudit> audit) {
        try {
            BulkRequest bulkRequest = new BulkRequest();
            audit.forEach(object -> {
                String s = JSONUtils.toJSON(object);
                bulkRequest.add(new IndexRequest(AUDIT_INDEX).routing(object.getEvent_id()).source(s, XContentType.JSON));
            });
            esService.addBulkDocument(bulkRequest);
        } catch (IOException e) {
            LOG.info("IOException while adding data to es : {}", e.getLocalizedMessage());
        }
    }

    @Override
    public Map.Entry<List<Long>, Long> getAssetLibFilteredAssetIdsAndTotalCount(Integer accountId, Map<String, Object> tokenMap, String assetIdEsFieldName) {
        List<Long> assetIds = new LinkedList<>();
        Long totalCount = 0L;
        try {
            Template t = freeMarkerConfig.getTemplate("social_asset_library_fetch.ftl");
            String esQuery = FreeMarkerTemplateUtils.processTemplateIntoString(t, tokenMap);
            LOG.info("[SocialEsServiceImpl] getAssetLibFilteredAssetIdsAndTotalCount ESQuery:{} for accountId:{}", esQuery, accountId);
            SearchTemplateResponse searchResponse = esService.searchByTemplate(esQuery, ASSET_LIB_INDEX, String.valueOf(accountId));
            SearchHit[] searchHits = searchResponse.getResponse().getHits().getHits();
            totalCount = searchResponse.getResponse().getHits().getTotalHits().value;
            if (searchHits.length > 0) {
                for (SearchHit hit : searchHits) {
                    Map<String, Object> sourceMap = hit.getSourceAsMap();
                    assetIds.add(Long.valueOf((Integer) sourceMap.get(assetIdEsFieldName)));
                }
            }
        } catch (Exception e) {
            LOG.error("[SocialEsServiceImpl] getAssetLibFilteredAssetIdsAndTotalCount Error fetching data from ES for index {} and accountId {} ", ASSET_LIB_INDEX, accountId);
            throw new BirdeyeSocialException("ES Call failed for getAssetLibFilteredAssetIdsAndTotalCount", e);
        }
        return new AbstractMap.SimpleEntry<>(assetIds, totalCount);
    }

    @Override
    public void deleteAssetLibraryEsDataByAccountIdAndParentId(Integer accountId, Long parentId) throws IOException {
        LOG.info("[SocialEsServiceImpl] deleteAssetLibraryEsDataByField called for accountId:{} and parentId:{}", accountId, parentId);
        SearchRequest searchRequest = new SearchRequest();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        TermsQueryBuilder accountTerm = new TermsQueryBuilder(SocialAssetLibraryConstants.ASSET_ACCOUNT_ID_ES_FIELD, String.valueOf(accountId));
        TermsQueryBuilder parentTerm = new TermsQueryBuilder(SocialAssetLibraryConstants.ASSET_PARENT_ID_ES_FIELD, String.valueOf(parentId));
        boolQueryBuilder.must(accountTerm);
        boolQueryBuilder.must(parentTerm);
        searchSourceBuilder.query(boolQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        BulkByScrollResponse bulkByScrollResponse = esService.deleteDocumentByQuery(boolQueryBuilder, ASSET_LIB_INDEX);
        LOG.info("[SocialEsServiceImpl] deleteAssetLibraryEsDataByField deleted :{} asset(s) for accountId:{} and parentId:{}", bulkByScrollResponse.getDeleted(),
                 accountId, parentId);
    }

    private void addDocument(List<LocationPageMappingRequest> socialMappingPageRequest, String channel) throws IOException {
        SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest();
        socialConnectPageRequest.setChannel(channel);
        if (SocialChannel.LINKEDIN.name().equalsIgnoreCase(channel)) {
            List<LinkedinConnectRequest> linkedinConnectRequests = new ArrayList<>();
            socialMappingPageRequest.stream().forEach(linkedinPageRequest -> {
                LinkedinConnectRequest linkedinConnectRequest = new LinkedinConnectRequest();
                linkedinConnectRequest.setId(linkedinPageRequest.getPageId());
                linkedinConnectRequest.setType(linkedinPageRequest.getType());
                linkedinConnectRequests.add(linkedinConnectRequest);
            });
            socialConnectPageRequest.setLinkedinConnectRequests(linkedinConnectRequests);
        } else {
            List<String> pages = socialMappingPageRequest.stream().map(LocationPageMappingRequest::getPageId).collect(Collectors.toList());
            socialConnectPageRequest.setIntegrationIds(pages);
        }
        addConnectedPage(socialConnectPageRequest);
    }

    private void updateEs(String channel, Integer rawId, SocialElasticDto socialElasticDto) throws IOException {
        String s = JSONUtils.toJSON(socialElasticDto, JsonInclude.Include.NON_NULL);
        esService.updateDocument(s, INDEX, channel + "_" + rawId.toString());
    }

    private BulkRequest prepareESBulkRequest(List<SocialElasticDto> socialElasticDtos) {
        BulkRequest bulkRequest = new BulkRequest();
        socialElasticDtos.stream().forEach(socialElasticDto -> {
            String s = JSONUtils.toJSON(socialElasticDto);
            bulkRequest.add(new IndexRequest(INDEX).id(socialElasticDto.getChannel() + "_" + socialElasticDto.getRaw_id().toString()).source(s, XContentType.JSON));
        });
        return bulkRequest;
    }

    private void uploadBulkDocument(List<SocialElasticDto> socialElasticDtos) throws IOException {
        BulkRequest bulkRequest = prepareESBulkRequest(socialElasticDtos);
        esService.addBulkDocument(bulkRequest);
    }

    @Override
    public void syncSocialESEvent(SocialEsSyncDTO socialEsSyncDTO) {
        LOG.info("Request received to sync social es for payload: {}",socialEsSyncDTO);
        try {
            List<SocialElasticDto> socialElasticDto = socialAccountService.fetchRawPagesSocialEsDtoByChannelAndId(socialEsSyncDTO.getSocialChannel(), socialEsSyncDTO.getId());
            LOG.info("Documents size fetched from database for channel {} is {}", socialEsSyncDTO.getSocialChannel(), socialElasticDto.size());
            if (CollectionUtils.isNotEmpty(socialElasticDto)) {
                LOG.info("Document updating: {}",socialElasticDto.get(0));
                uploadBulkDocument(socialElasticDto);
            }
        } catch (Exception e) {
            LOG.info("Exception while updating social es for payload: {}",socialEsSyncDTO,e);
        }
    }

}
