package com.birdeye.social.service;
/**
 * <AUTHOR>
 *
 */
import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.BusinessCoreUser;
import com.birdeye.social.dto.BusinessProfileResponse;
import com.birdeye.social.entities.*;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.ai_post.AiPostConfigDetailResponse;
import com.birdeye.social.sro.SocialTagBasicDetail;
import com.birdeye.social.sro.SocialTagEntityMappingActionEvent;
import com.birdeye.social.sro.SocialTagEntityMappingRequest;
import com.birdeye.social.model.post_lib.PostInsightsData;
import com.birdeye.social.sro.bulkupload.BulkUploadPostLibRequest;
import com.birdeye.social.sro.postlibrary.PostInsights;
import com.birdeye.social.sro.postlibrary.PostLibInsightsMetaData;
import com.birdeye.social.utils.ConversionUtils;
import com.birdeye.social.model.linkinbio.LinkInBioPostMetaData;
import com.birdeye.social.service.linkinbio.LinkInBioInternalService;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SocialTagMapper;
import com.birdeye.social.youtube.YoutubeMetaData;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.birdeye.social.service.DtoToEntityConverter.convertPostLibDtoToEntity;
import static com.birdeye.social.service.DtoToEntityConverter.convertPostLibMasterDtoToEntity;
import static com.birdeye.social.utils.CoreConvertUtils.getFullUsername;

@Service
public class SocialPostLibServiceImpl implements SocialPostLibService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SocialPostLibServiceImpl.class);

    @Autowired
    private SocialPostService socialPostService;

    @Autowired
    private PostLibMasterRepo postLibMasterRepo;

    @Autowired
    private PostLibRepo postLibRepo;
    @Autowired
    private PostLibInfoRepo postLibInfoRepo;
    @Autowired
    private IBusinessCoreService businessCoreService;
    @Autowired
    private SocialPostsAssetsRepository socialPostsAssetsRepository;
    @Autowired
    private KafkaProducerService kafkaProducerService;

    private static final String SCHEDULE_DATE_FORMAT = "MM/dd/yyyy HH:mm:ss";
    @Autowired
    private LinkInBioInternalService linkInBioInternalService;
    @Autowired
    private SocialPostsAssetsService socialPostsAssetsService;
    @Autowired
    private BusinessService businessService;

    @Autowired
    private SocialTagService socialTagService;

    @Autowired
    private SocialPostAiService socialPostAiService;

    @Autowired
    private SocialBusinessPropertyRepo socialBusinessPropertyRepo;
    @Autowired
    private SocialTagMapper socialTagMapper;
    @Autowired
    @Qualifier(Constants.SOCIAL_TASK_EXECUTOR)
    private ThreadPoolTaskExecutor executor;


    private void savePostLibInfo(SocialPostInputMessageRequest socialPost, PostLib postLib) {
        PostLibInfo postLibInfo = null;
        if(Objects.isNull(socialPost)){
            return;
        }
        if(Objects.nonNull(socialPost.getId())) {
            postLibInfo = postLibInfoRepo.findFirstByPostLibId(socialPost.getId());
        }
        if(Objects.isNull(postLibInfo)) {
            postLibInfo = new PostLibInfo();
        }
        String postingSite = Objects.isNull(socialPost.getPostingSites()) ? null: socialPost.getPostingSites().entrySet().iterator().next().getKey();
        Integer sourceId = SocialChannel.getSocialChannelByName(postingSite).getId();
        PostingPageScheduler postingPage = socialPost.getPostingSites().get(postingSite);
        if (Objects.nonNull(postingPage)) {
            postLibInfo.setLevelAlias(postingPage.getLevelAlias());
            postLibInfo.setLevelAliasId(postingPage.getLevelId());
            postLibInfo.setLevelNames(CollectionUtils.isNotEmpty(postingPage.getLevelNames()) ? postingPage.getLevelNames() : null);
            postLibInfo.setPageIds(postingPage.getPageIds());
        }
        postLibInfo.setSourceId(sourceId);
        postLibInfo.setPostLibId(postLib.getId());
        postLibInfo.setPostLib(postLib);
        postLibInfo.setCreatedAt(new Date());
        postLibInfoRepo.saveAndFlush(postLibInfo);
        LOGGER.info("Post lib info saved for id: {}",postLibInfo.getId());
    }

    private Integer getSourceId(SocialPostInputMessageRequest socialPost) {
        Integer sourceId = null;
        if (Objects.nonNull(socialPost) && MapUtils.isNotEmpty(socialPost.getPostingSites())) {
            Map.Entry<String, PostingPageScheduler> firstEntry = socialPost.getPostingSites().entrySet().iterator().next();
            sourceId = SocialChannel.getSocialChannelByName(firstEntry.getKey()).getId();
        }
        return sourceId;
    }

    private void processPostLibPosts(SocialPostInputMessageRequest socialPost, Integer masterPostId, PostLibMaster postLibMasterPost) throws JsonProcessingException {
        PostLib postLib = new PostLib();
        convertPostLibDtoToEntity(socialPost, postLib);
        Integer sourceId = getSourceId(socialPost);
        MediaDTO mediaDTO = setMediaData(socialPost);
        postLib.setImageIds(mediaDTO.getImageIds());
        postLib.setCompressedImageIds(mediaDTO.getCompressedImageIds());
        postLib.setVideoIds(mediaDTO.getVideoIds());
        postLib.setMasterPostId(masterPostId);
        postLib.setSourceId(sourceId);
        postLib.setPostLibMaster(postLibMasterPost);
        if(Objects.nonNull(socialPost.getAiPost()) && socialPost.getAiPost()) {
            postLib.setAiPost(1);
        } else {
            postLib.setAiPost(0);
        }
        postLibRepo.saveAndFlush(postLib);
        LOGGER.info("Social post saved: {}", postLib);
        savePostLibInfo(socialPost, postLib);
    }

    private List<String> getChannelNameListForMasterPost(SocialMasterPostInputMessages postInputMessages) {
        List<String> channelNameList = new ArrayList<>();
        if (Objects.nonNull(postInputMessages) && CollectionUtils.isNotEmpty(postInputMessages.getSocialPostList())) {
            postInputMessages.getSocialPostList().forEach(socialPost -> {
                if (Objects.nonNull(socialPost) && MapUtils.isNotEmpty(socialPost.getPostingSites())) {
                    String channel = socialPost.getPostingSites().entrySet().iterator().next().getKey();
                    if (!channel.equalsIgnoreCase(Constants.MASTER_POST)) {
                        channelNameList.add(socialPost.getPostingSites().entrySet().iterator().next().getKey());
                    }
                }
            });
        }
        return channelNameList;
    }

    private MediaDTO setMediaData(SocialPostInputMessageRequest socialPost) {
        MediaDTO mediaDTO = new MediaDTO();
        if (CollectionUtils.isNotEmpty(socialPost.getImages())) {
            List<Integer> imageIds = socialPostService.getAssetIds(socialPost.getImages(), Constants.IMAGE, null, socialPost.getBusinessId());
            mediaDTO.setImageIds(StringUtils.join(imageIds,","));
        }
        if (CollectionUtils.isNotEmpty(socialPost.getCompressedImages())) {
            List<MediaData> compressedImagesMediaData = socialPost.getCompressedImages().stream().map(s -> (new MediaData(s, null))).collect(Collectors.toList());
            List<Integer> imageIds = socialPostService.getAssetIds(compressedImagesMediaData, Constants.IMAGE, socialPost.getVideoThumbnailUrl(),socialPost.getBusinessId());
            mediaDTO.setCompressedImageIds(StringUtils.join(imageIds,","));
        }
        if (CollectionUtils.isNotEmpty(socialPost.getVideos())) {
            List<Integer> videoIds = socialPostService.getAssetIds(socialPost.getVideos(), Constants.VIDEO, socialPost.getVideoThumbnailUrl(),socialPost.getBusinessId());
            mediaDTO.setVideoIds(StringUtils.join(videoIds,","));
        }
        return mediaDTO;
    }

    @Override
    public void createPostLibrary(SocialMasterPostInputMessages socialPosts) {
        List<SocialPostInputMessageRequest> socialPostList = socialPosts.getSocialPostList();
        Integer masterPostLibId = null;
        try {
            List<CompletableFuture<Object>> completableFutures = new ArrayList<>();
            boolean taggingNeeded = CollectionUtils.isNotEmpty(socialPosts.getTagMappings());
            ExecutorService yourOwnExecutor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
            PostLibMaster postLibMasterPost = new PostLibMaster();
            Future<Object> postLibTagMappingFuture = null;
            List<String> channelNameList = getChannelNameListForMasterPost(socialPosts);
            for (SocialPostInputMessageRequest socialPost : socialPostList) {
                socialPost.setCreatedBy(socialPosts.getCreatedBy());
                socialPost.setEditedBy(socialPosts.getEditedBy());
                socialPost.setBusinessId(socialPosts.getBusinessId());
                if (MapUtils.isNotEmpty(socialPost.getPostingSites())
                        && socialPost.getPostingSites().containsKey(Constants.MASTER_POST)
                        && Objects.isNull(socialPost.getPostingSites().get(Constants.MASTER_POST))) {
                    convertPostLibMasterDtoToEntity(socialPost, postLibMasterPost, socialPosts.getTagMappings());
                    MediaDTO mediaDTO = setMediaData(socialPost);
                    postLibMasterPost.setImageIds(mediaDTO.getImageIds());
                    postLibMasterPost.setCompressedImageIds(mediaDTO.getCompressedImageIds());
                    postLibMasterPost.setVideoIds(mediaDTO.getVideoIds());
                    postLibMasterPost.setInsightMetadata(ConversionUtils.prepareInsightsMetaData(channelNameList));
                    postLibMasterPost.setSocialMasterPostId(socialPosts.getMasterPostId());
                    if(Objects.nonNull(socialPosts.getAiPost()) && socialPosts.getAiPost()) {
                        postLibMasterPost.setAiPost(1);
                    } else {
                        postLibMasterPost.setAiPost(0);
                    }
                    masterPostLibId = postLibMasterRepo.saveAndFlush(postLibMasterPost).getId();
                    postLibTagMappingFuture = createTagsForPost(taggingNeeded,socialPosts,masterPostLibId);
                } else if (MapUtils.isNotEmpty(socialPost.getPostingSites()) && Objects.nonNull(masterPostLibId)) {
                    Integer finalMasterPostLibId = masterPostLibId;
                    CompletableFuture<Object> requestCompletableFuture = CompletableFuture
                            .supplyAsync(() -> {
                                        try {
                                            processPostLibPosts(socialPost, finalMasterPostLibId, postLibMasterPost);
                                        } catch (Exception e) {
                                            LOGGER.info("Exception while creating post lib: ", e);
                                            throw new SocialBirdeyeException(ErrorCodes.UNKNOWN_ERROR_OCCURRED,Constants.GENERIC_EXCEPTION_MESSAGE);
                                        }
                                        return null;
                                    }, yourOwnExecutor
                            );
                    completableFutures.add(requestCompletableFuture);
                }
            }
            if (CollectionUtils.isNotEmpty(completableFutures)) {
                CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0]))
                        .exceptionally(ex -> null)
                        .join();
            }
            if(taggingNeeded && Objects.nonNull(postLibTagMappingFuture)){
                postLibTagMappingFuture.get();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // Preserve the interrupt status
            throw new SocialBirdeyeException(ErrorCodes.UNKNOWN_ERROR_OCCURRED, Constants.GENERIC_EXCEPTION_MESSAGE);
        } catch (Exception e) {
            LOGGER.info("Exception while creating post lib: ", e);
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, Constants.GENERIC_EXCEPTION_MESSAGE);
        }
    }


    private void fillSocialInputObject(SocialMasterPostInputMessages socialMasterPostInputMessages) {
        for (SocialPostInputMessageRequest socialPostInputMessageRequest : socialMasterPostInputMessages.getSocialPostList()) {
            socialPostInputMessageRequest.setCreatedBy(socialMasterPostInputMessages.getCreatedBy());
            socialPostInputMessageRequest.setEditedBy(socialMasterPostInputMessages.getEditedBy());
            socialPostInputMessageRequest.setBusinessId(socialMasterPostInputMessages.getBusinessId());
        }
    }

    void deleteRemovedChannels(List<Integer> updatedPostLib, List<PostLib> postLibList) {
        List<Integer> unusedPostIds = postLibList.stream().map(PostLib::getId)
                .filter(id -> !updatedPostLib.contains(id)).collect(Collectors.toList());
        LOGGER.info("Remove post lib with ids: {}",unusedPostIds);
        if(CollectionUtils.isNotEmpty(unusedPostIds)) {
            postLibRepo.deleteAllByIdIn(unusedPostIds);
        }
    }

    Boolean checkMasterPost(SocialMasterPostInputMessages socialMasterPostInputMessages) {
        return socialMasterPostInputMessages.getSocialPostList().get(0).getPostingSites().containsKey(Constants.MASTER_POST);
    }

    @Override
    public void editPostLibrary(SocialMasterPostInputMessages socialMasterPostInputMessages){
        try {
            if (!(MapUtils.isNotEmpty(socialMasterPostInputMessages.getSocialPostList().get(0).getPostingSites()) &&
                    checkMasterPost(socialMasterPostInputMessages))) {
                LOGGER.info("List must have at least 1 entry and 1st entry should be master post: {}", socialMasterPostInputMessages);
                throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST,"List must have at least 1 entry and 1st entry should be master post");
            }
            fillSocialInputObject(socialMasterPostInputMessages);
            List<String> channelNameList = getChannelNameListForMasterPost(socialMasterPostInputMessages);
            SocialPostInputMessageRequest masterPostInput = socialMasterPostInputMessages.getSocialPostList().get(0);
            LOGGER.info("Editing master post lib: {}", masterPostInput);
            PostLibMaster postLibMaster = postLibMasterRepo.findById(masterPostInput.getId());
            if(Objects.isNull(postLibMaster) || !postLibMaster.getEnterpriseId().equals(socialMasterPostInputMessages.getBusinessId())){
                throw new SocialBirdeyeException(ErrorCodes.INVALID_ARGUMENT,"No data is saved for social post library");
            }
            Integer masterPostId;
            boolean taggingNeeded = CollectionUtils.isNotEmpty(socialMasterPostInputMessages.getTagMappings());
            convertPostLibMasterDtoToEntity(masterPostInput, postLibMaster,socialMasterPostInputMessages.getTagMappings());
            MediaDTO mediaDTO = setMediaData(masterPostInput);
            postLibMaster.setImageIds(mediaDTO.getImageIds());
            postLibMaster.setCompressedImageIds(mediaDTO.getCompressedImageIds());
            postLibMaster.setVideoIds(mediaDTO.getVideoIds());
            postLibMaster.setInsightMetadata(ConversionUtils.prepareInsightsMetaData(channelNameList));
            postLibMaster.setTotalEngagements(0);
            postLibMaster.setTotalImpressions(0);
            postLibMaster.setUsageCount(0);
            postLibMaster.setSocialMasterPostId(null);
            if(Objects.nonNull(socialMasterPostInputMessages.getAiPost()) && socialMasterPostInputMessages.getAiPost()) {
                postLibMaster.setAiPost(1);
            } else {
                postLibMaster.setAiPost(0);
            }
            masterPostId = postLibMasterRepo.saveAndFlush(postLibMaster).getId();
            Future<Object> createTags = createTagsForPost(taggingNeeded,socialMasterPostInputMessages,masterPostId);
            List<Integer> postLibIds = socialMasterPostInputMessages.getSocialPostList().stream()
                    .skip(0).map(SocialPostInputMessageRequest::getId).filter(Objects::nonNull)
                    .collect(Collectors.toList());
            Map<Integer, PostLib> postLibMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(postLibIds)) {
                List<PostLib> postLibList = postLibRepo.findAll(postLibIds);
                postLibMap = postLibList.stream()
                        .collect(Collectors.toMap(PostLib::getId, postLib -> postLib));
            }
            List<Integer> updatedPostLib = new ArrayList<>();
            Map<Integer, List<PostLib>> masterLibVSPostLibMap = findMasterLibVSPostLibData(Arrays.asList(masterPostId));
            List<PostLib> postLibList = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(masterLibVSPostLibMap.get(masterPostId))) {
                postLibList = masterLibVSPostLibMap.get(masterPostId);
            }
            for (int i = 1; i < socialMasterPostInputMessages.getSocialPostList().size(); i++) {
                SocialPostInputMessageRequest socialPostInputMessageRequest = socialMasterPostInputMessages.getSocialPostList().get(i);
                LOGGER.info("Editing sub post lib: {} and masterPostId {}", socialPostInputMessageRequest, masterPostId);
                if (Objects.nonNull(socialPostInputMessageRequest.getId())) {
                    PostLib postLib = postLibMap.get(socialPostInputMessageRequest.getId());
                    if(Objects.nonNull(postLib)) {

                        convertPostLibDtoToEntity(socialPostInputMessageRequest, postLib);
                        setPostLibData(postLibMaster, masterPostId, socialPostInputMessageRequest, postLib);
                        postLibRepo.saveAndFlush(postLib);
                        updatedPostLib.add(postLib.getId());
                        savePostLibInfo(socialPostInputMessageRequest, postLib);
                    }
                } else {
                    PostLib postLib = new PostLib();
                    convertPostLibDtoToEntity(socialPostInputMessageRequest, postLib);
                    setPostLibData(postLibMaster,masterPostId,socialPostInputMessageRequest,postLib);
                    postLibRepo.saveAndFlush(postLib);
                    updatedPostLib.add(postLib.getId());
                    savePostLibInfo(socialPostInputMessageRequest, postLib);
                }
            }
            deleteRemovedChannels(updatedPostLib, postLibList);
            // to check weather tag is created or not
            if(taggingNeeded && Objects.nonNull(createTags)){
                createTags.get();
            }
            LOGGER.info("Post lib edited successfully!");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // Preserve the interrupt status
            throw new SocialBirdeyeException(ErrorCodes.UNKNOWN_ERROR_OCCURRED, Constants.GENERIC_EXCEPTION_MESSAGE);
        } catch (Exception e) {
            LOGGER.info("Exception while editing post lib: ", e);
            throw new SocialBirdeyeException(ErrorCodes.INTERNAL_SERVER_ERROR, Constants.GENERIC_EXCEPTION_MESSAGE);
        }

    }

    private Future<Object> createTagsForPost(boolean taggingNeeded, SocialMasterPostInputMessages socialMasterPostInputMessages,Integer masterPostId) {
        Future<Object> postLibTagMappingFuture = null;
        if(taggingNeeded) {
            postLibTagMappingFuture = executor.submit(() -> {
                socialTagService.performTagEntityMappingOperations(new SocialTagEntityMappingRequest(socialMasterPostInputMessages.getTagMappings()),
                        SocialTagEntityType.POST_LIB, Long.valueOf(masterPostId),
                        socialMasterPostInputMessages.getBusinessId(),
                        Objects.isNull(socialMasterPostInputMessages.getCreatedBy()) ?
                                Long.valueOf(socialMasterPostInputMessages.getEditedBy()) : Long.valueOf(socialMasterPostInputMessages.getCreatedBy()),null,false);
                return null;
            });
        }
        return postLibTagMappingFuture;
    }

    private void setPostLibData(PostLibMaster postLibMaster, Integer masterPostId, SocialPostInputMessageRequest socialPostInputMessageRequest, PostLib postLib) {
        Integer sourceId = getSourceId(socialPostInputMessageRequest);
        MediaDTO mediaDTO = setMediaData(socialPostInputMessageRequest);
        postLib.setImageIds(mediaDTO.getImageIds());
        postLib.setCompressedImageIds(mediaDTO.getCompressedImageIds());
        postLib.setVideoIds(mediaDTO.getVideoIds());
        postLib.setMasterPostId(masterPostId);
        postLib.setSourceId(sourceId);
        if(Objects.nonNull(socialPostInputMessageRequest.getAiPost()) && socialPostInputMessageRequest.getAiPost()) {
            postLib.setAiPost(1);
        } else {
            postLib.setAiPost(0);
        }
        postLib.setPostLibMaster(postLibMaster);
    }

    private PostLibEditPostResponse getMasterPostLibObject(PostLibMaster postLibMaster, Map<Integer, SocialPostsAssets> postAssetsMap, Long businessNumber) throws Exception {
        PostLibEditPostResponse postResponse = new PostLibEditPostResponse();
        postResponse.setId(postLibMaster.getId());
        postResponse.setPostText(postLibMaster.getPostText());
        postResponse.setPublishDate(new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(postLibMaster.getCreatedDate()));
        postResponse.setEditedDate(getDate(postLibMaster));
        postResponse.setLinkPreviewUrl(postLibMaster.getLinkPreviewUrl());

        if (StringUtils.isNotEmpty(postLibMaster.getImageIds())) {
            List<SocialPostsAssets> imageAssets = socialPostService.getPostsAssetsById(postAssetsMap, postLibMaster.getImageIds());
            postResponse.setImages(socialPostService.getMediaDataV2(imageAssets, businessNumber, Constants.IMAGE));
        }
        if (Objects.nonNull(postLibMaster.getCompressedImageIds())) {
            List<SocialPostsAssets> compressedImageAssets = socialPostService.getPostsAssetsById(postAssetsMap, postLibMaster.getCompressedImageIds());
            postResponse.setCompressedImages(socialPostService.getMediaRequestV2(compressedImageAssets, businessNumber, Constants.IMAGE));
        }
        if (StringUtils.isNotEmpty(postLibMaster.getVideoIds())) {
            List<SocialPostsAssets> videoAssets = socialPostService.getPostsAssetsById(postAssetsMap, postLibMaster.getVideoIds());
            PostAssetsData postAssetsData = socialPostService.setSocialPostsAssetsDataV2(videoAssets, businessNumber, Constants.VIDEO);
            postResponse.setVideos(postAssetsData.getVideos());
            postResponse.setVideoThumbnails(postAssetsData.getVideoThumbnailUrls());
        }
        if (Objects.nonNull(postLibMaster.getPostMetadata())) {
            postResponse.setMediaSequence(socialPostService.getMediaSequenceV2(postLibMaster, businessNumber));
        }

        Map<String, PostingPageScheduler> postingSites = new HashMap<>();
        postingSites.put(Constants.MASTER_POST, null);
        postResponse.setPostingSites(postingSites);
        postResponse.setApprovalWorkflowId(postLibMaster.getApprovalWorkflowId());
        return postResponse;
    }


    private void setPostingSiteData(PostLibEditPostResponse postResponse, PostLib postLib, PostLibInfo postLibInfo,
                                    Integer businessId, List<String> pageIds) throws IOException {

        PostingPageScheduler postingPageScheduler = new PostingPageScheduler();
        postingPageScheduler.setId(postLibInfo.getId());
        postingPageScheduler.setLevelId(postLibInfo.getLevelAliasId());
        postingPageScheduler.setLevelNames(postLibInfo.getLevelNames());
        postingPageScheduler.setLevelAlias(postLibInfo.getLevelAlias());
        postingPageScheduler.setPageIds(pageIds);

        Map<String, PostingPageScheduler> postingSites = new HashMap<>();
        SocialPostSchedulerMetadata metadata = null;
        if (StringUtils.isEmpty(postLib.getPostMetadata())) {
            return;
        }
        metadata = JSONUtils.fromJSON(postLib.getPostMetadata(), SocialPostSchedulerMetadata.class);
        preparePostingSitesData(postLibInfo, metadata, postingSites, postingPageScheduler, businessId);
        postResponse.setPostingSites(postingSites);
    }

    private void preparePostingSitesData(PostLibInfo postLibInfo, SocialPostSchedulerMetadata metadata,
                                         Map<String, PostingPageScheduler> postingSites,
                                         PostingPageScheduler postingPageScheduler, Integer businessId) throws IOException {
        SocialChannel socialChannel = SocialChannel.getSocialChannelById(postLibInfo.getSourceId());
        if(Objects.isNull(socialChannel)){
            LOGGER.info("No channel found with id :{}",postLibInfo.getSourceId());
            return;
        }
        if(!socialChannel.equals(SocialChannel.GMB)) {
            postingSites.put(socialChannel.getName(), postingPageScheduler);
        }
        if(Objects.isNull(metadata)){
            LOGGER.info("Meta Data not found for post lib :{}",postLibInfo.getPostLibId());
            return;
        }
        switch (socialChannel){
            case GMB:
                postingSites.put(SocialChannel.GOOGLE.getName(), postingPageScheduler);
                postingPageScheduler.setGmbPostMetaData(metadata.getGmbPostMetaDataObj());
                break;
            case TWITTER:
                postingPageScheduler.setLocationTagMetaData(JSONUtils.fromJSON(metadata.getLocationTagMetaData(), LocationTagMetaDataRequest.class));
                break;
            case YOUTUBE:
                postingPageScheduler.setYoutubeMetaDataRequest(JSONUtils.fromJSON(metadata.getYoutubePostMetaData(), YoutubeMetaData.class));
                break;
            case INSTAGRAM:
                postingPageScheduler.setIgPostMetadata(JSONUtils.fromJSON(metadata.getIgPostMetadata(), IgPostMetadata.class));
                if(StringUtils.isNotEmpty(metadata.getLinkInBioDetails())) {
                    postingPageScheduler.setLinkInBioDetails(
                            linkInBioInternalService.removeDeletedLinkInBio(JSONUtils
                                    .collectionFromJSON(metadata.getLinkInBioDetails(), LinkInBioPostMetaData.class), businessId));
                }
                break;
            case FACEBOOK:
                postingPageScheduler.setFbPostMetadata(JSONUtils.fromJSON(metadata.getFbPostMetadata(), FbPostMetadata.class));
            case LINKEDIN:
                postingPageScheduler.setLinkedinPostMetaData(metadata.getLinkedinPostMetaDataObj());
                break;
            case TIKTOK:
                postingPageScheduler.setTiktokPostMetaData(JSONUtils.fromJSON(metadata.getTiktokPostMetaData(), TiktokPostMetadataRequest.class));
                break;
            default:
                break;
        }
    }


    private List<String> getEligiblePageIDs(Integer sourceId, List<Integer> businessIds, List<String> pageIDs) {
        Set<String> currentUserPageIds = socialPostService.getPageIdsFromSourceAndBusinessId(sourceId,businessIds);
        if(CollectionUtils.isEmpty(currentUserPageIds)) {
            return new ArrayList<>();
        }
        Set<String> pageIdSet = new HashSet<>(pageIDs);

        pageIdSet.retainAll(currentUserPageIds);
        return new ArrayList<>(pageIdSet);
    }

    private PostLibEditPostResponse getPostLibObject(PostLib postLib, Map<Integer, SocialPostsAssets> postAssetsMap,
                                                     Long businessNumber, List<Integer> businessIds, PostLibInfo postLibInfo, Integer businessId) throws Exception {
        List<String> eligiblePageIds = getEligiblePageIDs(postLibInfo.getSourceId(),businessIds, postLibInfo.getPageIds());
        if(CollectionUtils.isEmpty(eligiblePageIds)) {
            return  null;
        }
        PostLibEditPostResponse postResponse = new PostLibEditPostResponse();
        postResponse.setId(postLib.getId());
        postResponse.setPostText(postLib.getPostText());
        postResponse.setPublishDate(new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(postLib.getCreatedDate()));
        postResponse.setLinkPreviewUrl(postLib.getLinkPreviewUrl());

        if (StringUtils.isNotEmpty(postLib.getImageIds())) {
            List<SocialPostsAssets> imageAssets = socialPostService.getPostsAssetsById(postAssetsMap, postLib.getImageIds());
            postResponse.setImages(socialPostService.getMediaDataV2(imageAssets, businessNumber, Constants.IMAGE));
        }
        if (Objects.nonNull(postLib.getCompressedImageIds())) {
            List<SocialPostsAssets> compressedImageAssets = socialPostService.getPostsAssetsById(postAssetsMap, postLib.getCompressedImageIds());
            postResponse.setCompressedImages(socialPostService.getMediaRequestV2(compressedImageAssets, businessNumber, Constants.IMAGE));
        }
        if (StringUtils.isNotEmpty(postLib.getVideoIds())) {
            List<SocialPostsAssets> videoAssets = socialPostService.getPostsAssetsById(postAssetsMap, postLib.getVideoIds());
            PostAssetsData postAssetsData = socialPostService.setSocialPostsAssetsDataV2(videoAssets, businessNumber, Constants.VIDEO);
            postResponse.setVideos(postAssetsData.getVideos());
            postResponse.setVideoThumbnails(postAssetsData.getVideoThumbnailUrls());
        }
        if (Objects.nonNull(postLib.getPostMetadata())) {
            postResponse.setMediaSequence(socialPostService.getMediaSequenceV2(postLib, businessNumber));
            SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(postLib.getPostMetadata(), SocialPostSchedulerMetadata.class);
            if (Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getVideoThumbnailMetadata())) {
                postResponse.setVideoThumbnailMetadata(metadata.getVideoThumbnailMetadata());
            }
        }
        if (Objects.nonNull(postLib.getMentions())) {
            postResponse.setMentions(JSONUtils.collectionFromJSON(postLib.getMentions(), MentionData.class));
        }
        postResponse.setAiPost((Objects.isNull(postLib.getAiPost()) || postLib.getAiPost()==0)?false:true);
        setPostingSiteData(postResponse, postLib, postLibInfo, businessId, eligiblePageIds);
        return postResponse;
    }


    @Override
    public MasterPostLibEditPostResponse getPostLibData(BusinessIdRequest businessIdRequest, Integer id, Long businessNumber) {
        MasterPostLibEditPostResponse masterPostLibEditPostResponse = new MasterPostLibEditPostResponse();
        List<PostLibEditPostResponse> postLibEditPostResponseList = new ArrayList<>();
        try {
            PostLibMaster postLibMaster = postLibMasterRepo.findById(id);
            if (Objects.nonNull(postLibMaster)) {
                masterPostLibEditPostResponse.setAiPost((Objects.isNull(postLibMaster.getAiPost()) || postLibMaster.getAiPost()==0)?false:true);
                Long masterPostIdLong = Long.valueOf(id);
                Future<Map<Long, List<SocialTagBasicDetail>>> postLibIdToTagDetailsMapFuture = executor.submit(() ->
                        socialTagService.getEntityIdToBasicTagDetailListMap(Collections.singletonList(masterPostIdLong), SocialTagEntityType.POST_LIB));
                Map<Integer, List<PostLib>> masterLibVsPostLibMap = findMasterLibVSPostLibData(Arrays.asList(postLibMaster.getId()));
                List<PostLib> postLibList = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(masterLibVsPostLibMap.get(postLibMaster.getId()))) {
                    postLibList = masterLibVsPostLibMap.get(postLibMaster.getId());
                }
                List<PostLib> finalPostLibList = postLibList;
                CompletableFuture<Map<Integer, SocialPostsAssets>> getPostsAssetsDetail =
                        CompletableFuture.supplyAsync(() -> socialPostsAssetsService.getPostAssetsForPostLib(postLibMaster, finalPostLibList));
                CompletableFuture<Void> parallelExecuter = CompletableFuture.allOf(getPostsAssetsDetail);
                parallelExecuter.get(100, TimeUnit.SECONDS);
                Map<Integer, SocialPostsAssets> postAssetsMap = getPostsAssetsDetail.get();

                List<SocialTagBasicDetail> tagBasicDetails = postLibIdToTagDetailsMapFuture.get().get(masterPostIdLong);
                masterPostLibEditPostResponse.setTags(tagBasicDetails);

                postLibEditPostResponseList.add(getMasterPostLibObject(postLibMaster, postAssetsMap, businessNumber));
                Map<Integer, PostLibInfo> postLibInfoMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(postLibList)) {
                    List<Integer> postLibIds = postLibList.stream().map(PostLib::getId).collect(Collectors.toList());
                    List<PostLibInfo> postLibInfoList = postLibInfoRepo.findByPostLibIdIn(postLibIds);
                    postLibInfoMap = postLibInfoList.stream().collect(Collectors.toMap(PostLibInfo::getPostLibId, postLibInfo -> postLibInfo));
                }
                Map<Integer, PostLibInfo> postLibInfoMapFinal = postLibInfoMap;
                postLibList.forEach(postLib -> {
                    PostLibEditPostResponse postResponse;
                    try {
                        postResponse = getPostLibObject(postLib, postAssetsMap, businessNumber,
                                businessIdRequest.getBusinessIds(), postLibInfoMapFinal.get(postLib.getId()),postLibMaster.getEnterpriseId());
                    } catch (Exception e) {
                        LOGGER.info("Exception occurred while get post lib ",e);
                        throw new SocialBirdeyeException(ErrorCodes.UNKNOWN_ERROR_OCCURRED,Constants.GENERIC_EXCEPTION_MESSAGE);
                    }
                    if(Objects.nonNull(postResponse)) {
                        postLibEditPostResponseList.add(postResponse);
                    }
                });
                masterPostLibEditPostResponse.setUsageCount(postLibMaster.getUsageCount());
                masterPostLibEditPostResponse.setShowEditBanner(Objects.nonNull(postLibMaster.getSocialMasterPostId()));
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // Preserve the interrupt status
            throw new SocialBirdeyeException(ErrorCodes.UNKNOWN_ERROR_OCCURRED, Constants.GENERIC_EXCEPTION_MESSAGE);
        } catch (Exception e) {
            LOGGER.info("Exception occurred while get post lib ",e);
            throw new SocialBirdeyeException(ErrorCodes.UNKNOWN_ERROR_OCCURRED,Constants.GENERIC_EXCEPTION_MESSAGE);
        }
        masterPostLibEditPostResponse.setPostLibEditPostResponseList(postLibEditPostResponseList);
        masterPostLibEditPostResponse.setMasterPostId(id);
        return masterPostLibEditPostResponse;
    }

    @Override
    public void deletePostLib(Integer id, Integer businessId) {
        PostLibMaster postLibMaster = postLibMasterRepo.findById(id);
        if(Objects.nonNull(postLibMaster)) {
            if(!Objects.equals(businessId,postLibMaster.getEnterpriseId())) {
                LOGGER.info("Not a valid request, businessId provided: {}",businessId);
                return;
            }
            postLibMasterRepo.delete(postLibMaster);
            LOGGER.info("Delete post executed for id: {}",id);
        } else {
            LOGGER.info("No post lib found for id: {}",id);
        }
    }

    private List<Integer> getTaggedPostIds(List<Integer> tagIds) {
        List<Integer> taggedPostsIds = new ArrayList<>();
        List<Long> longTagIds = tagIds.stream()
                .map(Integer::longValue)
                .collect(Collectors.toList());
        List<Long> taggedPostIdsLong = socialTagService.findEntityIdByEntityTypeAndTagIdIn(longTagIds,SocialTagEntityType.POST_LIB);
        if(CollectionUtils.isNotEmpty(taggedPostIdsLong)) {
            taggedPostsIds = taggedPostIdsLong.stream()
                    .map(Long::intValue)
                    .collect(Collectors.toList());
        }
        return taggedPostsIds;
    }

    private List<Integer> getIdsFromListOfChannels(List<String> socialChannels) {
        List<Integer> sourceIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(socialChannels)){
            if (socialChannels.contains("facebook")) {
                sourceIds.add(SocialChannel.FACEBOOK.getId());
            }
            if (socialChannels.contains("google")) {
                sourceIds.add(SocialChannel.GOOGLE.getId());
            }
            if (socialChannels.contains("twitter")) {
                sourceIds.add(SocialChannel.TWITTER.getId());
            }
            if (socialChannels.contains("instagram")) {
                sourceIds.add(SocialChannel.INSTAGRAM.getId());
            }
            if (socialChannels.contains("linkedin")) {
                sourceIds.add(SocialChannel.LINKEDIN.getId());
            }
            if (socialChannels.contains("youtube")) {
                sourceIds.add(SocialChannel.YOUTUBE.getId());
            }
            if(socialChannels.contains(SocialChannel.TIKTOK.getName())) {
                sourceIds.add(SocialChannel.TIKTOK.getId());
            }
        }
        return sourceIds;
    }

    private void setMediaInfo(PostLibMaster postLibMaster, PostLibResponse postLibResponse, Map<Integer, SocialPostsAssets> postAssetsMap, Long businessNumber) {
        if (StringUtils.isNotEmpty(postLibMaster.getImageIds())) {
            List<SocialPostsAssets> imageAssets = socialPostService.getPostsAssetsById(postAssetsMap, postLibMaster.getImageIds());
            postLibResponse.setImageUrlsMetaData(socialPostService.getMediaDataV2(imageAssets, businessNumber, Constants.IMAGE));
            postLibResponse.setImageUrls(postLibResponse.getImageUrlsMetaData().stream().map(MediaData::getMediaUrl).collect(Collectors.toList()));
        }
        if (Objects.nonNull(postLibMaster.getCompressedImageIds())) {
            List<SocialPostsAssets> compressedImageAssets = socialPostService.getPostsAssetsById(postAssetsMap, postLibMaster.getCompressedImageIds());
            postLibResponse.setCompressedImages(socialPostService.getMediaRequestV2(compressedImageAssets, businessNumber, Constants.IMAGE));
        }
        if (StringUtils.isNotEmpty(postLibMaster.getVideoIds())) {
            List<SocialPostsAssets> videoAssets = socialPostService.getPostsAssetsById(postAssetsMap, postLibMaster.getVideoIds());
            PostAssetsData postAssetsData = socialPostService.setSocialPostsAssetsDataV2(videoAssets, businessNumber, Constants.VIDEO);
            postLibResponse.setVideoUrlsMetaData(postAssetsData.getVideos());
            postLibResponse.setVideoThumbnails(postAssetsData.getVideoThumbnailUrls());
            postLibResponse.setVideoUrls(postLibResponse.getVideoUrlsMetaData().stream().map(MediaData::getMediaUrl).collect(Collectors.toList()));
        }
    }

    private void setPostSites(PostLibMaster postLibMaster, PostLibResponse postLibResponse) {
        List<String> postingSites = new ArrayList<>();
        List<PostLib> postLibList = postLibMaster.getPostLib();
        postLibList.forEach(postLib -> {
            String channel = SocialChannel.getSocialChannelNameById(postLib.getSourceId());
            postingSites.add(channel);
        });
        postLibResponse.setPostingSites(postingSites);
    }
    private List<PostLibResponse> getPostLibResponse(List<PostLibMaster> postLibMasterList, Map<Integer, BusinessCoreUser> userIdVsUser, Map<Integer, SocialPostsAssets> postAssetsMap,
                                                     Long businessNumber, Map<Long, List<SocialTagBasicDetail>> masterPostIdToTagDetailsMap){
        List<PostLibResponse> responseList = new ArrayList<>();
        List<SocialChannel> socialChannelsForAiBusiness = socialPostAiService.getSocialChannelsByEnterpriseId(businessNumber);
        postLibMasterList.forEach(postLibMaster -> {
            PostLibResponse postLibResponse = new PostLibResponse();
            postLibResponse.setId(postLibMaster.getId());
            postLibResponse.setAiPost(!Objects.isNull(postLibMaster.getAiPost()) && postLibMaster.getAiPost() != 0);
            postLibResponse.setPostText(postLibMaster.getPostText());
            postLibResponse.setCreatedDate(new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(postLibMaster.getCreatedDate()));
            postLibResponse.setEditedDate(getDate(postLibMaster));
            postLibResponse.setLastUsedDate(getDate(postLibMaster));
            postLibResponse.setTotalEngagements(postLibMaster.getTotalEngagements());
            postLibResponse.setTotalImpressions(postLibMaster.getTotalImpressions());
            postLibResponse.setUsageCount(postLibMaster.getUsageCount());
            postLibResponse.setLinkPreviewUrl(postLibMaster.getLinkPreviewUrl());
            postLibResponse.setInsightData(postLibMaster.getInsightMetadata());
            if(Objects.nonNull(postLibMaster.getSocialMasterPostId())
                    || (Objects.nonNull(postLibMaster.getUsageCount()) && postLibMaster.getUsageCount()>0)) {
                postLibResponse.setShowInsight(true);
            }
            setMediaInfo(postLibMaster, postLibResponse, postAssetsMap, businessNumber);
            updateMetaData(postLibMaster,postLibResponse,businessNumber);
            postLibResponse.setTags(masterPostIdToTagDetailsMap.get(Long.valueOf(postLibMaster.getId())));
            if(Objects.nonNull(postLibMaster.getAiSuggested()) && postLibMaster.getAiSuggested()==1) {
                postLibResponse.setAiSuggestion(true);
                postLibResponse.setPostHeader(postLibMaster.getPostHeader());
                postLibResponse.setCreatedBy(updateNameForUserId(socialPostAiService.getAiUserId(),userIdVsUser));
                //  postLibResponse.setCreatedBy(getFullUsername(userIdVsUser.get(socialPostAiService.getAiUserId())));
                postLibResponse.setEditedBy(updateNameForUserId(socialPostAiService.getAiUserId(),userIdVsUser));
                postLibResponse.setCreatedById(socialPostAiService.getAiUserId());
                postLibResponse.setEditedById(socialPostAiService.getAiUserId());
                postLibResponse.setPostingSites(CollectionUtils.isEmpty(socialChannelsForAiBusiness)
                        ?null:socialChannelsForAiBusiness.stream().map(SocialChannel::getName).collect(Collectors.toList()));
            }else {
                postLibResponse.setAiSuggestion(false);
                postLibResponse.setCreatedBy(updateNameForUserId(postLibMaster.getCreatedBy(),userIdVsUser));
                // postLibResponse.setCreatedBy(getFullUsername(userIdVsUser.get(postLibMaster.getCreatedBy())));
                postLibResponse.setEditedBy(updateNameForUserId(postLibMaster.getEditedBy(),userIdVsUser));
                postLibResponse.setCreatedById(postLibMaster.getCreatedBy());
                postLibResponse.setEditedById(postLibMaster.getEditedBy());
                setPostSites(postLibMaster,postLibResponse);
            }
            responseList.add(postLibResponse);
        });
        return responseList;
    }
    private String updateNameForUserId(Integer userId, Map<Integer, BusinessCoreUser> userIdVsUser) {
        if (Objects.nonNull(userId) && Objects.nonNull(userIdVsUser.get(userId))) {
            return getFullUsername(userIdVsUser.get(userId));
        }
        return null;
    }

    private void updateMetaData(PostLibMaster postLibMaster, PostLibResponse postLibResponse,Long businessNumber, Map<Integer, SocialPostsAssets> postAssetsMap) {
        if (Objects.nonNull(postLibMaster) && Objects.nonNull(postLibMaster.getPostMetadata())) {
            try {
                List<String> mediaUrls = new ArrayList<>();
                if(CollectionUtils.isNotEmpty(postLibResponse.getImageUrls())) {
                    mediaUrls.addAll(postLibResponse.getImageUrls());
                }
                if(CollectionUtils.isNotEmpty(postLibResponse.getVideoUrls())) {
                    mediaUrls.addAll(postLibResponse.getVideoUrls());
                }
                postLibResponse.setMediaSequence(mediaUrls);
            } catch (Exception e) {
                throw new SocialBirdeyeException(ErrorCodes.UNKNOWN_ERROR_OCCURRED,e.getMessage());
            }
            SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(postLibMaster.getPostMetadata(), SocialPostSchedulerMetadata.class);
            if (Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getIgPostMetadata())) {
                IgPostMetadata igPostMetadata = JSONUtils.fromJSON(metadata.getIgPostMetadata(), IgPostMetadata.class);
                postLibResponse.setType(Objects.nonNull(igPostMetadata) ? igPostMetadata.getType() : null);
            }
        }
    }
    private void updateMetaData(PostLibMaster postLibMaster, PostLibResponse postLibResponse,Long businessNumber) {
        if (Objects.nonNull(postLibMaster) && Objects.nonNull(postLibMaster.getPostMetadata())) {
            try {
                postLibResponse.setMediaSequence(socialPostService.getMediaSequenceV2(postLibMaster, businessNumber));
            } catch (Exception e) {
                throw new SocialBirdeyeException(ErrorCodes.UNKNOWN_ERROR_OCCURRED,e.getMessage());
            }
            SocialPostSchedulerMetadata metadata = JSONUtils.fromJSON(postLibMaster.getPostMetadata(), SocialPostSchedulerMetadata.class);
            if (Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getIgPostMetadata())) {
                IgPostMetadata igPostMetadata = JSONUtils.fromJSON(metadata.getIgPostMetadata(), IgPostMetadata.class);
                postLibResponse.setType(Objects.nonNull(igPostMetadata) ? igPostMetadata.getType() : null);
            }

            if (Objects.nonNull(metadata) && StringUtils.isNotEmpty(metadata.getFbPostMetadata())) {
                FbPostMetadata fbPostMetadata = JSONUtils.fromJSON(metadata.getFbPostMetadata(), FbPostMetadata.class);
                postLibResponse.setType(Objects.nonNull(fbPostMetadata) ? fbPostMetadata.getType() : null);
            }
        }
    }
    private static String getDate(PostLibMaster postLibMaster) {
        if(Objects.isNull(postLibMaster.getLastEditedAt())){
            return null;
        }
        return new SimpleDateFormat(SCHEDULE_DATE_FORMAT).format(postLibMaster.getLastEditedAt());
    }

    private Map<Integer, SocialPostsAssets> getPostAssets(List<PostLibMaster> postLibMasterList) {
        Map<Integer, SocialPostsAssets> responseMap = new HashMap<>();
        List<Integer> ids = new ArrayList<>();
        for(PostLibMaster masterPost: postLibMasterList) {
            ids.addAll(convertStringToIntegerList(masterPost.getImageIds()));
            ids.addAll(convertStringToIntegerList(masterPost.getVideoIds()));
            ids.addAll(convertStringToIntegerList(masterPost.getCompressedImageIds()));
        }
        if(CollectionUtils.isEmpty(ids)) {
            return responseMap;
        }
        List<SocialPostsAssets> socialPostsAssetsList = socialPostsAssetsRepository.findByIds(new HashSet<>(ids));
        if(CollectionUtils.isNotEmpty(socialPostsAssetsList)) {
            responseMap = socialPostsAssetsList.stream().collect(Collectors.toMap(SocialPostsAssets::getId,Function.identity()));
        }
        return responseMap;
    }

    private List<Integer> convertStringToIntegerList(String ids) {
        if(StringUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        return Stream.of(ids.split(",")).map(Integer::parseInt).collect(Collectors.toList());
    }

    private void checkUntaggedPosts(Integer enterpriseId, List<Integer> taggedPostIds, List<Integer> tagIds) {
        if(CollectionUtils.isNotEmpty(tagIds) && tagIds.contains(-1)) {
            List<Integer> untaggedPostIds = postLibMasterRepo.findAllIdsByEnterpriseIdAndIsTagged(enterpriseId);
            if(CollectionUtils.isNotEmpty(untaggedPostIds)) {
                taggedPostIds.addAll(untaggedPostIds);
            }
        }
    }

    private Sort getSortingOrder(PostLibFilter filter) {
        Sort.Direction sortDirection = filter.getOrder().equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        List<String> sortingList = new ArrayList<>();
        if("totalEngagements".equalsIgnoreCase(filter.getSortBy())) {
            sortingList.add(filter.getSortBy());
            sortingList.add("usageCount");
        } else if("usageCount".equalsIgnoreCase(filter.getSortBy())) {
            sortingList.add(filter.getSortBy());
            sortingList.add("totalEngagements");
        }
        sortingList.add("lastEditedAt");
        return new Sort(sortDirection,sortingList);
    }

    @Override
    public PostLibListResponse getPostLibList(PostLibFilter filter, Integer enterpriseId, String timezone, Long businessNumber) {
        return getPostLibListResponseWithAI(filter, enterpriseId, businessNumber);
    }
    @NotNull
    private PostLibListResponse getPostLibListResponseWithAI(PostLibFilter filter, Integer enterpriseId, Long businessNumber) {
        PostLibListResponse postLibListResponse = new PostLibListResponse();
        try {
            Future<Map<Long, List<SocialTagBasicDetail>>> masterPostIdToTagDetailsMapFuture;
            List<Integer> socialChannels = getIdsFromListOfChannels(filter.getSocialChannels());
            Sort finalSort = getSortingOrder(filter);
            Pageable pageable = new PageRequest(filter.getPageNumber(), filter.getPageSize(), finalSort);
            List<Integer> taggedPostIds = null;
            if (CollectionUtils.isNotEmpty(filter.getTags())) {
                taggedPostIds = getTaggedPostIds(filter.getTags());
                checkUntaggedPosts(enterpriseId,taggedPostIds,filter.getTags());
                if(CollectionUtils.isEmpty(taggedPostIds)) {
                    postLibListResponse.setTotalCount(0);
                    return postLibListResponse;
                }
            }
            Date startDate = null;
            Date endDate = null;
            if (Objects.nonNull(filter.getStartTime()) && Objects.nonNull(filter.getEndTime())) {
                startDate = new Date(filter.getStartTime());
                endDate = new Date(filter.getEndTime());
            }
            Specification<PostLibMaster> specification = PostLibSpecification.getMasterPostLib(filter, taggedPostIds, enterpriseId,
                    startDate, endDate, socialChannels);
            Page<PostLibMaster> postLibMasterPageData = postLibMasterRepo.findAll(specification, pageable);
            List<PostLibMaster> postLibMasterList = postLibMasterPageData.getContent();
            LOGGER.info("Posts fetched: {}",postLibMasterList.size());

            if (CollectionUtils.isNotEmpty(postLibMasterList)) {
                List<Integer> userIds = new ArrayList<>();
                postLibMasterList.forEach(postLibMaster -> {
                    userIds.add(postLibMaster.getCreatedBy());
                    if(Objects.nonNull(postLibMaster.getEditedBy())) {
                        userIds.add(postLibMaster.getEditedBy());
                    }
                });

                List<Integer> masterPostIds = postLibMasterList.stream().map(PostLibMaster::getId).distinct().collect(Collectors.toList());
                List<Long> masterPostIdsLong = masterPostIds.stream().map(Long::valueOf).collect(Collectors.toList());
                masterPostIdToTagDetailsMapFuture =
                        executor.submit(() -> socialTagService.getEntityIdToBasicTagDetailListMap(masterPostIdsLong, SocialTagEntityType.POST_LIB));
                Map<Long, List<SocialTagBasicDetail>> masterPostIdToTagDetailsMap = masterPostIdToTagDetailsMapFuture.get();
                CompletableFuture<Map<Integer, BusinessCoreUser>> getUsersFromCore = CompletableFuture.supplyAsync(() -> businessCoreService.getBusinessUserForUserId(userIds));
                CompletableFuture<Map<Integer, SocialPostsAssets>> getPostsAssetsDetail = CompletableFuture.supplyAsync(() -> getPostAssets(postLibMasterList));

                CompletableFuture<Void> parallelExecuter = CompletableFuture.allOf(getUsersFromCore, getPostsAssetsDetail);
                parallelExecuter.get(100, TimeUnit.SECONDS);

                Map<Integer, BusinessCoreUser> userIdVsUser = getUsersFromCore.get();
                Map<Integer, SocialPostsAssets> postAssetsMap = getPostsAssetsDetail.get();
                LOGGER.info("Starting processing for fetched posts");

                List<PostLibResponse> postLibResponseList = getPostLibResponse(postLibMasterList,userIdVsUser,postAssetsMap,businessNumber,masterPostIdToTagDetailsMap);
                postLibListResponse.setData(postLibResponseList);
                postLibListResponse.setTotalCount((int) postLibMasterPageData.getTotalElements());
                LOGGER.info("Post lib list response, total entries: {}",postLibListResponse.getTotalCount());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // Preserve the interrupt status
            throw new SocialBirdeyeException(ErrorCodes.UNKNOWN_ERROR_OCCURRED, Constants.GENERIC_EXCEPTION_MESSAGE);
        } catch (Exception e) {
            LOGGER.info("Exception occurred while fetching post lib ",e);
            throw new SocialBirdeyeException(ErrorCodes.UNKNOWN_ERROR_OCCURRED,Constants.GENERIC_EXCEPTION_MESSAGE);
        }
        return postLibListResponse;
    }

    private void addAIPostGenerationInfo(Integer enterpriseId, Long businessNumber, PostLibListResponse postLibListResponse) {
        List<SocialBusinessProperty> socialBusinessPropertyList = socialBusinessPropertyRepo.findByEnterpriseId(businessNumber);
        if (CollectionUtils.isEmpty(socialBusinessPropertyList)) {
            LOGGER.error("No SocialBusinessProperty entry found for the given enterprise number: {}", businessNumber);
            throw new BirdeyeSocialException(ErrorCodes.INTERNAL_SERVER_ERROR, "internal server error");
        }
        SocialBusinessProperty socialBusinessProperty = socialBusinessPropertyList.get(0);
        if(Objects.nonNull(socialBusinessProperty.getCurrentScanDate()) && Objects.nonNull(socialBusinessProperty.getNextScanDate())){
            postLibListResponse.setAiPostsGenerated(true);
        }
        BusinessProfileResponse businessProfileResponse = businessCoreService.getBusinessProfile(enterpriseId);
        AiPostConfigDetailResponse aiPostConfigDetailResponse = socialPostAiService.getBusinessAiConfig(enterpriseId, businessNumber, null);

        if (Objects.nonNull(businessProfileResponse) && ((Objects.nonNull(businessProfileResponse.getCategory()) && !Constants.OTHER.equals(businessProfileResponse.getCategory())) ||
                (CollectionUtils.isNotEmpty(businessProfileResponse.getSubCategories()) && (businessProfileResponse.getSubCategories().size() > 1 || !Constants.OTHER.equals(businessProfileResponse.getSubCategories().get(0)))) ||
                (CollectionUtils.isNotEmpty(businessProfileResponse.getKeywords()) && (businessProfileResponse.getKeywords().size() > 1 || !Constants.OTHER.equals(businessProfileResponse.getKeywords().get(0)))) ||
                (CollectionUtils.isNotEmpty(businessProfileResponse.getServices()) && (businessProfileResponse.getServices().size() > 1 || !Constants.OTHER.equals(businessProfileResponse.getServices().get(0)))) ||
                (CollectionUtils.isNotEmpty(businessProfileResponse.getProducts())  && (businessProfileResponse.getProducts().size() > 1 || !Constants.OTHER.equals(businessProfileResponse.getProducts().get(0)))))) {
            postLibListResponse.setBusinessCategoryExists(true);
        }
        if(Objects.nonNull(aiPostConfigDetailResponse) && CollectionUtils.isNotEmpty(aiPostConfigDetailResponse.getCategories()) && aiPostConfigDetailResponse.getCategories().size()==3){
            postLibListResponse.setAllCategoriesSelected(true);
        }
    }

    private int nullSafeValue(Integer value) {
        return value == null ? Integer.MIN_VALUE : value;
    }

    private Date parseDate(String dateStr) {
        if (dateStr == null) return null;
        try {
            return new SimpleDateFormat(SCHEDULE_DATE_FORMAT).parse(dateStr);
        } catch (ParseException e) {
            System.err.println("Date parsing failed: " + e.getMessage());
            return null;
        }
    }
    private boolean filterChannels(List<PostLib> postLibList, List<Integer> socialChannels) {
        if(CollectionUtils.isEmpty(socialChannels)) return true;
        if(CollectionUtils.isEmpty(postLibList)) return false;

        List<Integer> postLibSourceIds = postLibList.stream().map(PostLib::getSourceId).collect(Collectors.toList());
        for(Integer sourceId: socialChannels) {
            if(postLibSourceIds.contains(sourceId)) return true;
        }
        return false;
    }

    @Override
    public void updatePostInsights(PostInsightsData postInsightsData) {
        PostLibMaster postLibMaster = postLibMasterRepo.findBySocialMasterPostId(postInsightsData.getMasterPostId());
        if(Objects.isNull(postLibMaster)){
            LOGGER.info("No post lib found for request : {}",postInsightsData);
            return;
        }
        LOGGER.info("Post lib post insights update for id : {}",postLibMaster.getId());
        PostLibInsightsMetaData postLibInsightsMetaData = postLibMaster.getInsightMetadata();
        if(Objects.isNull(postLibInsightsMetaData)) postLibInsightsMetaData = new PostLibInsightsMetaData();
        prepareInsightsData(postLibInsightsMetaData,postInsightsData);
        postLibMaster.setInsightMetadata(postLibInsightsMetaData);
        postLibMaster.setTotalImpressions(sumImpressions(postLibInsightsMetaData));
        postLibMaster.setTotalEngagements(sumEngagement(postLibInsightsMetaData));
        postLibMasterRepo.saveAndFlush(postLibMaster);
    }

    @Override
    public void updateUsageCount(Integer postLibId) {
        postLibMasterRepo.increaseUsageCount(postLibId);
    }

    @Override
    public void bulkCreatePostLib(BulkUploadPostLibRequest request) {
        if(CollectionUtils.isEmpty(request.getLibPostsList())){
            LOGGER.info("No data found for the request : {}",request);
            return;
        }
        request.getLibPostsList().forEach(postInfoRequest -> {
            postInfoRequest.setDraft(false);
            kafkaProducerService.sendObjectV1(KafkaTopicEnum.POST_BULK_UPLOAD.getName(), postInfoRequest);
        });
    }

    private Integer sumEngagement(PostLibInsightsMetaData postLibInsightsMetaData) {
        return (Objects.nonNull(postLibInsightsMetaData.getFacebook()) ? postLibInsightsMetaData.getFacebook().getEngagements() : 0)
                +(Objects.nonNull(postLibInsightsMetaData.getLinkedin()) ? postLibInsightsMetaData.getLinkedin().getEngagements() : 0)
                +(Objects.nonNull(postLibInsightsMetaData.getInstagram()) ? postLibInsightsMetaData.getInstagram().getEngagements() : 0)
                +(Objects.nonNull(postLibInsightsMetaData.getTwitter()) ? postLibInsightsMetaData.getTwitter().getEngagements() : 0);
    }

    private Integer sumImpressions(PostLibInsightsMetaData postLibInsightsMetaData) {
        return (Objects.nonNull(postLibInsightsMetaData.getFacebook()) ? postLibInsightsMetaData.getFacebook().getImpressions() : 0)
                +(Objects.nonNull(postLibInsightsMetaData.getLinkedin()) ? postLibInsightsMetaData.getLinkedin().getImpressions() : 0)
                +(Objects.nonNull(postLibInsightsMetaData.getInstagram()) ? postLibInsightsMetaData.getInstagram().getImpressions() : 0)
                +(Objects.nonNull(postLibInsightsMetaData.getTwitter()) ? postLibInsightsMetaData.getTwitter().getImpressions() : 0);
    }

    private void prepareInsightsData(PostLibInsightsMetaData postLibInsightsMetaData, PostInsightsData postInsightsData) {
        SocialChannel socialChannel = SocialChannel.getSocialChannelById(postInsightsData.getSourceId());
        if(Objects.isNull(socialChannel)){
            LOGGER.info("No social channel found for is : {}",postInsightsData.getSourceId());
            return;
        }
        postInsightsData.setImpression(Objects.nonNull(postInsightsData.getImpression())? postInsightsData.getImpression() : 0);
        postInsightsData.setEngagement(Objects.nonNull(postInsightsData.getEngagement())? postInsightsData.getEngagement() : 0);
        LOGGER.info("Update insights for channel : {} and post id : {}",socialChannel,postInsightsData.getMasterPostId());
        PostInsights facebook = postLibInsightsMetaData.getFacebook();
        PostInsights linkedIn = postLibInsightsMetaData.getLinkedin();
        PostInsights twitter = postLibInsightsMetaData.getTwitter();
        PostInsights instagram = postLibInsightsMetaData.getInstagram();
        switch (socialChannel){
            case FACEBOOK:
                if(Objects.nonNull(facebook))
                    postLibInsightsMetaData.setFacebook(new PostInsights(facebook.getImpressions() + postInsightsData.getImpression()
                            ,facebook.getEngagements() + postInsightsData.getEngagement()));
                break;
            case INSTAGRAM:
                if(Objects.nonNull(instagram))
                    postLibInsightsMetaData.setInstagram(new PostInsights(instagram.getImpressions() + postInsightsData.getImpression()
                            ,instagram.getEngagements() + postInsightsData.getEngagement()));
                break;
            case LINKEDIN:
                if(Objects.nonNull(linkedIn))
                    postLibInsightsMetaData.setLinkedin(new PostInsights(linkedIn.getImpressions() + postInsightsData.getImpression()
                            ,linkedIn.getEngagements() + postInsightsData.getEngagement()));
                break;
            case TWITTER:
                if(Objects.nonNull(twitter))
                    postLibInsightsMetaData.setTwitter(new PostInsights(twitter.getImpressions() + postInsightsData.getImpression()
                            ,twitter.getEngagements() + postInsightsData.getEngagement()));
                break;
            default:
                LOGGER.info("Channel not found :{}",socialChannel);
                break;
        }
    }

    @Override
    public void updateEditorInfo(SocialTagEntityMappingActionEvent tagUpdateEvent) {
        //No processing needed. Logic moved to tag service to make call sync
//        if(Objects.nonNull(tagUpdateEvent) && BooleanUtils.isTrue(tagUpdateEvent.getIsFreshRequest()) && Objects.nonNull(tagUpdateEvent.getEntityId()) && Objects.nonNull(tagUpdateEvent.getBy())) {
//            PostLibMaster postLibMaster = postLibMasterRepo.findById(Math.toIntExact(tagUpdateEvent.getEntityId()));
//            if (Objects.nonNull(postLibMaster)) {
//                postLibMaster.setLastEditedAt(new Date());
//                postLibMaster.setEditedBy(Math.toIntExact(tagUpdateEvent.getBy()));
//                postLibMasterRepo.saveAndFlush(postLibMaster);
//            }
//        }
    }

    private Map<Integer, List<PostLib>> findMasterLibVSPostLibData(List<Integer> postMasterLibIds) {
        Map<Integer, List<PostLib>> masterLibIdVsPostLib = new HashMap<>();
        if(CollectionUtils.isEmpty(postMasterLibIds)) {
            return masterLibIdVsPostLib;
        }
        List<PostLib> postLibList = new ArrayList<>();
        int pageNo = 0;
        int pageSize = 1000;
        while(true) {
            Page<PostLib> postLibs = postLibRepo.findByMasterPostIdIn(postMasterLibIds, new PageRequest(pageNo, pageSize));
            if(CollectionUtils.isEmpty(postLibs.getContent())) break;
            pageNo++;
            postLibList.addAll(postLibs.getContent());
        }


        masterLibIdVsPostLib = postLibList.stream().collect(Collectors.groupingBy(PostLib::getMasterPostId));
        return masterLibIdVsPostLib;
    }
}