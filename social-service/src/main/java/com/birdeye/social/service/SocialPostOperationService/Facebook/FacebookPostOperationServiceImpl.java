package com.birdeye.social.service.SocialPostOperationService.Facebook;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.constant.SocialPostStatusEnum;
import com.birdeye.social.dao.SocialFBPageRepository;
import com.birdeye.social.dao.SocialPostInfoRepository;
import com.birdeye.social.entities.BusinessFBPage;
import com.birdeye.social.entities.PermissionMapping;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.facebook.FacebookPostInfo;
import com.birdeye.social.service.IPermissionMappingService;
import com.birdeye.social.service.SocialPostFacebookService;
import com.birdeye.social.service.SocialPostOperationService.PostOperationUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.birdeye.social.constant.Constants.FB_BASE_URL;

@Service
public class FacebookPostOperationServiceImpl implements FacebookPostOperationService{

    @Autowired
    private SocialFBPageRepository socialFBPageRepository;

    @Autowired
    private SocialPostFacebookService socialPostFacebookService;

    @Autowired
    private IPermissionMappingService permissionMappingService;
    @Autowired
    private SocialPostInfoRepository publishInfoRepository;
    @Autowired
    private PostOperationUtil postOperationUtil;

    private static final String ERROR_CODE = "error_code";
    private static final String HTTP_RESPONSE = "http_response";

    private static final Logger LOGGER = LoggerFactory.getLogger(FacebookPostOperationServiceImpl.class);


    private void addErrorMessage(BirdeyeSocialException e, SocialPostPublishInfo publishInfo) {
        List<PermissionMapping> permissionMapping = new ArrayList<>();
        boolean check = false;
        boolean isErrorMessageFound = false;
        if (Objects.nonNull(e.getData().get(ERROR_CODE))) {
            if (Objects.nonNull(e.getData().get("error_sub_code"))) {
                permissionMapping = permissionMappingService.getDataByChannelandHttpResponseAndErrorCodeAndErrorSubCode(Constants.FACEBOOK, (Integer) e.getData().get(HTTP_RESPONSE), (Integer) e.getData().get(ERROR_CODE), (Integer) e.getData().get("error_sub_code"));
                check = true;
            }
            if (!check) {
                permissionMapping = permissionMappingService.getDataByChannelandHttpResponseAndErrorCode(Constants.FACEBOOK, (Integer) e.getData().get(HTTP_RESPONSE), (Integer) e.getData().get(ERROR_CODE));
            }
        } else {
            permissionMapping = permissionMappingService.getDataByChannelandHttpResponse(Constants.FACEBOOK, (Integer) e.getData().get(HTTP_RESPONSE));
        }
        if (permissionMapping.size() == 1) {
            publishInfo.setFailureReason(permissionMapping.get(0).getErrorMessage());
            publishInfo.setBucket(permissionMapping.get(0).getBucket());
            isErrorMessageFound = true;
        } else if (permissionMapping.size() > 1) {
            for (PermissionMapping permissionMappingList : permissionMapping) {
                if (e.getMessage().contains(permissionMappingList.geterrorActualMessage())) {
                    publishInfo.setFailureReason(permissionMappingList.getErrorMessage());
                    publishInfo.setBucket(permissionMappingList.getBucket());
                    isErrorMessageFound = true;
                    break;
                }
            }
        }
        if(!isErrorMessageFound){
            PermissionMapping permissionMappingUnknown = permissionMappingService.getDataByChannelAndPermissionCodeAndModule(Constants.FACEBOOK, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR,Constants.PUBLISH_MODULE);
            publishInfo.setFailureReason(permissionMappingUnknown.getErrorMessage());
            publishInfo.setBucket(permissionMappingUnknown.getBucket());
            LOGGER.info("New error found : {}", e.getMessage());
            LOGGER.info("Error code sub values :{}", e.getData());
        }

        LOGGER.error("FB post edit failed: {}", e.getMessage());
        publishInfo.setIsPublished(2);
    }

    @Override
    public void editPublishedPost(SocialPostPublishInfo publishInfo) throws Exception{
        List<BusinessFBPage> businessFBPages = socialFBPageRepository.findByFacebookPageIdAndIsValid(publishInfo.getExternalPageId(), 1);
        try {
            if(CollectionUtils.isEmpty(businessFBPages)) {
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, "Page not found");
            }
            // If postId is not in the format of pageId_postId, then convert it to that format
            String postId = publishInfo.getPostId();
            if(StringUtils.isEmpty(publishInfo.getSocialPost().getVideoIds()) &&!postId.contains(publishInfo.getExternalPageId()+"_")){
                postId = publishInfo.getExternalPageId() + "_" + postId;
            }
            LOGGER.info("Editing FB post with postId: {} and fb page id: {}", postId, publishInfo.getExternalPageId());
            FacebookPostInfo info = socialPostFacebookService.editFBPost(businessFBPages.get(0).getPageAccessToken(),postId,publishInfo.getSocialPost().getPostText());
            publishInfo.setIsPublished(SocialPostStatusEnum.PUBLISHED.getId());
            publishInfo.setPostUrl(FB_BASE_URL + publishInfo.getPostId());
            LOGGER.info("FB post edit {} is successful", info.getPostId());
        } catch (BirdeyeSocialException e) {
            addErrorMessage(e,publishInfo);
        } catch (Exception ex) {
            LOGGER.error("FB post edit failed: {}", ex.getMessage());
            PermissionMapping permissionMappingUnknown = permissionMappingService.getDataByChannelAndPermissionCodeAndModule(Constants.FACEBOOK, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR,Constants.PUBLISH_MODULE);
            publishInfo.setFailureReason(permissionMappingUnknown.getErrorMessage());
            publishInfo.setBucket(permissionMappingUnknown.getBucket());
            publishInfo.setIsPublished(SocialPostStatusEnum.FAILED.getId());
        }
        publishInfoRepository.saveAndFlush(publishInfo);
        postOperationUtil.markOriginalPost(publishInfo);
    }

    @Override
    public void deletePost(SocialPostPublishInfo publishedPost) throws Exception{
        List<BusinessFBPage> businessFBPages = socialFBPageRepository.findByFacebookPageIdAndIsValid(publishedPost.getExternalPageId(), 1);
        if(CollectionUtils.isEmpty(businessFBPages)) {
            throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_FETCH_PAGE_DETAILS, "Page not found");
        }
        try {
            BusinessFBPage businessFBPage = businessFBPages.get(0);
            boolean success = socialPostFacebookService.deletePostObject(businessFBPage.getPageAccessToken(), publishedPost.getPostId());
            if(!success) {
                throw new BirdeyeSocialException(ErrorCodes.UNABLE_TO_DELETE_FACEBOOK_OBJECT, "post delete failed on facebook");
            }
        } catch (BirdeyeSocialException e) {
            if(e.getCode() == ErrorCodes.CLIENT_ERROR_404.value()) {
                LOGGER.info("Seems like {} post with publish info id: {} is already deleted, marking it as deleted in the system", SocialChannel.FACEBOOK.getName(), publishedPost.getId());
            } else {
                throw e;
            }
        }
    }
}
