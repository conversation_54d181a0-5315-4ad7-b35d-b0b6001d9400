package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.businessgetpage.IBusinessGetPageService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dto.*;
import com.birdeye.social.dto.BusinessEntity;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.TwitterLiteDto;
import com.birdeye.social.dto.WebsiteDomainInfo;
import com.birdeye.social.entities.*;
import com.birdeye.social.entities.mediaupload.SocialAssetChunkInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadInfo;
import com.birdeye.social.entities.mediaupload.SocialMediaUploadRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.exception.SocialBirdeyeException;
import com.birdeye.social.exception.TooManyRequestException;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.exception.TooManyRequestException;
import com.birdeye.social.external.request.business.BusinessLiteRequest;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequest;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.external.service.SocialRawPageDetail;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.dao.BusinessRepository;
import com.birdeye.social.platform.dao.SessionTokenRepository;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.BusinessTwitterPage;
import com.birdeye.social.platform.entities.SessionToken;
import com.birdeye.social.response.TwitterMediaResponse;
import com.birdeye.social.response.TwitterMediaResponseData;
import com.birdeye.social.specification.XSpecification;
import com.birdeye.social.sro.*;
import com.birdeye.social.twitter.*;
import com.birdeye.social.twitter.TwitterData.FileType;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.SocialFileHandlingUtils;
import com.birdeye.social.utils.SocialProxyHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.domain.Specifications;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.social.ApiException;
import org.springframework.social.twitter.api.impl.TwitterTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.*;
import twitter4j.Twitter;
import twitter4j.TwitterException;
import twitter4j.TwitterFactory;
import twitter4j.auth.AccessToken;
import twitter4j.auth.RequestToken;
import twitter4j.conf.Configuration;
import twitter4j.conf.ConfigurationBuilder;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.birdeye.social.constant.Constants.*;
import static com.birdeye.social.constant.KafkaTopicEnum.SOCIAL_SYNC_BUSINESS_POSTS;
import static com.birdeye.social.twitter.TwitterConstants.POST;
import static com.birdeye.social.twitter.TwitterConstants.TWITTER_POST;
import static java.util.Comparator.nullsFirst;

@Service
@Primary
public class SocialPostTwitterServiceImpl extends SocialAccountSetupCommonService implements SocialPostTwitterService {
	
	private static final String CONNECT = "connect";
	
	private static final String RECONNECT = "reconnect";
	
	private static final String TWITTER_COM = "http://twitter.com/";

	private static final String STATUS = "/status/";

	private static final String TOKEN_AND_SECRET_NOT_FOUND = "Token and Secret not found.";

	private static final Integer			MAX_REPLY_ITERATION	= 5;

	private static final Integer TWITTER_MENTION_PAGES_RETRY = 3;

	@Autowired
	private SocialPostInfoRepository		socialPostInfoRepository;
	
	@Autowired
	private ISocialPostsAssetService		socialPostsAssetService;

	@Autowired
	BusinessRepository						businessRepo;
	
	@Autowired
	private CommonService					commonService;
	
	@Autowired
	private SessionTokenRepository			sessionTokenRepository;

	@Autowired
	private IBusinessGetPageService businessGetPageService;

	@Autowired
	private BusinessGetPageReqRepo			businessGetPageReqRepo;
	
	@Autowired
	private IRedisLockService				redisService;
	
	@Autowired
	private BusinessGetPageOpenUrlReqRepo businessGetPageOpenUrlReqRepo;

	@Autowired
	private SocialProxyHandler socialProxyHandler;

	@Autowired
	private SocialTwitterAccountRepository	socialTwitterRepo;
	
	@Autowired
	private SocialSetupAuditRepository setupAuditRepo;

	@Autowired
	private IBusinessCoreService businessCoreService;

	@Autowired
	private TwitterSocialAccountService twitterSocialAccountService;

	@Autowired
	private IBusinessCachedService businessService;

	@Autowired
	private ISocialAppService socialAppService;

	@Autowired
	private KafkaProducerService kafkaProducer;

	@Autowired
	private IBrokenIntegrationService brokenIntegrationService;

	@Autowired
	private NexusService nexusService;

	@Autowired
	private SocialPostScheduleInfoRepo socialPostScheduleInfoRepository;

	@Autowired
	private CacheService cacheService;

	@Autowired
	private KafkaExternalService kafkaExternalService;

	@Autowired
	private IPermissionMappingService permissionMappingService;

	@Autowired
	private TwitterCommonService twitterCommonService;

	@Autowired
	private SocialPostInfoRepository socialPostPublishInfoRepository;

	@Autowired
	private XSpecification xSpecification;

	@Autowired
	@Qualifier("socialRestTemplate")
	private RestTemplate restClient;


	private static final Logger				LOGGER				= LoggerFactory.getLogger(SocialPostTwitterServiceImpl.class);
	private SocialMentionData data;


	@Override
	public void postOnTwitter(SocialPostPublishInfo publishInfo, File videoFile,LocationTagMetaDataRequest request) throws Exception {
		LOGGER.info("postOnTwitter method call with publishInfoId: {}", publishInfo.getId());
		if (StringUtils.isNotEmpty(publishInfo.getSocialPost().getVideoIds())) {
			postVideoOnTwitter(publishInfo, videoFile,request);
		} else if (StringUtils.isNotEmpty(publishInfo.getSocialPost().getImageIds())) {
			postImagesOnTwitter(publishInfo,request);
		} else {
			postTextOnTwitter(publishInfo,request);
		}
	}


	private void postVideoOnTwitter(SocialPostPublishInfo publishInfo, File videoFile,LocationTagMetaDataRequest request) {
		BusinessTwitterAccounts twitterAccounts = null;

		try {
			Business business = businessRepo.findById(publishInfo.getBusinessId());
			twitterAccounts = socialTwitterRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
			if(null == twitterAccounts) {
				throw new Exception("No account mapped to the business");
			}
			TwitterData data = twitterRequest(publishInfo, videoFile, request);
			TweetInfo tweetInfo = postVideo(twitterCreds(business, twitterAccounts), data, publishInfo, twitterAccounts);
			if(Objects.nonNull(tweetInfo.getId())) {
				successPostLogging(publishInfo, tweetInfo, twitterAccounts);
			}
		} catch (BirdeyeSocialException ex) {
			if(MapUtils.isEmpty(ex.getData())) {
				Map<String, Object> data = new HashMap<>();
				data.put("error_code", ex.getCode());
				data.put("error_sub_code", ex.getErrorCode());
				data.put("message", ex.getMessage());
				ex.setData(data);
			}
			boolean isEligibleForRetry = commonService.retryPostIfEligible(ex, publishInfo);
			if(isEligibleForRetry) return;

			PermissionMapping pm = errorHandlerForTwitterService(ex);

			if((Objects.nonNull(ex.getCode()) && (ex.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value())) ||
					Objects.nonNull(pm) && (Constants.TWITTER_PAGE_INVALID_ERROR.contains(pm.getErrorMessage()) || pm.getMarkInvalid() == 1)) {;
				kafkaExternalService.markPageInvalid(SocialChannel.TWITTER.getName(), publishInfo.getExternalPageId());
			}

			handlePostError(publishInfo,  pm.getErrorMessage(), pm.getBucket());
		} catch (Exception e) {
			handlePostError(publishInfo, e.getMessage(), null);
		}
	}



	private TwitterData twitterRequest(SocialPostPublishInfo publishInfo, File videoFile,
			LocationTagMetaDataRequest request) {
		String message = (null != publishInfo && null != publishInfo.getSocialPost()) ? publishInfo.getSocialPost().getPostText() : null ;
		String locationId = null !=request ? request.getLocationId() : null;
		TwitterData data = new TwitterData(message, Objects.nonNull(videoFile) ? Collections.singletonList(videoFile) : Collections.emptyList(),FileType.VIDEO,locationId,null,null);
		if(Objects.nonNull(publishInfo.getSocialPost().getQuotedPostId()) && StringUtils.isNotEmpty(publishInfo.getSocialPost().getQuotedPostUrl())) {
			if (Objects.nonNull(publishInfo.getSocialPost().getQuotedTweetSource()) && StringUtils.isNotEmpty(publishInfo.getSocialPost().getQuotedTweetSource()) &&
					StringUtils.equalsIgnoreCase(publishInfo.getSocialPost().getQuotedTweetSource(), Constants.TWITTER_SOURCE_TWITTER)) {
				data.setAttachmentUrl(publishInfo.getSocialPost().getQuotedPostUrl());
				data.setQuoteTweetId(publishInfo.getSocialPost().getQuotedPostId());
			} else {
				List<SocialPostPublishInfo> quotedPostsPublishInfo = socialPostPublishInfoRepository.findBySocialPostIdAndIsPublishedAndSourceId(Integer.valueOf(publishInfo.getSocialPost().getQuotedPostId()), SocialPostStatusEnum.PUBLISHED.getId(), SocialChannel.TWITTER.getId());
				if (CollectionUtils.isNotEmpty(quotedPostsPublishInfo)) {
					data.setAttachmentUrl(publishInfo.getSocialPost().getQuotedPostUrl());
					data.setQuoteTweetId(quotedPostsPublishInfo.get(0).getPostId());
				}
			}
		}
		return data;
	}

	private PermissionMapping errorHandlerService(String channel, Integer failureCode, Integer errorSubCode, String message) {


		List<PermissionMapping> permissionMappings =permissionMappingService.getDataByChannelAndParentErrorCodeAndPermissionCode(channel,
				failureCode, errorSubCode);

		if(CollectionUtils.isEmpty(permissionMappings)) {
			return permissionMappingService.getDataByChannelAndPermissionCode(channel,
					Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
		}
		if(permissionMappings.size() == 1) return permissionMappings.get(0);

		for(PermissionMapping pm: permissionMappings) {
			if(com.birdeye.social.utils.StringUtils.isNotEmpty(message) && message.contains(pm.geterrorActualMessage())){
				return pm;
			}
		}
		return null;

	}

	private PermissionMapping errorHandlerForTwitterService(BirdeyeSocialException ex) {
		PermissionMapping permissionMapping = permissionMappingService.getDataByChannelAndActualErrorText(SocialChannel.TWITTER.getName(), ex.getMessage());
		if(Objects.isNull(permissionMapping)) {
			LOGGER.info("New error found for TWITTER posting with message: {}", ex.getMessage());
			permissionMapping =  permissionMappingService.getDataByChannelAndPermissionCode(Constants.TWITTER,
					Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
		}

		return permissionMapping;
	}

	private void postImagesOnTwitter(SocialPostPublishInfo publishInfo,LocationTagMetaDataRequest request) {
		Business business;
		if (publishInfo.getEnterpriseId() != null) {
			business = businessRepo.findById(publishInfo.getEnterpriseId());
		} else {
			business = businessRepo.findById(publishInfo.getBusinessId());
		}
		BusinessTwitterAccounts twitterAccounts = socialTwitterRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
		if(null == twitterAccounts ) {
			LOGGER.info("No twitter accounts found for business: {} and pageId: {}, marking the publish as failed", publishInfo.getBusinessId(), publishInfo.getPageId());
			publishInfo.setIsPublished(2);
			PermissionMapping pm = errorHandlerService(Constants.TWITTER,ErrorCodes.NO_PROFILE_MAPPED_BUSINESS.value(), -1, ExternalAPIErrorCode.NO_PROFILE_MAPPED_BUSINESS.getDescription());
			publishInfo.setFailureReason(pm.getErrorMessage());
			publishInfo.setBucket(pm.getBucket());
			socialPostInfoRepository.saveAndFlush(publishInfo);
			kafkaExternalService.publishSocialPostEvent(publishInfo);
			return;
		}
		
		String[] imageIds = publishInfo.getSocialPost().getImageIds().split(",");
		if (imageIds.length > 4) {
			LOGGER.info("More than 4 images are not allowed for twitter post, marking the publish: {} as failed", publishInfo.getId());
			publishInfo.setIsPublished(2);
			PermissionMapping pm = errorHandlerService(Constants.TWITTER,ErrorCodes.POST_MORE_THAN_4_IMAGES.value(), -1, ExternalAPIErrorCode.POST_MORE_THAN_4_IMAGES.getDescription());
			publishInfo.setFailureReason(pm.getErrorMessage());
			publishInfo.setBucket(pm.getBucket());
			socialPostInfoRepository.saveAndFlush(publishInfo);
			kafkaExternalService.publishSocialPostEvent(publishInfo);
			return;
		}
		List<Integer> ids = new ArrayList<>();
		for (String imageId : imageIds) {
			ids.add(Integer.parseInt(imageId));
		}
		//Get Image URLs
		List<String> imageUrls = socialPostsAssetService.findImageUrlsByIds(ids, business.getBusinessId().toString());
		List<String> completeImageUrls = new ArrayList<>();
		for (String imageUrl : imageUrls) {
			completeImageUrls.add(imageUrl);
		}
		
		List<File> filesToBeUploaded = SocialFileHandlingUtils.getFilesToBeUploaded(completeImageUrls);
		TweetInfo status = null;
		if (CollectionUtils.isNotEmpty(filesToBeUploaded)) {
			String statusText = publishInfo.getSocialPost().getPostText();
			String quoteTweetId = null;
			if(Objects.nonNull(publishInfo.getSocialPost().getQuotedPostId()) && StringUtils.isNotEmpty(publishInfo.getSocialPost().getQuotedPostUrl())) {
				if (Objects.nonNull(publishInfo.getSocialPost().getQuotedTweetSource()) && StringUtils.isNotEmpty(publishInfo.getSocialPost().getQuotedTweetSource()) &&
						StringUtils.equalsIgnoreCase(publishInfo.getSocialPost().getQuotedTweetSource(), Constants.TWITTER_SOURCE_TWITTER)) {
					quoteTweetId=publishInfo.getSocialPost().getQuotedPostId();
				} else {
					List<SocialPostPublishInfo> quotedPostsPublishInfo = socialPostPublishInfoRepository.findBySocialPostIdAndIsPublishedAndSourceId(Integer.valueOf(publishInfo.getSocialPost().getQuotedPostId()), SocialPostStatusEnum.PUBLISHED.getId(), SocialChannel.TWITTER.getId());
					if (CollectionUtils.isNotEmpty(quotedPostsPublishInfo)) {
						quoteTweetId=quotedPostsPublishInfo.get(0).getPostId();
					}
				}
			}
			try {
				String locationId = null !=request ? request.getLocationId() : null;
				status = postTwitterData(twitterCreds(business, twitterAccounts),
						new TwitterData(statusText,filesToBeUploaded,FileType.IMAGE, locationId,null, quoteTweetId, twitterAccounts.getAccountId()));
			} catch (BirdeyeSocialException ex) {
				if(MapUtils.isEmpty(ex.getData())) {
					Map<String, Object> data = new HashMap<>();
					data.put("error_code", ex.getCode());
					data.put("error_sub_code", ex.getErrorCode());
					data.put("message", ex.getMessage());
					ex.setData(data);
				}
				boolean isEligibleForRetry = commonService.retryPostIfEligible(ex, publishInfo);
				if(isEligibleForRetry) return;
				PermissionMapping pm = errorHandlerForTwitterService(ex);

				if((Objects.nonNull(ex.getCode()) && (ex.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value())) ||
						Objects.nonNull(pm) && (Constants.TWITTER_PAGE_INVALID_ERROR.contains(pm.getErrorMessage()) || pm.getMarkInvalid() == 1)) {;
					kafkaExternalService.markPageInvalid(SocialChannel.TWITTER.getName(), publishInfo.getExternalPageId());
				}

				handlePostError(publishInfo, pm.getErrorMessage(), pm.getBucket());
			}catch (ResourceAccessException ex) {
				publishInfo.setIsPublished(2);
				PermissionMapping pm = errorHandlerService(Constants.TWITTER,ErrorCodes.TWITTER_RESOURCE_FAIL.value(), -1, ExternalAPIErrorCode.TWITTER_RESOURCE_FAIL.getDescription());
				publishInfo.setFailureReason(pm.getErrorMessage());
				publishInfo.setBucket(pm.getBucket());
			} catch(Exception e) {
				handlePostError(publishInfo, e.getMessage(), null);
			}
			
			if (status != null) {
				publishInfo.setPostId(status.getId());
				publishInfo.setIsPublished(1);
				publishInfo.setPostUrl(twitterAccounts.getProfileUrl() + STATUS + String.valueOf(status.getId()));
				List<SocialPostsAssets> assets = socialPostsAssetService.findAllByImageUrlAndBucketId(imageUrls, String.valueOf(business.getBusinessId()));
				Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
				for (SocialPostsAssets asset : assets) {
					asset.setLastUsedDate(cal.getTime());
					socialPostsAssetService.save(asset);
				}
			}
			socialPostInfoRepository.saveAndFlush(publishInfo);
			if(SocialPostStatusEnum.PUBLISHED.getId().equals(publishInfo.getIsPublished())
					&& commonService.isBusinessAllowedToSyncBusinessPosts(publishInfo.getEnterpriseId())) {
				kafkaProducer.sendObjectV1(SOCIAL_SYNC_BUSINESS_POSTS.getName(), SocialPostPublishInfoRequest.builder().id(publishInfo.getId()).build());
			}
			kafkaExternalService.publishSocialPostEvent(publishInfo);
		}
	}
	
	@Autowired
	private TwitterService twitterService;
	
	private void postTextOnTwitter(SocialPostPublishInfo publishInfo,LocationTagMetaDataRequest request) {
		BusinessTwitterAccounts twitterAccounts = null;
		TweetInfo info = null;
		try {
			Business business = businessRepo.findById(publishInfo.getBusinessId());
			twitterAccounts = socialTwitterRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
			if(null == twitterAccounts) {
				throw new Exception("No account mapped to the business"); // NO_PAGE_MAPPED_TO_BUSINESS
			}

			TwitterCreds creds = twitterCreds(business, twitterAccounts);
			TwitterData data = twitterRequest(publishInfo, request);
			info=postTwitterData(creds,data);
			publishInfo.setIsPublished(1);
			publishInfo.setPostId(String.valueOf(info.getId()));
			publishInfo.setPostUrl(twitterAccounts.getProfileUrl() + STATUS + String.valueOf(info.getId()));
		} catch (BirdeyeSocialException ex) {
			if(MapUtils.isEmpty(ex.getData())) {
				Map<String, Object> data = new HashMap<>();
				data.put("error_code", ex.getCode());
				data.put("error_sub_code", ex.getErrorCode());
				data.put("message", ex.getMessage());
				ex.setData(data);
			}
			boolean isEligibleForRetry = commonService.retryPostIfEligible(ex, publishInfo);
			if(isEligibleForRetry) return;
			PermissionMapping pm = errorHandlerForTwitterService(ex);

			if((Objects.nonNull(ex.getCode()) && (ex.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value())) ||
					Objects.nonNull(pm) && (Constants.TWITTER_PAGE_INVALID_ERROR.contains(pm.getErrorMessage()) || pm.getMarkInvalid() == 1)) {;
				kafkaExternalService.markPageInvalid(SocialChannel.TWITTER.getName(), publishInfo.getExternalPageId());
			}
			handlePostError(publishInfo, pm.getErrorMessage(), pm.getBucket());
		} catch (Exception ex) {
			handlePostError(publishInfo, ex.getMessage(), null);
		}
		socialPostInfoRepository.saveAndFlush(publishInfo);
		if(SocialPostStatusEnum.PUBLISHED.getId().equals(publishInfo.getIsPublished())
				&& commonService.isBusinessAllowedToSyncBusinessPosts(publishInfo.getEnterpriseId())) {
			kafkaProducer.sendObjectV1(SOCIAL_SYNC_BUSINESS_POSTS.getName(), SocialPostPublishInfoRequest.builder().id(publishInfo.getId()).build());
		}
		kafkaExternalService.publishSocialPostEvent(publishInfo);
		//TODO Twitter Migration API does not provide FollowerCount data from API
		if(Objects.nonNull(info) && StringUtils.isNotEmpty(publishInfo.getSocialPost().getSaveType()) && publishInfo.getSocialPost().getSaveType().equalsIgnoreCase("review_share")) {
			kafkaExternalService.pushSocialPostingToES(info.getFollowerCount(),publishInfo.getSourceId(),publishInfo.getPublishDate(),
					publishInfo.getBusinessId(),publishInfo.getEnterpriseId(),publishInfo.getSocialPost().getReviewId(),publishInfo.getPublishedBy());
		}
	}



	private TwitterData twitterRequest(SocialPostPublishInfo publishInfo, LocationTagMetaDataRequest request) {
		TwitterData data = new TwitterData();
		data.setText( (null != publishInfo && null != publishInfo.getSocialPost()) ? publishInfo.getSocialPost().getPostText() : null);
		data.setAccountId(null != publishInfo && null != publishInfo.getEnterpriseId() ? publishInfo.getEnterpriseId() : null);
		data.setLocationId(null != request ? request.getLocationId() : null);
		if(Objects.nonNull(publishInfo.getSocialPost().getQuotedPostId()) && StringUtils.isNotEmpty(publishInfo.getSocialPost().getQuotedPostUrl())) {
			if (Objects.nonNull(publishInfo.getSocialPost().getQuotedTweetSource()) && StringUtils.isNotEmpty(publishInfo.getSocialPost().getQuotedTweetSource()) &&
					StringUtils.equalsIgnoreCase(publishInfo.getSocialPost().getQuotedTweetSource(), Constants.TWITTER_SOURCE_TWITTER)) {
				data.setAttachmentUrl(publishInfo.getSocialPost().getQuotedPostUrl());
				data.setQuoteTweetId(publishInfo.getSocialPost().getQuotedPostId());
			} else {
				List<SocialPostPublishInfo> quotedPostsPublishInfo = socialPostPublishInfoRepository.findBySocialPostIdAndIsPublishedAndSourceId(Integer.valueOf(publishInfo.getSocialPost().getQuotedPostId()), SocialPostStatusEnum.PUBLISHED.getId(), SocialChannel.TWITTER.getId());
				if (CollectionUtils.isNotEmpty(quotedPostsPublishInfo)) {
					data.setAttachmentUrl(publishInfo.getSocialPost().getQuotedPostUrl());
					data.setQuoteTweetId(quotedPostsPublishInfo.get(0).getPostId());
				}
			}
		}
		return data;
	}

	private TwitterCreds twitterCreds(Business business, BusinessTwitterAccounts twitterAccounts) throws Exception {
//		ConsumerTokenAndSecret consumer = commonService.getAppKeyAndToken(business, "twitter");
//		WebsiteDomainInfo websiteDomainInfo = null;
//		if(isOpenUrlConnectedPage(twitterAccounts)){
//			websiteDomainInfo = businessService.getDefaultDomain(business.getId());
//		}else{
//			websiteDomainInfo = businessService.getBusinessDomain(business);
//		}
		SocialAppCredsInfo domainInfo = socialAppService.getTwitterAppSettingsV2();
		TwitterCreds creds = new TwitterCreds(domainInfo.getChannelClientId(), domainInfo.getChannelClientSecret(), twitterAccounts.getAccessToken(), twitterAccounts.getAccessSecret());
		return creds;
	}
	
	private boolean isOpenUrlConnectedPage(BusinessTwitterAccounts twitterAccounts){
		List<BusinessTwitterAccounts> twitterAccount =  socialTwitterRepo.findByProfileId(twitterAccounts.getProfileId());
		BusinessTwitterAccounts accounts = twitterAccount.get(0);
		return StringUtils.isNotBlank(accounts.getRequestId()) && StringUtils.contains(accounts.getRequestId(), "openurl");
	}

	private void handlePostError(SocialPostPublishInfo publishInfo, String message, Integer bucketId) {
		// TODO: Currently posts are saved before publish. ID should be avaialble.
		LOGGER.error("Error posting content to Twitter for {} {}", publishInfo.getId(), message);

		publishInfo.setIsPublished(2);

		if(Objects.isNull(bucketId)) {
			LOGGER.info("New error found for TWITTER posting with message: {}", message);
			PermissionMapping pm = permissionMappingService.getDataByChannelAndPermissionCode(Constants.TWITTER,
					Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
			publishInfo.setFailureReason(pm.getErrorMessage());
			publishInfo.setBucket(pm.getBucket());
			return;
		}

		publishInfo.setFailureReason(message);
		publishInfo.setBucket(bucketId);
	}
	
	@Override
	public String updateAccountInfo(Integer businessId) throws Exception {
		Integer count = 0;
		TwitterCreds creds;
		TwitterUserInfo userInfo;
		Business business = businessRepo.findOne(businessId);
		List<Integer> businessIds = new ArrayList<>();
		if ("Business".equalsIgnoreCase(business.getType()) || "Product".equalsIgnoreCase(business.getType())) {
			businessIds.add(businessId);
		} else if ("Enterprise-Location".equalsIgnoreCase(business.getType()) || "Enterprise-Product".equalsIgnoreCase(business.getType())) {
			businessIds = businessRepo.findEnterpriseLocations(businessId);
		}
		
		List<BusinessTwitterAccounts> twitterAccounts = socialTwitterRepo.findByBusinessIdIn(businessIds);
		for (BusinessTwitterAccounts account : twitterAccounts) {
			creds = twitterCreds(business, account);
			try {
				userInfo = twitterService.getAccountInfoV2(creds,account.getHandle().substring(1));
				account.setProfileId(userInfo.getProfileId());
				account.setHandle(userInfo.getHandle());
				account.setProfilePicUrl(userInfo.getProfilePictureUrl());
				account.setName(userInfo.getName());
				account.setEnabled(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialAutoPostFlag());
				socialTwitterRepo.saveAndFlush(account);
				count++;
			} catch (BirdeyeSocialException ex) {
				if (Objects.nonNull(ex.getCode()) && (ex.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value())) {
					commonService.handleExpiredTokenForTwitter(account);
				}
			}
		}
		return count + " twitter pages updated";
	}
	
	@Override
	public String updateAccountInfo() throws Exception {
		Integer count = 0;
		TwitterCreds creds;
		TwitterUserInfo userInfo;
		Business business;
		List<BusinessTwitterAccounts> accounts = socialTwitterRepo.findByProfileIdIsNull();
		for (BusinessTwitterAccounts account : accounts) {
			business = businessRepo.findOne(account.getBusinessId());
			creds = twitterCreds(business, account);
			try {
				userInfo = twitterService.getAccountInfoV2(creds,account.getHandle().substring(1));
				account.setProfileId(userInfo.getProfileId());
				account.setHandle(userInfo.getHandle());
				account.setProfilePicUrl(userInfo.getProfilePictureUrl());
				account.setName(userInfo.getName());
				socialTwitterRepo.saveAndFlush(account);
				count++;
			} catch (BirdeyeSocialException ex) {
				if (Objects.nonNull(ex.getCode()) && (ex.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value())) {
					commonService.handleExpiredTokenForTwitter(account);
				}
				if (ex.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()) {
					account.setEnabled(0);
					socialTwitterRepo.saveAndFlush(account);
				}
			}
		}
		return count + " twitter pages updated";
	}
	
	@Override
	public SocialTimeline getTwitterTimeline(Business business,BusinessTwitterAccounts twitterAccounts, String type, String maxId, Date maxDate) {
		SocialTimeline twitterTimeline = new SocialTimeline();
		twitterTimeline.setChannel("twitter");
		twitterTimeline.setStreamName(twitterAccounts.getHandle().substring(1));
		twitterTimeline.setStreamImage(twitterAccounts.getProfilePicUrl());
		twitterTimeline.setPageId(twitterAccounts.getId());
		twitterTimeline.setBusinessId(twitterAccounts.getBusinessId());
		SocialTimelineLocationInfo locationInfo = new SocialTimelineLocationInfo();
		locationInfo.setId(String.valueOf(twitterAccounts.getId()));
		locationInfo.setName(twitterAccounts.getLocation());
		twitterTimeline.setLocation(locationInfo);
		try {
			TwitterCreds creds = twitterCreds(business, twitterAccounts);
			if (SocialStreams.StreamType.HOME.getType().equalsIgnoreCase(type)) {
				twitterTimeline.setStreamType(type.toLowerCase());
				List<Feed> feeds = new ArrayList<>();
				MediaTweetResponse mediaTweetResponse = twitterService.getTwitterHomeTimeline(creds, maxId,String.valueOf(twitterAccounts.getProfileId()));
				maxId = getFeedsFromTweets(mediaTweetResponse != null ? mediaTweetResponse.getMediaTweets() : null, twitterAccounts.getProfileId(), feeds, maxDate);
				twitterTimeline.setFeeds(feeds);
				twitterTimeline.setNextUrl(maxId);
			} else if (SocialStreams.StreamType.MY_POSTS.getType().equalsIgnoreCase(type)) {
				twitterTimeline.setStreamType(type.toLowerCase());
				List<Feed> feeds = new ArrayList<>();
				maxId = getFeedsFromTweets(twitterService.getUserTimelineV2(creds, maxId, String.valueOf(twitterAccounts.getProfileId())),
						twitterAccounts.getProfileId(), feeds, maxDate);
				twitterTimeline.setFeeds(feeds);
				twitterTimeline.setNextUrl(maxId);
			} else if (SocialStreams.StreamType.MENTIONS.getType().equalsIgnoreCase(type)) {
				twitterTimeline.setStreamType(type.toLowerCase());
				List<Feed> feeds = new ArrayList<>();
				maxId = getFeedsFromTweets(twitterService.
						getMentionsTimelineV2(creds, maxId,String.valueOf(twitterAccounts.getProfileId()),null),twitterAccounts.getProfileId(), feeds, maxDate);
				twitterTimeline.setFeeds(feeds);
				twitterTimeline.setNextUrl(maxId);
			}
		} catch (BirdeyeSocialException ex) {
			if (ex.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()) {
				twitterTimeline.setReconnect(Boolean.TRUE);
				commonService.handleExpiredTokenForTwitter(twitterAccounts);
			}
		} catch (Exception ex) {
			LOGGER.info("Exception occured which fetching date from twitter {}", ex.getMessage());
		}
		return twitterTimeline;
	}
	
	private Feed getFeedFromTweet(MediaTweet tweet, Long userId) {
		Feed feed = new Feed();
		List<FeedHeader> headers = new ArrayList<>();
		Set<String> replyingTo = new HashSet<>();
		FeedHeader header;
		if (tweet.getRetweetedStatus() != null) {
			header = new FeedHeader();
			header.setType(FeedHeader.FeedHeaderType.RETWEET.getType());
			header.setText(tweet.getUser().getName());
			header.setUrl(TWITTER_BASE_URL + tweet.getUser().getScreenName());
			headers.add(header);
			tweet = tweet.getRetweetedStatus();
		}
		feed.setPublisherId(tweet.getUser().getIdStr());
		feed.setPublisherName(tweet.getUser().getName());
		feed.setPublisherHandle(tweet.getUser().getScreenName());
		replyingTo.add(tweet.getUser().getScreenName());
		feed.setProfileURL(TWITTER_BASE_URL + tweet.getUser().getScreenName());
		feed.setProfileImage(tweet.getUser().getProfileImageUrlHttps());
		LOGGER.info("Date Published :{}",tweet.getCreatedAt() );
		DateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT_1);
		//feed.setDatePublished(DateTimeUtils.relativeDate(tweet.getCreatedAt()));
		feed.setDatePublished(dateFormat.format(tweet.getCreatedAt()));
		LOGGER.info("Date Published m1:{}",feed.getDatePublished() );//2023/10/23 06:45:47

		feed.setFeedId(tweet.getIdStr());
		feed.setCan_like(!tweet.isFavorited());
		feed.setCanRetweet(!tweet.isRetweeted());
//		StrBuilder tweetContent = new StrBuilder(unescapeUnicodes(tweet.getText()));
//		if (tweet.getEntities() != null && CollectionUtils.isNotEmpty(tweet.getEntities().getUserMentions())) {
//			for (UserMentionsEntity mention : tweet.getEntities().getUserMentions()) {
//				replyingTo.add(mention.getScreenName());
//				tweetContent.replaceAll("@" + mention.getScreenName(), "");
//			}
//		}
//		feed.setFeedText(tweetContent.trim().toString());
		feed.setFeedText(tweet.getText());
		if (tweet.getExtendedEntitiesData() != null && CollectionUtils.isNotEmpty(tweet.getExtendedEntitiesData().getMedia())) {
			List<String> videoUrls = null;
			List<String> imageUrls = null;
			for (ExtendedMediaEntity entity : tweet.getExtendedEntitiesData().getMedia()) {
				if ("photo".equals(entity.getType())) {
					if (imageUrls == null) {
						imageUrls = new ArrayList<>();
					}
					imageUrls.add(entity.getMediaUrlHttps());
				} else if ("video".equals(entity.getType()) || "animated_gif".equals(entity.getType())) {
					if (videoUrls == null) {
						videoUrls = new ArrayList<>();
					}
					videoUrls.add(entity.getMediaUrlHttps());
				/*	for (VideoVariants variant : entity.getVideoInfo().getVariants()) {
						if ("video/mp4".equals(variant.getContentType()) && variant.getBitrate() != null) {
							// handling for videos
							videoUrls.add(variant.getUrl());
							break;
						} else if ("video/mp4".equals(variant.getContentType()) && "animated_gif".equals(entity.getType())) {
							// handling for gifs
							videoUrls.add(variant.getUrl());
							break;
						}
					}*/
				}
			}
			feed.setImages(imageUrls);
			feed.setVideos(videoUrls);
		}
		feed.setLikes(tweet.getFavoriteCount());
		feed.setShares(tweet.getRetweetCount());
		feed.setCanDelete(userId.toString().equals(tweet.getUser().getId()));
//		feed.setEditable(userId.toString().equals(tweet.getUser().getId()));
		StringBuilder urlBuilder = new StringBuilder(TWITTER_BASE_URL);
		urlBuilder.append(tweet.getUser().getScreenName()).append(STATUS).append(tweet.getIdStr());
		feed.setFeedUrl(urlBuilder.toString());
		if (CollectionUtils.isNotEmpty(replyingTo)) {
			for (String reply : replyingTo) {
				header = new FeedHeader();
				header.setType(FeedHeader.FeedHeaderType.REPLY.getType());
				header.setText("@" + reply);
				header.setUrl(TWITTER_BASE_URL + reply);
				headers.add(header);
			}
		}
		feed.setFeedHeaders(headers);
		if (tweet.getQuoted_status() != null) {
			feed.setQuotedFeed(getFeedFromTweet(tweet.getQuoted_status(), userId));
		}
		return feed;
	}

	public String getFeedsFromTweets(List<MediaTweet> tweets, Long userId, List<Feed> feeds, Date maxDate) {
		if (CollectionUtils.isEmpty(tweets)) {
			return null;
		}
		String maxId = null;
		Feed feed;
		for (MediaTweet tweet : tweets) {
			if(Objects.nonNull(maxDate) && tweet.getCreatedAt().before(maxDate)) {
				maxId = null;
				continue;
			}
			feed = getFeedFromTweet(tweet, userId);
			feed.setCanDelete(userId.toString().equals(tweet.getUser().getId()));
			StringBuilder urlBuilder = new StringBuilder(TWITTER_BASE_URL);
			urlBuilder.append(tweet.getUser().getScreenName()).append(STATUS).append(tweet.getIdStr());
			feed.setFeedUrl(urlBuilder.toString());
			feeds.add(feed);
			maxId = tweet.getId();
		}
		return maxId == null ? null : String.valueOf(Long.parseLong(maxId) - 1);
	}

	@Override
	public String getFeedsFromTweetsWithMinDate(List<MediaTweet> tweets, Long userId, List<Feed> feeds, Date minDate ,String type) {
		if (CollectionUtils.isEmpty(tweets)) {
			return null;
		}
		String sinceId = null;
		Feed feed;
		for (MediaTweet tweet : tweets) {
			if(Objects.nonNull(minDate) && tweet.getCreatedAt().after(minDate)) {
				sinceId = null;
				continue;
			}
			feed = getFeedFromTweet(tweet, userId);
			if(feed.isReply()){
				continue;
			}
			feed.setType(type);
			feed.setCanDelete(userId.toString().equals(tweet.getUser().getId()));
			feed.setFeedUrl(TWITTER_BASE_URL + tweet.getUser().getScreenName() + STATUS + tweet.getIdStr());
			feed.setConversationId(tweet.getConversation_id());
			feed.setLikes(tweet.getFavorite_count());
			feed.setComments(tweet.getComment_count());
			feeds.add(feed);
			sinceId = tweet.getId();
		}
		return sinceId == null ? null : String.valueOf(Long.parseLong(sinceId) + 1);
	}

	@Override
	public List<Feed> getRepliesForTwitter(Business business, BusinessTwitterAccounts twitterAccounts, String statusId, String publishedBy) throws Exception {
		TwitterCreds creds = twitterCreds(business, twitterAccounts);
		List<MediaTweet> replies = new ArrayList<>();
		List<MediaTweet> parentNodes = new ArrayList<>();
		String maxId = null;
		int count = 0;
		Map<String, String> converstationMap = new HashMap<>();
		List<String> ultimateDecendents = new ArrayList<>();
		while (count < MAX_REPLY_ITERATION) {
			count++;
			SearchResults results = twitterService.getSearchResults(creds, maxId, statusId, publishedBy);
			if (results == null || CollectionUtils.isEmpty(results.getStatuses())) {
				break;
			}
			maxId = null;
			List<MediaTweet> allTweets = results.getStatuses();
			Collections.reverse(allTweets);
			for (MediaTweet tweet : allTweets) {
				if (tweet.getInReplyToStatusId() == null) {
					continue;
				}
				String inReply = tweet.getInReplyToStatusId().toString();
				if (inReply.equals(statusId)) {
					parentNodes.add(tweet);
					ultimateDecendents.add(tweet.getIdStr());
				} else if (ultimateDecendents.contains(inReply)) {
					replies.add(tweet);
					converstationMap.put(tweet.getIdStr(), inReply);
				} else if (converstationMap.keySet().contains(inReply)) {
					replies.add(tweet);
					converstationMap.put(tweet.getIdStr(), converstationMap.get(inReply));
				}
				if (maxId == null) {
					maxId = String.valueOf(Long.parseLong(tweet.getId()) - 1);
				}
			}
			if (results.isLastPage() || replies.size() > 9) {
				break;
			}
		}
		List<Feed> childReplies = new ArrayList<>();
		getFeedsFromTweets(replies, twitterAccounts.getProfileId(), childReplies, null);
		Map<String, Feed> repliesMap = new HashMap<>();
		for (Feed childReply : childReplies) {
			repliesMap.put(childReply.getFeedId(), childReply);
		}
		List<Feed> replyTree = new ArrayList<>();
		getFeedsFromTweets(parentNodes, twitterAccounts.getProfileId(), replyTree, null);
		for (Feed parentReply : replyTree) {
			for (Map.Entry<?, ?> entry : converstationMap.entrySet()) {
				if (entry.getValue().equals(parentReply.getFeedId())) {
					List<Feed> conversations = parentReply.getConversation();
					if (conversations == null) {
						conversations = new ArrayList<>();
					}
					conversations.add(repliesMap.get(entry.getKey().toString()));
					parentReply.setConversation(conversations);
				}
			}
		}
		return replyTree;
	}
	
	@Override
	public String replyToTweet(Business business,BusinessTwitterAccounts twitterAccounts, String statusId, String publishedBy, String replyText, List<String> images, String videoUrl) throws Exception {
		TwitterCreds creds = twitterCreds(business, twitterAccounts);
		TweetInfo status = null;
		if (StringUtils.isNotBlank(videoUrl)) {
			videoUrl = socialPostsAssetService.getCompleteCdnUrlFromBaseUrl(videoUrl, business.getBusinessId().toString());
			List<File> filesToBeUploaded = SocialFileHandlingUtils.getFilesToBeUploaded(Collections.singletonList(videoUrl));
			if (CollectionUtils.isEmpty(filesToBeUploaded)) {
				LOGGER.error("[Social Twitter] Unable to download video with url: {}", videoUrl);
				return null;
			}
			TwitterData data = new TwitterData(replyText);
			data.setInReplyToStatusId(statusId);
			data.setMedia(filesToBeUploaded.get(0));
			data.setType(FileType.VIDEO);
			data.setAccountId(twitterAccounts.getAccountId());
			status=	postTwitterData(creds,data);
			//status = twitterService.post(creds, data);
		} else {
			List<File> filesToBeUploaded = null;
			if (CollectionUtils.isNotEmpty(images)) {
				List<String> fullImageUrls = new ArrayList<>();
				for (String imageUrl : images) {
					fullImageUrls.add(socialPostsAssetService.getCompleteCdnUrlFromBaseUrl(imageUrl, business.getBusinessId().toString()));
				}
				filesToBeUploaded = SocialFileHandlingUtils.getFilesToBeUploaded(fullImageUrls);
			}
			//status = twitterService.post(creds, filesToBeUploaded, replyText, statusId,null, null);
			status=	postTwitterData(creds,new TwitterData(replyText,filesToBeUploaded,FileType.IMAGE,null,statusId,null, twitterAccounts.getAccountId()));
		}
		return status != null ? status.getId() : null;
	}
	
	@Override
	public boolean likeTweet(Business business, BusinessTwitterAccounts twitterAccounts, String statusId) throws Exception {
		TwitterCreds creds = twitterCreds(business, twitterAccounts);
		return twitterService.likeTweetV2(creds, statusId, String.valueOf(twitterAccounts.getProfileId()));
	}
	
	@Override
	public boolean unlikeTweet(Business business, BusinessTwitterAccounts twitterAccounts , String statusId) throws Exception {
		TwitterCreds creds = twitterCreds(business, twitterAccounts);
		return twitterService.unlikeTweetV2(creds, statusId, String.valueOf(twitterAccounts.getProfileId()));
	}
	
	@Override
	public boolean deleteTweet(Business business,  BusinessTwitterAccounts twitterAccounts, String statusId) throws Exception {
		TwitterCreds creds = twitterCreds(business, twitterAccounts);
		//return twitterService.deleteTweet(creds, statusId);
		return twitterService.deleteTweetV2(creds, statusId);

	}

	@Override
	public boolean deleteTweet(BusinessTwitterAccounts twitterAccounts, String statusId) throws Exception {
		TwitterCreds creds = twitterCommonService.getTwitterCreds(twitterAccounts);
		return twitterService.deleteTweetV2(creds, statusId);
	}
	
	@Override
	public boolean retweet(Business business,BusinessTwitterAccounts twitterAccounts, String statusId, String retweetText, String tweetUrl) throws Exception {
		TwitterCreds creds = twitterCreds(business, twitterAccounts);
		if (StringUtils.isBlank(retweetText)) {
			try {
				return twitterService.retweetV2(creds, statusId, String.valueOf(twitterAccounts.getProfileId()));
			}catch (Exception e){
				LOGGER.info("Exception occurred while retweet for feed :{} for profileId :{}",statusId,twitterAccounts.getProfileId());
				return false;
			}
		} else {
			// Retweet with comment is a new tweet with 'tweet URL'
			StringBuilder retweetBuilder = new StringBuilder(retweetText);
			retweetBuilder.append(" ").append(tweetUrl);
			TwitterData data = new TwitterData(retweetBuilder.toString());
			data.setAccountId(twitterAccounts.getAccountId());
			TweetInfo status = postTwitterData(creds, data);
			return !(status == null || status.getId() == null);
		}
	}

	public TweetInfo postTwitterData(TwitterCreds creds,TwitterData data) throws Exception {
		TweetCreateRequest tweetRequest = createTweetRequest(data);
		com.twitter.clientlib.model.TweetCreateResponse twitterResponse = null;
		if (CollectionUtils.isEmpty(data.getMediaData())) {
			twitterResponse = postV2(creds, tweetRequest, null, null, data.getAccountId());
		} else {
			if (data.getType() == FileType.IMAGE) {
				twitterResponse = postV2(creds, tweetRequest, TwitterData.FileType.IMAGE.name(), data.getMediaData(), data.getAccountId());
			} else if (data.getType() == FileType.VIDEO) {
				//postVideo(creds, data)
				twitterResponse = postV2(creds, tweetRequest, TwitterData.FileType.VIDEO.name(), data.getMediaData(), data.getAccountId());
			} else {
				LOGGER.warn("Unsupported type {}", data.getType());
			}
		}
		return MapTwitterData(twitterResponse);
	}

	@Override
	public boolean registerMedia(Integer id, SocialRawPageDetail socialRawPageDetail, MediaUploadRequest mediaInitiateRequest) throws Exception {
		List<BusinessTwitterAccounts> businessTwitterAccounts = socialTwitterRepo.findByProfileId(Long.parseLong(socialRawPageDetail.getPageId()));
		if (CollectionUtils.isEmpty(businessTwitterAccounts)) {
			return false;
		}
		String mediaId;
		TwitterCreds creds = twitterCreds(null, businessTwitterAccounts.get(0));
		Integer accountId = businessTwitterAccounts.get(0).getAccountId();
		if (isTwitterV2MediaUploadEnabled(accountId)) {
			mediaId = initV2(getTemplateForMediaUploadV2(creds), String.valueOf(mediaInitiateRequest.getFileSize()));
		} else {
			mediaId = initV1(createTwitterTemplate(creds), String.valueOf(mediaInitiateRequest.getFileSize()));
		}
		mediaInitiateRequest.setMediaId(mediaId);
		long chunkSize = Long.parseLong(CacheManager.getInstance().getCache(SystemPropertiesCache.class)
                .getProperty(SystemPropertiesCache.MEDIA_UPLOAD_CHUNK_SIZE));
		int segments = (int) Math.ceil((double) mediaInitiateRequest.getFileSize() / (double) chunkSize);
		mediaInitiateRequest.setTotalParts(segments);
        return StringUtils.isNotEmpty(mediaId);
	}

	@Override
	public void uploadChunk(SocialAssetChunkInfo socialAssetChunkInfo, SocialMediaUploadInfo socialMediaUploadInfo, SocialRawPageDetail socialRawPageDetail, SocialMediaUploadRequest socialMediaUploadRequest) throws Exception {
		List<BusinessTwitterAccounts> businessTwitterAccounts = socialTwitterRepo.findByProfileId(Long.parseLong(socialRawPageDetail.getPageId()));
		if(CollectionUtils.isEmpty(businessTwitterAccounts)) {
			return;
		}
		TwitterCreds creds = twitterCreds(null, businessTwitterAccounts.get(0));
		ByteArrayMetaData byteArrayMetaData = socialAssetChunkInfo.getByteArrayMetaData();
		Integer accountId = businessTwitterAccounts.get(0).getAccountId();
		if(isTwitterV2MediaUploadEnabled(accountId)) {
			appendAPIV2(getTemplateForMediaUploadV2(creds), Base64.decodeBase64(byteArrayMetaData.getChunk()), socialMediaUploadRequest.getVideoId(), String.valueOf(socialAssetChunkInfo.getSequenceId()));
		} else {
			appendAPIV1(createTwitterTemplate(creds), Base64.decodeBase64(byteArrayMetaData.getChunk()), socialMediaUploadRequest.getVideoId(), String.valueOf(socialAssetChunkInfo.getSequenceId()));
		}
	}

	@Override
	public void finalizeVideoUpload(SocialMediaUploadRequest socialMediaUploadRequest, SocialRawPageDetail socialRawPageDetail, List<String> eTags) throws Exception {
		List<BusinessTwitterAccounts> businessTwitterAccounts = socialTwitterRepo.findByProfileId(Long.parseLong(socialRawPageDetail.getPageId()));
		if(CollectionUtils.isEmpty(businessTwitterAccounts)) {
			return;
		}
		TwitterCreds creds = twitterCreds(null, businessTwitterAccounts.get(0));
		TwitterTemplate twitterTemplate;
		if(isTwitterV2MediaUploadEnabled(businessTwitterAccounts.get(0).getAccountId())) {
			twitterTemplate = getTemplateForMediaUploadV2(creds);
			finalizeV2(twitterTemplate, socialMediaUploadRequest.getVideoId());
			if (!statusV2(twitterTemplate, socialMediaUploadRequest.getVideoId(), 15)) {
				throw new BirdeyeSocialException(ErrorCodes.TWITTER_MEDIA_POST_FAILED, "Unable to post video to Twitter.");
			}
		} else {
			twitterTemplate = createTwitterTemplate(creds);
			finalizeV1(twitterTemplate, socialMediaUploadRequest.getVideoId());
			if (!statusV1(twitterTemplate, socialMediaUploadRequest.getVideoId(), 15)) {
				throw new BirdeyeSocialException(ErrorCodes.TWITTER_MEDIA_POST_FAILED, "Unable to post video to Twitter.");
			}
		}
	}

	@Override
	public void checkStatus(SocialRawPageDetail socialRawPageDetail, MediaUploadRequest mediaUploadRequest) throws Exception {
		List<BusinessTwitterAccounts> businessTwitterAccounts = socialTwitterRepo.findByProfileId(Long.parseLong(socialRawPageDetail.getPageId()));
		if(CollectionUtils.isEmpty(businessTwitterAccounts)) {
			return;
		}
		boolean status;
		TwitterCreds creds = twitterCreds(null, businessTwitterAccounts.get(0));
		Integer accountId = businessTwitterAccounts.get(0).getAccountId();
		if(isTwitterV2MediaUploadEnabled(accountId)) {
			status = statusV2(getTemplateForMediaUploadV2(creds), mediaUploadRequest.getMediaId(), 1);
		} else {
			status = statusV1(createTwitterTemplate(creds), mediaUploadRequest.getMediaId(), 1);
		}
		mediaUploadRequest.setStatus(status);
		mediaUploadRequest.setPageId(socialRawPageDetail.getPageId());
		mediaUploadRequest.setChannel(SocialChannel.getSocialChannelNameById(mediaUploadRequest.getSourceId()));
    }

	@Override
	public void generalExceptionHandler(String message, Integer publishInfoId) {
		SocialPostPublishInfo socialPostPublishInfo = socialPostInfoRepository.findOne(publishInfoId);
		failedPostLogging(socialPostPublishInfo,null,message,null);
	}

	@Override
	public void birdeyeExceptionHandler(BirdeyeSocialException bse, Integer publishInfoId, String pageId) {
		SocialPostPublishInfo socialPostPublishInfo = socialPostInfoRepository.findOne(publishInfoId);
		List<BusinessTwitterAccounts> businessTwitterAccounts = socialTwitterRepo.findByProfileId(Long.parseLong(pageId));
		if(CollectionUtils.isEmpty(businessTwitterAccounts)) {
			return;
		}
		putErrorMessageInPublishInfo(bse,socialPostPublishInfo, businessTwitterAccounts.get(0));
	}

	private void putErrorMessageInPublishInfo(BirdeyeSocialException bse, SocialPostPublishInfo publishInfo, BusinessTwitterAccounts businessTwitterAccounts) {
		if (MapUtils.isEmpty(bse.getData())) {
			Map<String, Object> errorMap = new HashMap<>();
			errorMap.put("error_code", bse.getCode());
			errorMap.put("error_sub_code", bse.getErrorCode());
			if (StringUtils.isNotEmpty(bse.getMessage()) &&
					(bse.getMessage().contains("failed: connect timed out") || bse.getMessage().contains("Read timed out"))) {
				bse = new BirdeyeSocialException(ErrorCodes.TWITTER_MEDIA_POST_FAILED, "Internal Server Error");
				errorMap.put("error_message", "Internal Server Error");
			} else {
				errorMap.put("error_message", bse.getMessage());
			}
			bse.setData(errorMap);
		}
		boolean isEligibleForRetry = commonService.retryPostIfEligible(bse, publishInfo);
		if (isEligibleForRetry) return;

		PermissionMapping pm = errorHandlerForTwitterService(bse);

		if (Objects.nonNull(businessTwitterAccounts) && (bse.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value()
				|| (Objects.nonNull(bse.getErrorCode()) && (bse.getErrorCode() == 65602 || bse.getErrorCode() == 65601)))
				|| (Objects.nonNull(pm) && pm.getMarkInvalid() == 1)) {
			// mark page invalid
			kafkaExternalService.markPageInvalid(SocialChannel.TWITTER.getName(), publishInfo.getExternalPageId());
		}
		failedPostLogging(publishInfo, bse.getCode(), pm.getErrorMessage(), pm.getBucket());
	}

	private void failedPostLogging(SocialPostPublishInfo publishInfo,Integer errorCode, String errorMessage, Integer bucketId) {

		if(Objects.isNull(bucketId)) {
			PermissionMapping permissionMappingUnknown = permissionMappingService.getDataByChannelAndPermissionCode(TWITTER, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
			publishInfo.setFailureReason(permissionMappingUnknown.getErrorMessage());
			publishInfo.setBucket(permissionMappingUnknown.getBucket());
		} else {
			publishInfo.setFailureReason(errorMessage);
			publishInfo.setFailureCode(errorCode);
			publishInfo.setBucket(bucketId);
		}
		publishInfo.setIsPublished(2);

		socialPostInfoRepository.saveAndFlush(publishInfo);
		kafkaExternalService.publishSocialPostEvent(publishInfo);
	}

	public TweetInfo postVideo(TwitterCreds creds, TwitterData data, SocialPostPublishInfo publishInfo, BusinessTwitterAccounts twitterAccount) throws Exception {
		LOGGER.info("postVideo method called for publish info id: {}", publishInfo.getId());
		TweetCreateRequest tweetCreateRequest = createTweetRequest(data);
		com.twitter.clientlib.model.TweetCreateResponse tweetCreateResponse = uploadTwitterVideo(creds, data.getMediaData().get(0),
				true, publishInfo, twitterAccount, tweetCreateRequest, twitterAccount.getAccountId());
		return MapTwitterData(tweetCreateResponse);
	}

	private TweetCreateRequest createTweetRequest(TwitterData data) {
		TweetCreateRequest tweetRequest = new TweetCreateRequest();
		tweetRequest.setText(data.getText());

		if (data.getInReplyToStatusId() != null) {
			TweetCreateRequestReply tweetCreateRequestReply= new TweetCreateRequestReply();
			tweetCreateRequestReply.setInReplyToTweetId(data.getInReplyToStatusId());
			tweetRequest.setReply(tweetCreateRequestReply);
		}
		if(Objects.nonNull(data.getLocationId())){
			TweetCreateRequestGeo geo= new TweetCreateRequestGeo();
			geo.setPlaceId(data.getLocationId());
			tweetRequest.setGeo(geo);
		}
		if(Objects.nonNull(data.getQuoteTweetId())) {
			tweetRequest.setQuoteTweetId(data.getQuoteTweetId());
		}
		return tweetRequest;
	}

	private TweetInfo MapTwitterData(com.twitter.clientlib.model.TweetCreateResponse twitterResponse) {
		TweetInfo tweetInfo= new TweetInfo();
		if(ObjectUtils.isEmpty(twitterResponse) ||ObjectUtils.isEmpty(twitterResponse.getData()) ){
			return tweetInfo;
		}
		tweetInfo.setId(twitterResponse.getData().getId());
		tweetInfo.setText(twitterResponse.getData().getText());
		return tweetInfo;
	}
	@Override
	public boolean unRetweet(Business business, BusinessTwitterAccounts twitterAccounts, String statusId) throws Exception {
		TwitterCreds creds = twitterCreds(business, twitterAccounts);
		try {
			return twitterService.undoRetweetV2(creds, statusId, String.valueOf(twitterAccounts.getProfileId()));
		}catch (Exception e){
			LOGGER.info("Exception occurred while undo retweet operation for feedId:{}",statusId);
			return false;
		}
	}
	
	@Override
	public ActivityResponse shareReview(String reviewContent, Integer businessId,  BusinessTwitterAccounts twitterAccounts) throws Exception {
		try {
			Business business = businessRepo.findOne(businessId);
			TwitterCreds creds = twitterCreds(business, twitterAccounts);
			TwitterData data = new TwitterData(reviewContent);
			TweetInfo status=postTwitterData(creds,data);
			status.setTweetUrl(twitterAccounts.getProfileUrl() + STATUS + status.getId());
		//	TweetInfo status = twitterService.post(creds, data);
			//TODO TwitterV2 Migration Changes will impact getFollowerCount as this data is not retrieved via API
			return new ActivityResponse(status.getId(), status.getTweetUrl(), status.getFollowerCount());
		} catch (BirdeyeSocialException ex) {
			if (Objects.nonNull(ex.getCode()) && (ex.getCode() == ErrorCodes.UNAUTHORIZED_OR_EXPIRED_TOKEN.value())) {
				commonService.handleExpiredTokenForTwitter(twitterAccounts);
			}
			throw ex;
		}
	}

	@Override
	public String getRedirectUrl(String sessionToken) throws Exception {
		SessionToken sessionTokenObj = sessionTokenRepository.findBySessionToken(sessionToken);
		ConsumerTokenAndSecret consumer = commonService.getAppKeyAndToken(sessionTokenObj.getBusiness(), "twitter");
		return twitterService.getRedirectUrl(consumer.getToken(), consumer.getSecret());
	}
	
	@Override
	public void generatedPermanentToken(String sessionToken, String tempAccessToken, String authToken, String authSecret) throws Exception {
		SessionToken sessionTokenObj = sessionTokenRepository.findBySessionToken(sessionToken);
		ConsumerTokenAndSecret consumer = commonService.getAppKeyAndToken(sessionTokenObj.getBusiness(), "twitter");
		twitterService.generateAccess(consumer.getToken(), consumer.getSecret(), tempAccessToken, authToken, authSecret);
	}
	
	/**
	 *
	 * API to reconnect twitter account
	 *
	 *
	 */
	@Override
	public void reconnectTwitterAccount(String pageIdStr, String tempAccessToken, String authToken, String authSecret, Integer userId) throws Exception {
		if (StringUtils.isEmpty(pageIdStr)) {
			LOGGER.error("[Twitter Setup] SocialPostTwitterServiceImpl reconnectTwitterAccount :: profile id {}.", pageIdStr);
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Profile Id is empty.");
		}
	}
	
	public boolean isTwitterProfileValidAndSameAsInTwitterPage(TwitterCreds creds, BusinessTwitterPage businessTwitterPage) {
		if (creds == null || businessTwitterPage == null) {
			LOGGER.error("[Social Setup] SocialPostTwitterServiceImpl isTwitterProfileValidAndSameAsInTwitterPage :: Creds are null");
			return false;
		}
		TwitterUserInfo twitterUser = twitterService.getAccountInfoV2(creds,businessTwitterPage.getHandle().substring(1));
		if (twitterUser == null) {
			LOGGER.error("[Social Setup] SocialPostTwitterServiceImpl isTwitterProfileValidAndSameAsInTwitterPage :: Twitter user is null for page id {}", businessTwitterPage.getId());
			return false;
		}
		int isBothProfileIdsEqual = businessTwitterPage.getProfileId().compareTo(twitterUser.getProfileId());
		if (isBothProfileIdsEqual == 0) {
			return true;
		}
		LOGGER.error("[Social Setup] SocialPostTwitterServiceImpl isTwitterProfileValidAndSameAsInTwitterPage :: Logged in profile {} and integrated profile {} are different",
				businessTwitterPage.getProfileId(), twitterUser.getProfileId());
		return false;
	}
	
	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialPostTwitterService#submitFetchPageRequest(java.lang.Long, java.lang.Integer, java.lang.String, java.lang.String, java.lang.String)
	 */
	@Override
	public void submitFetchPageRequest(Long businessId, Integer birdeyeUserId, String requestToken, String requestSecret,
									   String oauthVerifier, String type) throws TwitterException {

		LOGGER.info("[Twitter Setup] Request Received to generate twitter access token");
		
		Business business = businessRepo.findByBusinessId(businessId);
		if (business == null) {
			LOGGER.error("[Twitter Setup] Business not found for Business id {}", businessId);
			throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND);
		}
		String key = SocialChannel.TWITTER.getName().concat(String.valueOf(business.getBusinessId()));
		BusinessGetPageRequest request = (ENTERPRISE.equalsIgnoreCase(type))?
				businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId, SocialChannel.TWITTER.getName(),CONNECT):
				businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(businessId, SocialChannel.TWITTER.getName(),CONNECT);
		if (redisService.tryToAcquireLock(key)) {
			List<String> statusList = Arrays.asList(Status.INITIAL.getName(), Status.FETCHED.getName());
			if (Objects.isNull(request) || !statusList.contains(request.getStatus())) {
				pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),CONNECT,Status.INITIAL.getName(),businessId);
				WebsiteDomainInfo websiteDomainInfo = businessService.getBusinessDomain(business.getId());
				SocialAppCredsInfo domainInfo = socialAppService.getTwitterAppSettings(websiteDomainInfo.getId(), websiteDomainInfo.getDomainName());
				if (domainInfo == null) {
					LOGGER.error("[Twitter Setup] Consumer Token and Secret not found for Business id {}", businessId);
					redisService.release(key);
					LOGGER.error("[Redis Lock] (Twitter) Lock released for business {}", businessId);
					pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),CONNECT,Status.COMPLETE.getName(),businessId);
					throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, TOKEN_AND_SECRET_NOT_FOUND);
				}

				Configuration config = new ConfigurationBuilder().build();
				Twitter twitter = new TwitterFactory(config).getInstance();
				twitter.setOAuthConsumer(domainInfo.getChannelClientId(), domainInfo.getChannelClientSecret());

				RequestToken reqTokenObj = new RequestToken(requestToken, requestSecret);
				AccessToken accessToken = null;
			try {
				accessToken = twitter.getOAuthAccessToken(reqTokenObj, oauthVerifier);
			} catch (Exception ex) {
				// Cleanup redis cache for error cases
				redisService.release(key);
				LOGGER.error("[Twitter Setup] Error occurred while Setting up Twitter, redis lock released {}",ex.getMessage());
				pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),CONNECT,Status.COMPLETE.getName(),businessId,true);
				throw new BirdeyeSocialException(ErrorCodes.TWITTER_CONNECT_ERROR, ex.getMessage() != null ? ex.getMessage() : "Error while connecting to Twitter");
			}
			if (accessToken != null)
				LOGGER.info("[Twitter Setup] Twitter Access Token Received  Token {}  TokenSecret {} ", accessToken.getToken(), accessToken.getTokenSecret());
			
			request = new BusinessGetPageRequest();
			request.setBirdeyeUserId(birdeyeUserId);
			request.setSocialUserId(String.valueOf(twitter.getId()));
			request.setChannel(SocialChannel.TWITTER.getName());
			if (ENTERPRISE.equals(type)) {
				request.setEnterpriseId(business.getBusinessId());
			} else {
				request.setResellerId(business.getBusinessId());
			}
			request.setPageCount(0);
			request.setStatus(Status.INITIAL.getName());
			request.setRequestType(CONNECT);
			request.setEmail("@" + twitter.getScreenName());
			businessGetPageReqRepo.saveAndFlush(request);
			
			//Saving Twitter Account Details Async
			fetchTwitterAccounts(request, twitter, accessToken,type);
			}else{
				pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),request.getRequestType(),request.getStatus(),businessId);
				LOGGER.info("[Twitter] BusinessGetPageRequest found with status init or fetched for enterprise id {}",businessId);
				redisService.release(key);
			}
		} else {
			LOGGER.info("[Redis Lock] (Twitter) Lock is already acquired for business {}", businessId);
			handleFailureLock(request,SocialChannel.TWITTER.getName(),key,businessId,CONNECT);
		}
	}
	
	@Override
	public OpenUrlFetchPageResponse submitFetchPageRequestForOpenURL(Long enterpriseId, ChannelAuthOpenUrlRequest authRequest) {
		String key = SocialChannel.TWITTER.getName().concat(String.valueOf(authRequest.getFirebaseKey()));
		boolean lock = redisService.tryToAcquireLock(key);
		LOGGER.info("[Redis Lock twitter Open url fetch page] Lock status : {}",lock);
		OpenUrlFetchPageResponse openUrlFetchPageResponse = new OpenUrlFetchPageResponse();
		if(lock){
//			Business business = businessRepo.findByBusinessId(enterpriseId);
//			if (business == null) {
//				LOGGER.error("[Twitter Setup] Business not found for Business id {}", enterpriseId);
//				throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND);
//			}
//
//			WebsiteDomainInfo websiteDomainInfo = businessService.getDefaultDomain(business.getId());
			SocialAppCredsInfo domainInfo = socialAppService.getTwitterAppSettingsV2();
			if (domainInfo == null) {
				LOGGER.error("[Twitter Setup] Consumer Token and Secret not found for Business id {}", enterpriseId);
				redisService.release(key);
				LOGGER.error("[Redis Lock] (Twitter) Lock released for business {}", enterpriseId);
				throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, TOKEN_AND_SECRET_NOT_FOUND);
			}

			Configuration config = new ConfigurationBuilder().build();
			Twitter twitter = new TwitterFactory(config).getInstance();
			twitter.setOAuthConsumer(domainInfo.getChannelClientId(), domainInfo.getChannelClientSecret());
			RequestToken reqTokenObj = new RequestToken(authRequest.getRequestToken(), authRequest.getRequestSecret());
			try {
				AccessToken	accessToken = twitter.getOAuthAccessToken(reqTokenObj, authRequest.getOauthVerifier());
				BusinessGetPageOpenUrlRequest request = submitBusinessGetPageOpenUrl(twitter,enterpriseId,accessToken,authRequest.getFirebaseKey());
				socialProxyHandler.runInAsync(() -> {
					fetchTwitterAccountsOpenUrl(request,twitter,accessToken,authRequest);
					return true;
				});
			} catch (Exception ex) {
				handleCleanupRedisForOpenurl(key, authRequest, enterpriseId);
			}
		}else{
			LOGGER.info("Could not acquire redis lock for twitter open url fetch page for key {} ", key);
		}
		openUrlFetchPageResponse.setFirebaseKey(authRequest.getFirebaseKey());
		LOGGER.info("API response for twitter open url request for enterprise {} : {}",enterpriseId,openUrlFetchPageResponse.toString());
		return openUrlFetchPageResponse;
	}

	private BusinessGetPageOpenUrlRequest submitBusinessGetPageOpenUrl(Twitter twitter, Long enterpriseId, AccessToken accessToken, String firebaseKey) throws TwitterException {
		BusinessGetPageOpenUrlRequest request = new BusinessGetPageOpenUrlRequest();
		request.setSocialUserId(String.valueOf(twitter.getId()));
		request.setChannel(SocialChannel.TWITTER.getName());
		request.setEnterpriseId(enterpriseId);
		request.setPageCount(0);
		request.setStatus(Status.INITIAL.getName());
		request.setRequestType(CONNECT);
		request.setUserAccessToken(accessToken.getToken());
		request.setEmail("@" + twitter.getScreenName());
		request.setFirebaseKey(firebaseKey);
		return businessGetPageOpenUrlReqRepo.save(request);
	}

	public void fetchTwitterAccountsOpenUrl(BusinessGetPageOpenUrlRequest request, Twitter twitter, AccessToken accessToken,ChannelAuthOpenUrlRequest authRequest){
		String key = SocialChannel.TWITTER.getName().concat(String.valueOf(request.getEnterpriseId()));
		Set<String> oldRequestIds = new HashSet<>();
		try{
			LOGGER.info("[Twitter Setup] Fetching Twitter Account Details for Profile id and enterprise {} {}", twitter.getId(),request.getEnterpriseId());
			Integer pageCount = 0;
			Integer	totalCount = 0;
			List<BusinessTwitterAccounts> existingAccounts = socialTwitterRepo.findByProfileId(twitter.getId());
			if (CollectionUtils.isNotEmpty(existingAccounts)){
				existingAccounts.stream().forEach(acc -> {
					oldRequestIds.add(acc.getRequestId());
					acc.setRequestId(request.getId());
					acc.setIsValid(1);
					acc.setInvalidType(null);
					acc.setUpdatedBy(null);
					commonService.sendTwitterSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(acc), null, acc.getBusinessId(), acc.getEnterpriseId());
				});
				socialTwitterRepo.save(existingAccounts);
				existingAccounts.forEach(page->commonService.uploadPageImageToCDN(page));
				socialTwitterRepo.flush();
				if(CollectionUtils.isNotEmpty(oldRequestIds)) {// send event to check invalid get page requests and mark them as cancelled
					kafkaProducer.sendObjectV1(VALIDATE_PAGE_REQUEST, new CheckInvalidGetPageState(TWITTER, oldRequestIds));
				}
			} else {
				pageCount = saveTwitterAccount(request.getId(),null, twitter, accessToken, ENTERPRISE);
			}

			if(CollectionUtils.isNotEmpty(existingAccounts)) {
				totalCount += existingAccounts.size() + pageCount;
			} else {
				totalCount = pageCount;
			}

			request.setPageCount(pageCount);
			request.setTotalPages(totalCount);

			if(Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0){
				request.setStatus(Status.NO_PAGES_FOUND.getName());
				nexusService.updateMapInFirebase("socialOpenUrl/"+ authRequest.getFirebaseKey(),authRequest.getFirebaseKey() ,Status.NO_PAGES_FOUND.getName());
			}else{
				request.setStatus(Status.FETCHED.getName());
				nexusService.updateMapInFirebase("socialOpenUrl/"+ authRequest.getFirebaseKey(),authRequest.getFirebaseKey() ,Status.FETCHED.getName());
			}
			redisService.release(key);
			businessGetPageOpenUrlReqRepo.saveAndFlush(request);
		}catch (Exception ex){
			nexusService.updateMapInFirebase("socialOpenUrl/"+ authRequest.getFirebaseKey(),authRequest.getFirebaseKey() ,Status.CANCEL.getName());
			redisService.release(key);
			LOGGER.error("[Redis Lock] (Twitter) Lock released open url for business {}, error {}", request.getEnterpriseId(), ex);
			BusinessGetPageOpenUrlRequest req = checkInProgressRequestsOpenUrl(request.getEnterpriseId());
			if (req != null) {
				req.setStatus(Status.CANCEL.getName());
				businessGetPageOpenUrlReqRepo.saveAndFlush(req);
			}
		}
	}

	@Async
	@Override
	public void fetchTwitterAccounts(BusinessGetPageRequest request, Twitter twitter, AccessToken accessToken, String type) throws TwitterException {
		LOGGER.info("[Twitter Setup] Fetching Twitter Account Details for Profile id {}", twitter.getId());
		Integer pageCount = 0;
		Integer	totalCount = 0;
		Long parentId=Constants.ENTERPRISE.equals(type)?request.getEnterpriseId():request.getResellerId();
		String key = SocialChannel.TWITTER.getName().concat(String.valueOf(parentId));
		try{
			List<BusinessTwitterAccounts> existingAccounts = socialTwitterRepo.findByProfileId(twitter.getId());
			Set<String> oldRequestIds = new HashSet<>();
			if (CollectionUtils.isNotEmpty(existingAccounts)){
				existingAccounts.stream().forEach(acc -> {
					oldRequestIds.add(acc.getRequestId());
					acc.setRequestId(request.getId().toString());
					acc.setUpdatedBy(request.getBirdeyeUserId());

					commonService.sendTwitterSetupAuditEvent(SocialSetupAuditEnum.UPDATE_PAGE.name(), Arrays.asList(acc),
							request.getBirdeyeUserId().toString(), acc.getBusinessId(),parentId);
				});
				List<Long> profileIds = existingAccounts.stream().map(BusinessTwitterAccounts::getProfileId).collect(Collectors.toList());
				pushToKafkaForValidity(TWITTER, profileIds);
				socialTwitterRepo.save(existingAccounts);
				existingAccounts.forEach(page->commonService.uploadPageImageToCDN(page));
				socialTwitterRepo.flush();
				if(CollectionUtils.isNotEmpty(oldRequestIds)) {// send event to check invalid get page requests and mark them as cancelled
					kafkaProducer.sendObjectV1(VALIDATE_PAGE_REQUEST, new CheckInvalidGetPageState(TWITTER, oldRequestIds));
				}
			} else {
				pageCount = saveTwitterAccount(request.getId().toString(),request.getBirdeyeUserId(), twitter, accessToken,type);
			}

			if(CollectionUtils.isNotEmpty(existingAccounts)) {
				totalCount += existingAccounts.size() + pageCount;
			} else {
				totalCount = pageCount;
			}

			request.setPageCount(pageCount);
			request.setTotalPages(totalCount);
			if(Objects.isNull(request.getTotalPages()) || request.getTotalPages() == 0){

				request.setStatus(Status.NO_PAGES_FOUND.getName());
				redisService.release(key);
			}else{
				request.setStatus(Status.FETCHED.getName());
			}
			pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),request.getRequestType(),request.getStatus(),parentId);
			businessGetPageReqRepo.saveAndFlush(request);
		}catch (Exception ex){
			redisService.release(key);
			LOGGER.error("[Redis Lock] (Twitter) Lock released for business {}, error {}", parentId, ex);
			//Cleanup business get pages request table
			BusinessGetPageRequest req = (ENTERPRISE.equalsIgnoreCase(type))?
					businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(parentId,SocialChannel.TWITTER.getName(),
							CONNECT):
					businessGetPageService.findLastRequestByResellerIdAndChannelAndRequestType(parentId, SocialChannel.TWITTER.getName(),CONNECT);

			if (req != null) {
				req.setStatus(Status.CANCEL.getName());
				businessGetPageReqRepo.saveAndFlush(req);
				pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),req.getRequestType(),Status.COMPLETE.getName(),parentId,true);
			}else{
				pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),"connect",Status.COMPLETE.getName(), parentId,true);
			}
		}
	}


	private TwitterV2ProfileResponse getUserProfileResponse(Long profileId, AccessToken accessToken) {
		SocialAppCredsInfo domainInfo = socialAppService.getTwitterAppSettingsV2();
		TwitterCreds twitterCreds = new TwitterCreds(domainInfo.getChannelClientId(),domainInfo.getChannelClientSecret(),accessToken.getToken(),accessToken.getTokenSecret());
		GetUserProfileResponse userProfileResponse = twitterService.getProfileDataFromIdV2(twitterCreds,profileId);
		if(Objects.nonNull(userProfileResponse) && Objects.nonNull(userProfileResponse.getData())) {
			return userProfileResponse.getData();
		}
		throw new BirdeyeSocialException("Unable to get userInfo from twitter");
	}

	private Integer saveTwitterAccount(String requestId,Integer userId, Twitter twitter, AccessToken accessToken, String type) throws TwitterException {
		BusinessTwitterAccounts twitterAccount = new BusinessTwitterAccounts();
		// for enterprise setup during page fetching no need to save enterprise Id as during connect page api (selected page save) we will save enterprise id.
		twitterAccount.setAccessToken(accessToken.getToken());
		twitterAccount.setAccessSecret(accessToken.getTokenSecret());
		twitterAccount.setProfileUrl(TWITTER_COM + twitter.getScreenName());
		twitterAccount.setProfileId(twitter.getId());
		twitterAccount.setAccountPermissions("read,write,direct_message");
		TwitterV2ProfileResponse profileResponse = getUserProfileResponse(twitterAccount.getProfileId(),accessToken);
		twitterAccount.setProfilePicUrl(profileResponse.getProfileImageUrl());
		twitterAccount.setHandle("@" + twitter.getScreenName());
		twitterAccount.setLocation(profileResponse.getLocation());
		twitterAccount.setName(profileResponse.getName());
		twitterAccount.setIsSelected(0);
		twitterAccount.setIsValid(1);
		twitterSocialAccountService.updateXValidityType(twitterAccount);
		twitterAccount.setInvalidType(null);
		twitterAccount.setRequestId(requestId);
		twitterAccount.setCreatedBy(userId);
		twitterAccount.setUpdatedBy(userId);
		twitterAccount.setUserEmailId("@" + twitter.getScreenName());

		socialTwitterRepo.saveAndFlush(twitterAccount);
		commonService.uploadPageImageToCDN(twitterAccount);

		String auditUserId = Objects.nonNull(profileResponse.getId()) ? profileResponse.getId() : null;
		commonService.sendTwitterSetupAuditEvent(SocialSetupAuditEnum.ADD_PAGES.name(), Arrays.asList(twitterAccount), auditUserId, twitterAccount.getBusinessId(),ENTERPRISE.equals(type)?twitterAccount.getEnterpriseId():twitterAccount.getResellerId());
		return 1;
	}

	private List<BusinessGetPageRequest> getRequestForBusiness(Long businessId, String status, String requestType, String type) {

		return ENTERPRISE.equals(type)?businessGetPageReqRepo.findByEnterpriseIdAndStatusAndChannelAndRequestType(businessId, status, SocialChannel.TWITTER.getName(), requestType)
				:businessGetPageReqRepo.findByResellerIdAndStatusAndChannelAndRequestType(businessId, status, SocialChannel.TWITTER.getName(), requestType);
	}

	@Deprecated
	private Map<String, List<ChannelAccountInfo>> getTwitterAccounts(Long businessId, String requestType) {
		Map<String, List<ChannelAccountInfo>> pageType = new HashMap<>();
		List<BusinessTwitterAccounts> fetchedPages = getListOfPages(businessId, requestType);
		List<ChannelAccountInfo> twitterPages = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(fetchedPages)) {
			getTwitterAcntsInfo(fetchedPages, twitterPages);
				pageType.put(SocialChannel.TWITTER.getName(), twitterPages);
		}
		return pageType;
	}

	@Override
	public Map<String, List<ChannelAccountInfo>> getTwitterAccounts(BusinessGetPageRequest businessGetPageRequest) {
		Map<String, List<ChannelAccountInfo>> pageType = new HashMap<>();
		List<BusinessTwitterAccounts> fetchedPages = socialTwitterRepo.findByRequestId(businessGetPageRequest.getId().toString());
		List<ChannelAccountInfo> twitterPages = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(fetchedPages)) {
			getTwitterAcntsInfo(fetchedPages, twitterPages);
			pageType.put(SocialChannel.TWITTER.getName(), twitterPages);
		}
		return pageType;
	}

	@Override
	@Retryable(value = BirdeyeSocialException.class,maxAttempts = 2,backoff = @Backoff(delay = 1000))
	public SocialMentionsData searchMentionData(String search, Integer businessId, Long enterpriseId, String nextToken){
		List<SocialMentionData> socialMentionDataList;
		List<TwitterResponse> twitterResponses = null;
		String nextPageToken = null;
		try {
			List<BusinessTwitterAccounts> twitterAccountsList =
					socialTwitterRepo.findFirstByEnterpriseIdOrResellerIdAndIsSelectedAndIsValid(enterpriseId,
							1,1,new org.springframework.data.domain.PageRequest(0, 1));
			if(Objects.isNull(twitterAccountsList)) {
				throw new SocialBirdeyeException(ErrorCodes.INVALID_PAGE,INVALID_PAGE);
			}
			BusinessTwitterAccounts businessTwitterAccount;
			for(int i=0; i<Math.min(TWITTER_MENTION_PAGES_RETRY, twitterAccountsList.size()) ; i++) {
				businessTwitterAccount = twitterAccountsList.get(i);
				try {
					TwitterCreds creds = twitterCredsV2(businessTwitterAccount.getAccessToken(),businessTwitterAccount.getAccessSecret());
					TwitterUserResponse twitterUserResponse = twitterService.getMentionData(search,creds, nextToken);
					if(Objects.nonNull(twitterUserResponse) && CollectionUtils.isNotEmpty(twitterUserResponse.getData())) {
						twitterResponses = twitterUserResponse.getData();
						if(Objects.nonNull(twitterUserResponse.getMeta()) && twitterUserResponse.getData().size() >= 5) {
							nextPageToken = twitterUserResponse.getMeta().getNextToken();
						}
						break;
					}
				} catch (Exception e) {
					LOGGER.info("error while fetching data for twitter: {}, retrying", e.getMessage());
				}
			}
		}catch (BirdeyeSocialException e){
			LOGGER.error("Exception occurred while fetching mentions : {}",e.getMessage());
			cacheService.clearCacheByName(Constants.TWITTER_TOKEN_CACHE);
			LOGGER.info("Cache cleared for cache name : {}",Constants.TWITTER_TOKEN_CACHE);
			throw new BirdeyeSocialException(e.getCode(),"Unable to fetch twitter mentions : "+e.getMessage());
		}catch (Exception e){
			LOGGER.info("Unable to locate mention");
			throw new BirdeyeSocialException("Unable to fetch twitter mentions : "+e.getMessage());
		}
		socialMentionDataList = setSearchResponse(twitterResponses);
		return new SocialMentionsData(socialMentionDataList, nextPageToken);
	}

	private TwitterCreds twitterCredsV2(String accessToken,String accessSecret){
		SocialAppCredsInfo domainInfo = socialAppService.getTwitterAppSettingsV2();
		return new TwitterCreds(domainInfo.getChannelClientId(), domainInfo.getChannelClientSecret(), accessToken, accessSecret);
	}

	private String fetchAccessToken(Integer businessId){
		LOGGER.info("Generate access token for twitter for business id {}",businessId);
		try {
			WebsiteDomainInfo websiteDomainInfo = businessService.getDefaultDomain(businessId);
			SocialAppCredsInfo domainInfo = socialAppService.getTwitterAppSettings(websiteDomainInfo.getId(), websiteDomainInfo.getDomainName());
			return twitterService.getAccessTokenFromAppCreds(domainInfo.getChannelClientId(), domainInfo.getChannelClientSecret());
		}catch (BirdeyeSocialException e){
			throw new BirdeyeSocialException(e.getCode(),"Unable to fetch access token : "+e.getMessage());
		}catch (Exception e){
			throw new BirdeyeSocialException("Unable to fetch access token: "+e.getMessage());
		}
	}

	private List<SocialMentionData> setSearchResponse(List<TwitterResponse> twitterResponses) {
		List<SocialMentionData> socialMentionDataList = new ArrayList<>();
		if(CollectionUtils.isEmpty(twitterResponses)){
			LOGGER.info("No data found to set in response for mentions twitter");
			return socialMentionDataList;
		}
		twitterResponses.forEach(response -> {
			SocialMentionData data = new SocialMentionData();
			data.setId(response.getId());
			data.setUserName(response.getUsername());
			data.setName(response.getName());
			data.setProfilePictureUrl(response.getProfileImageUrl());
			data.setIsVerified(response.getVerified());
			data.setAddress(response.getLocation());
			data.setProfileUrl("https://x.com/" + response.getUsername());
			data.setFollowerCount(Objects.nonNull(response.getPublicMetrics()) ? response.getPublicMetrics().getFollowerCount() : 0 );
			socialMentionDataList.add(data);
		});
		return socialMentionDataList;
	}

	// need to remove this as not being used from UI
	@Override
	@Deprecated
	@Retryable(value = BirdeyeSocialException.class,maxAttempts = 2,backoff = @Backoff(delay = 1000))
	public List<SocialLocationTagResponse> searchLocationTagData(String search, Integer businessId){
		LOGGER.info("Search location for string : {}",search);
		List<SocialLocationTagResponse> socialLocationTagResponseList;
		TwitterLocationTagResponse result = new TwitterLocationTagResponse();
		try {
			String accessToken = fetchAccessToken(businessId);
			result = twitterService.getLocations(search,accessToken);
		}catch (BirdeyeSocialException e){
			LOGGER.error("Exception occurred while getting the data for location search :{}",e.getMessage());
			cacheService.clearCacheByName(Constants.TWITTER_TOKEN_CACHE);
			LOGGER.info("Cache cleared for cache name : {}",Constants.TWITTER_TOKEN_CACHE);
			throw new BirdeyeSocialException(e.getCode(),"Unable to fetch twitter locations : "+e.getMessage());
		}catch (Exception e){
			LOGGER.info("Unable to locate mention");
			throw new BirdeyeSocialException("Unable to fetch twitter locations : "+e.getMessage());
		}
		socialLocationTagResponseList = setLocationSearchResponse(result);
		return socialLocationTagResponseList;
	}
	public TwitterLiteDto findByRequestIdOrderByCreatedAt(String requestId, org.springframework.data.domain.PageRequest pageRequest) {
		Page<BusinessTwitterAccounts> page = socialTwitterRepo.findByRequestId(requestId,pageRequest);
		return getTwitterLiteDto(page);
	}


	@Override
	public Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer pageNumber, Integer size, String search) {
		Map<String, Object> pageData = new HashMap<>();
		Map<String, List<ChannelAccountInfoLite>> pageType = new HashMap<>();
		List<ChannelAccountInfoLite> managed = new ArrayList<>();

		// fetchedPages =  defined pageNumber and pageSize to get pagableResult
		TwitterLiteDto fetchedPages = com.birdeye.social.utils.StringUtils.isNotEmpty(search) ?
				convertToTwitterObject(search,new org.springframework.data.domain.PageRequest(pageNumber, size, Sort.Direction.ASC, IS_SELECTED),
						businessGetPageRequest.getId().toString()):
				findByRequestIdOrderByCreatedAt(businessGetPageRequest.getId().toString(),new org.springframework.data.domain.PageRequest(pageNumber, size,Sort.Direction.ASC, IS_SELECTED));
		if (CollectionUtils.isNotEmpty(fetchedPages.getPageLites())) {
			fetchedPages.getPageLites().forEach(page -> managed.add(getTwitterAcntInfoLite(page,businessGetPageRequest.getEmail())));
			if (CollectionUtils.isNotEmpty(managed))
				pageType.put(SocialChannel.TWITTER.getName(), managed);
		}
		pageData.put("pageType", pageType);
		pageData.put("totalCount", fetchedPages.getTotalElements());
		pageData.put("pageCount", fetchedPages.getTotalPages());
		return pageData;
	}

	private TwitterLiteDto convertToTwitterObject(String search, org.springframework.data.domain.PageRequest pageRequest, String requestId) {
		Page<BusinessTwitterAccounts> page = searchWithRequestId(search,pageRequest,requestId);
		return getTwitterLiteDto(page);
	}

	private TwitterLiteDto getTwitterLiteDto(Page<BusinessTwitterAccounts> page) {
		TwitterLiteDto twitterLiteDto = new TwitterLiteDto();
		if(Objects.nonNull(page)) {
			twitterLiteDto.setPageLites(page.getContent());
			twitterLiteDto.setTotalElements(page.getTotalElements());
			twitterLiteDto.setTotalPages(page.getTotalPages());
		}
		return twitterLiteDto;
	}

	private Page<BusinessTwitterAccounts> searchWithRequestId(String search, org.springframework.data.domain.PageRequest pageRequest,
															  String requestId) {
		Specification<BusinessTwitterAccounts> twitterSpec = Specifications.where((xSpecification.hasPageName(search))).
				and(xSpecification.hasRequestId(requestId));

		return socialTwitterRepo.findAll(twitterSpec,pageRequest);
	}

	private List<SocialLocationTagResponse> setLocationSearchResponse(TwitterLocationTagResponse result) {
		List<SocialLocationTagResponse> socialLocationTagResponseList = new ArrayList<>();
		if(Objects.isNull(result) || Objects.isNull(result.getResult()) || CollectionUtils.isEmpty(result.getResult().getPlaces())){
			LOGGER.info("No data found to set in response for location tag twitter");
			return socialLocationTagResponseList;
		}
		result.getResult().getPlaces().forEach(place -> {
			SocialLocationTagResponse response = new SocialLocationTagResponse();
			response.setId(place.getId());
			response.setCountry(place.getCountry());
			response.setPlaceName(place.getName());
			socialLocationTagResponseList.add(response);
		});
		return socialLocationTagResponseList;
	}


	@Deprecated
	private List<BusinessTwitterAccounts> getListOfPages(Long businessId, String requestType) {
		List<BusinessTwitterAccounts> pages = null;
		List<BusinessGetPageRequest> fetchedRequests = getRequestForBusiness(businessId, Status.FETCHED.getName(), requestType, ENTERPRISE);
		if (CollectionUtils.isNotEmpty(fetchedRequests)) {
			if (fetchedRequests.size() > 1) {
				LOGGER.error("multiple fetched rows are not possible for any business");
				throw new BirdeyeSocialException("fetched accounts for multiple social users");
			}
			BusinessGetPageRequest getRequest = fetchedRequests.get(0);
			pages = socialTwitterRepo.findByRequestId(getRequest.getId().toString());
			if (CollectionUtils.isEmpty(pages)) {
				getRequest.setStatus(Status.COMPLETE.getName());
				getRequest.setUpdated(new Date());
				businessGetPageReqRepo.saveAndFlush(getRequest);
				releaseLock(getRequest.getChannel(), getRequest.getEnterpriseId());
			}
		}
		return pages;
	}
	
	private void getTwitterAcntsInfo(List<BusinessTwitterAccounts> fetchedPages, List<ChannelAccountInfo> twitterPages) {
		fetchedPages.stream().forEach(page -> twitterPages.add(getTwitterAcntInfo(page)));
	}
	
	private ChannelAccountInfo getTwitterAcntInfo(BusinessTwitterAccounts fetchedPage) {
		ChannelAccountInfo accountInfo = new ChannelAccountInfo();
		accountInfo.setId(String.valueOf(fetchedPage.getProfileId()));
		accountInfo.setPageName(fetchedPage.getName());
		accountInfo.setLink(fetchedPage.getProfileUrl());
		accountInfo.setImage(fetchedPage.getProfilePicUrl());
		accountInfo.setHandle(fetchedPage.getHandle());
		Validity validity = twitterSocialAccountService.fetchValidityAndErrorMessage(fetchedPage);
		accountInfo.setValidType(validity.getValidType());
		accountInfo.setErrorCode(validity.getErrorCode());
		accountInfo.setErrorMessage(validity.getErrorMessage());
		accountInfo.setDisabled((fetchedPage.getIsSelected() != null && fetchedPage.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
		accountInfo.setAddress(fetchedPage.getLocation());

		return accountInfo;
	}

	private ChannelAccountInfoLite getTwitterAcntInfoLite(BusinessTwitterAccounts fetchedPage, String userEmail) {
		ChannelAccountInfoLite accountInfo= new ChannelAccountInfoLite();
		accountInfo.setId(String.valueOf(fetchedPage.getProfileId()));
		accountInfo.setPageName(fetchedPage.getName());
		accountInfo.setLink(fetchedPage.getProfileUrl());
		accountInfo.setImage(fetchedPage.getProfilePicUrl());
		accountInfo.setHandle(fetchedPage.getHandle());
		Validity validity = twitterSocialAccountService.fetchValidityAndErrorMessage(fetchedPage);
		accountInfo.setValidType(validity.getValidType());
		accountInfo.setErrorCode(validity.getErrorCode());
		accountInfo.setErrorMessage(validity.getErrorMessage());
		accountInfo.setDisabled((fetchedPage.getIsSelected() != null && fetchedPage.getIsSelected() == 1) ? Boolean.TRUE : Boolean.FALSE);
		accountInfo.setAddress(fetchedPage.getLocation());
		accountInfo.setUserId(userEmail);
		accountInfo.setParentId(Objects.nonNull(fetchedPage.getResellerId())?fetchedPage.getResellerId():fetchedPage.getEnterpriseId());
		return accountInfo;
	}


		private void releaseLock(String channel, Long enterpriseId) {
		redisService.release(channel.concat(String.valueOf(enterpriseId)));
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialPostTwitterService#getConnectedPages(java.util.Map, java.lang.Long, java.lang.Integer, java.lang.Integer)
	 */
	@Deprecated //TODO "Remove if /{channel}/all is not required"
	@Override
	public ConnectedPages getConnectedPages(Map<Integer, BusinessEntity> idToBusinessMap, Long enterpriseId, Integer startIndex, Integer endIndex) {
		ConnectedPages connectedPage = new ConnectedPages();
		List<BusinessTwitterAccounts> connectedAccounts = socialTwitterRepo.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
		
		List<Long> profileIds = new ArrayList<>();
		Map<String, BusinessEntity> twitterAccountToBusinessMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(connectedAccounts)) {
			connectedAccounts.stream().forEach(acc -> profileIds.add(acc.getProfileId()));
			List<BusinessTwitterAccounts> existingPages = socialTwitterRepo.findByProfileIdIn(profileIds);
			existingPages.stream().forEach(page -> twitterAccountToBusinessMap.put(String.valueOf(page.getProfileId()), idToBusinessMap.get(page.getBusinessId())));
		}
		ChannelPageDetails accountInfo = getAccountInfo(connectedAccounts, twitterAccountToBusinessMap);
		Map<String, ChannelPageDetails> pageTypes = new HashMap<>();
		if (CollectionUtils.isNotEmpty(accountInfo.getPages())) {
			pageTypes.put(SocialChannel.TWITTER.getName(), accountInfo);
		}
		connectedPage.setPageTypes(pageTypes);
		return connectedPage;
	}

	@Override
	public ChannelSetupStatus.PageSetupStatus getPageSetupStatus(Long enterpriseId) {
		List<BusinessTwitterAccounts> twitterAccounts = socialTwitterRepo.findByEnterpriseId(enterpriseId);
		if ( twitterAccounts.isEmpty() ) {
			return ChannelSetupStatus.PageSetupStatus.NO_PAGES_FOUND;
		} else {
			// We only need to check if there are pages which have broken mapping
			// We don't need to check for unmapped pages here	
			for ( BusinessTwitterAccounts twitterAccount : twitterAccounts ) {
				if ( twitterAccount.getIsValid() != 1 ) {
					return ChannelSetupStatus.PageSetupStatus.DISCONNECTED_PAGES_FOUND;
				}
			}
		}
		return ChannelSetupStatus.PageSetupStatus.OK;
	}

	@Override
	public ChannelSetupStatus.PageSetupStatus getPageConnectionSetupStatus(Long enterpriseId) {
		List<BusinessTwitterAccounts> twitterAccounts = socialTwitterRepo.findByEnterpriseId(enterpriseId);
		if ( twitterAccounts.isEmpty() ) {
			return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
		} else {
			// We only need to check if there are pages which have broken mapping
			// We don't need to check for unmapped pages here
			for ( BusinessTwitterAccounts twitterAccount : twitterAccounts ) {
				if (Objects.nonNull(twitterAccount.getBusinessId())  && twitterAccount.getIsValid() == 1 ) {
					return ChannelSetupStatus.PageSetupStatus.VALID_INTEGRATION;
				}
			}
			return ChannelSetupStatus.PageSetupStatus.INVALID_INTEGRATION;
		}
	}

	@Override
	public BusinessIntegrationStatus.ChannelIntegrationInfo getPageIntegrationStatus(Integer businessId) {
		LOGGER.info("getPageIntegrationStatus {}", businessId);
		if (businessId != null) {
			// Get twitter mapping
			final List<BusinessTwitterAccounts> twitterLocList = socialTwitterRepo.findByBusinessId(businessId);
			if (CollectionUtils.isEmpty(twitterLocList)) {
				return null;
			}
			// Prepare response
			BusinessIntegrationStatus.ChannelIntegrationInfo channelInfo = new BusinessIntegrationStatus.ChannelIntegrationInfo();
			channelInfo.setPageId(String.valueOf(twitterLocList.get(0).getProfileId()));
			channelInfo.setValid(twitterLocList.get(0).getIsValid());
			channelInfo.setPageName(twitterLocList.get(0).getName());
			// Get Raw Twitter page
			//final List<BusinessTwitterAccounts> twitterLocRaw = socialTwitterRepo.findByProfileId(twitterLocList.get(0).getProfileId());
			/*
			 * if (CollectionUtils.isEmpty(twitterLocRaw)) { throw new
			 * BirdeyeSocialException(ErrorCodes.TWITTER_ACCOUNT_NOT_FOUND,
			 * "Raw Twitter page not found"); }
			 */
			if (twitterLocList.get(0).getRequestId() != null) {
				String requestId = twitterLocList.get(0).getRequestId();
				BusinessGetPageRequest getPageRequest = null;
				BusinessGetPageOpenUrlRequest getPageOpenUrlRequest = null;
					if(requestId.contains(OPEN_URL_REQUEST_PREFIX)) {
						getPageOpenUrlRequest = businessGetPageOpenUrlReqRepo.findOne(requestId);
					} else {
						getPageRequest = businessGetPageReqRepo.findOne(Integer.parseInt(requestId));
					}
					// Get emailId from get page request table
					if (getPageRequest != null) {
						channelInfo.setEmail(getPageRequest.getEmail());
					} else {
						channelInfo.setEmail(getPageOpenUrlRequest.getEmail());
					}
				}
				// Set firstname and lastName
				channelInfo.setUserName(twitterLocList.get(0).getName());
				channelInfo.setPageUrl(twitterLocList.get(0).getProfileUrl());
				return channelInfo;
			}
			return null;
		}

	@Override
	public ConnectedPages getPages(Long enterpriseId, PageConnectionStatus pageConnectionStatus) {
		ConnectedPages connectedPage = new ConnectedPages();
		List<BusinessTwitterAccounts> connectedAccounts = socialTwitterRepo.findByEnterpriseIdAndIsSelected(enterpriseId, 1);
		// For twitter, we only need to check the account info as we are not returning location mapping data
		ChannelPageDetails channelPageDetails = getPageDetailsFromAccount(connectedAccounts, pageConnectionStatus);
		Map<String, ChannelPageDetails> pageTypes = new HashMap<>();
		pageTypes.put(SocialChannel.TWITTER.getName(), channelPageDetails);
		connectedPage.setPageTypes(pageTypes);
		return connectedPage;
	}

	@Override
	public ConnectedPages getPagesForPostReconnect(PageConnectionStatus pageConnectionStatus, SocialPostPageConnectRequest request) {
		ConnectedPages connectedPage = new ConnectedPages();
		List<Long> longList = new ArrayList<>();
		if(Objects.nonNull(request)) {
			for (String str : request.getPageIds()) {
				longList.add(Long.parseLong(str));
			}
		}
		List<BusinessTwitterAccounts> connectedAccounts = socialTwitterRepo.findByProfileIdInAndIsSelected(longList, 1);
		// For twitter, we only need to check the account info as we are not returning location mapping data
		ChannelPageDetails channelPageDetails = getPageDetailsFromAccount(connectedAccounts, pageConnectionStatus);
		Map<String, ChannelPageDetails> pageTypes = new HashMap<>();
		pageTypes.put(SocialChannel.TWITTER.getName(), channelPageDetails);
		connectedPage.setPageTypes(pageTypes);
		return connectedPage;
	}

	private ChannelPageDetails getPageDetailsFromAccount(List<BusinessTwitterAccounts> connectedAccounts, PageConnectionStatus pageConnectionStatus) {
		List<ChannelPages> pageInfo = new ArrayList<>();
		connectedAccounts.forEach(account -> {
			ChannelPages completePageInfo = getAccountInfo(account, null);
			if ( pageConnectionStatus == PageConnectionStatus.CONNECTED &&
					completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName()) ) {
				pageInfo.add(completePageInfo);
			} else if ( pageConnectionStatus == PageConnectionStatus.DISCONNECTED && (
					completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.INVALID.getName()) ||
							completePageInfo.getValidType().equalsIgnoreCase(ValidTypeEnum.PARTIAL_VALID.getName())
					) ) {
				pageInfo.add(completePageInfo);
			} else if ( pageConnectionStatus == PageConnectionStatus.ALL ) {
				pageInfo.add(completePageInfo);
			}
		});
		return filterInvalidAndValidPage(pageInfo);
	}

	/**
	 * @param connectedAccounts
	 * @param twitterAccountToBusinessMap
	 * @return
	 */
	private ChannelPageDetails getAccountInfo(List<BusinessTwitterAccounts> connectedAccounts, Map<String, BusinessEntity> twitterAccountToBusinessMap) {
		List<ChannelPages> pageInfo = new ArrayList<>();
		connectedAccounts.stream().forEach(page -> pageInfo.add(getAccountInfo(page, twitterAccountToBusinessMap.get(String.valueOf(page.getProfileId())))));
		return filterInvalidAndValidPage(pageInfo);
	}

	/**
	 * @param page
	 * @param mappedBusiness
	 * @return
	 */
	private ChannelPages getAccountInfo(BusinessTwitterAccounts page, BusinessEntity mappedBusiness) {
		ChannelPages pageInfo = new ChannelPages();
		pageInfo.setId(String.valueOf(page.getProfileId()));
		pageInfo.setImage(page.getProfilePicUrl());
		pageInfo.setPageName(page.getName());
		pageInfo.setLink(page.getProfileUrl());
		if (StringUtils.isNotBlank(page.getHandle()) && !StringUtils.startsWith(page.getHandle(), "@")) {
			pageInfo.setHandle(StringUtils.join("@", page.getHandle()));
		} else {
			pageInfo.setHandle(page.getHandle());
		}
		Validity validity = twitterSocialAccountService.fetchValidityAndErrorMessage(page);
		pageInfo.setValidType(validity.getValidType());
		pageInfo.setErrorCode(validity.getErrorCode());
		pageInfo.setErrorMessage(validity.getErrorMessage());

		if (mappedBusiness != null) {
			pageInfo.setLocationId(mappedBusiness.getId());
			pageInfo.setLocationName(mappedBusiness.getAlias1() != null ? mappedBusiness.getAlias1() : mappedBusiness.getName());
		}
		pageInfo.setAddress(page.getLocation());
		
		return pageInfo;
	}

	/**
	 * @param pageInfo
	 * @return
	 */
	private ChannelPageDetails filterInvalidAndValidPage(List<ChannelPages> pageInfo) {
		ChannelPageDetails channelPageDetails = new ChannelPageDetails();
		List<ChannelPages> invalidPages = new ArrayList<>();
		List<ChannelPages> validPages = new ArrayList<>();
		for(ChannelPages page: pageInfo) {
			if(page.getValidType().equalsIgnoreCase(ValidTypeEnum.VALID.getName())) {
				validPages.add(page);
			} else {
				invalidPages.add(page);
			}
		}
		channelPageDetails.setDisconnected(invalidPages.size());
		invalidPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
		validPages.sort(Comparator.comparing(ChannelPages::getPageName, nullsFirst(Comparator.naturalOrder())));
		invalidPages.addAll(validPages);
		channelPageDetails.setPages(invalidPages);
		return channelPageDetails;
	}

	private void checkForUnMappedSmbPages(BusinessLiteDTO business, String type) {
		if (checkBusinessSMB(business)) {
			List<BusinessTwitterAccounts> existingPages = socialTwitterRepo.findByAccountId(business.getBusinessId());
			List<String> profileIds = existingPages.stream().filter(page -> page.getBusinessId() != null ).map(page -> page.getProfileId().toString()).collect(Collectors.toList());
			/*List<BusinessTwitterAccounts> mappedPage = socialTwitterRepo.findByProfileIdIn(profileIds);
			List<String> profileIdList = mappedPage.parallelStream().map(mappingPage -> mappingPage.getProfileId().toString())
					.collect(Collectors.toList());*/
			if(RESELLER.equals(type)){
				socialTwitterRepo.deleteByResellerIdAndProfileIdNotIn(business.getBusinessNumber(),profileIds);
			}
			else {
				if (CollectionUtils.isNotEmpty(profileIds)) {
					socialTwitterRepo.deleteByEnterpriseIdAndProfileIdNotIn(business.getBusinessNumber(), profileIds);
				} else {
					socialTwitterRepo.deleteByAccountId(business.getBusinessId());
				}
			}
		}
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialPostTwitterService#connectTwitterPagesV1(java.util.Map, java.lang.Long)
	 */
	@Override
	public ChannelPageInfo connectTwitterPagesV1(TwitterConnectAccountRequest twitterConnectAccountRequest) {
		List<String> accountIds = twitterConnectAccountRequest.getId();
		Long enterpriseId = twitterConnectAccountRequest.getBusinessId();
		Integer accountId= twitterConnectAccountRequest.getAccountId();
		String type= twitterConnectAccountRequest.getType();
		LOGGER.info("[Twitter Setup] connectTwitterPagesV1 : account ids : {}", accountIds);
		ChannelPageInfo channelAccountInfo = new ChannelPageInfo();
		List<BusinessGetPageRequest> request = getRequestForBusiness(enterpriseId, Status.FETCHED.getName(), CONNECT,type);
		if (CollectionUtils.isEmpty(request)) {
			LOGGER.error("[Twitter Setup] seems status has already changed");
			throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "seems status has already changed");
		} else if (request.size() > 1) {
			LOGGER.error("[Twitter Setup] multiple fetched rows are not possible for any business");
			throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "multiple rows with fetched status");
		} else {
			BusinessLiteDTO business = businessCoreService.getBusinessLiteByNumber(enterpriseId);
			checkForUnMappedSmbPages(business,type);
			BusinessGetPageRequest req = request.get(0);
			Long parentId= RESELLER.equals(type)?req.getResellerId():req.getEnterpriseId();
			req.setStatus(Status.COMPLETE.getName());
			req.setUpdated(new Date());
			businessGetPageReqRepo.saveAndFlush(req);
			pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),req.getRequestType(),Status.COMPLETE.getName(), parentId);

			List<Long> twitterAccountIds;
			if(Constants.RESELLER.equals(twitterConnectAccountRequest.getType()) && BooleanUtils.isTrue(twitterConnectAccountRequest.getSelectAll())
					&& StringUtils.isNotEmpty(twitterConnectAccountRequest.getSearchStr())) {
				twitterAccountIds = socialTwitterRepo.findAllByTwitterPageName(twitterConnectAccountRequest.getSearchStr(),req.getId().toString());
			}
			else if(Constants.RESELLER.equals(twitterConnectAccountRequest.getType()) && BooleanUtils.isTrue(twitterConnectAccountRequest.getSelectAll())){
				twitterAccountIds = socialTwitterRepo.findAllByRequestId(req.getId().toString());
			}else{
				twitterAccountIds = twitterConnectAccountRequest.getId().stream().map(s -> Long.parseLong(s)).collect(Collectors.toList());
			}


			LOGGER.info("pageIds extracted to process  :{}",twitterAccountIds);
			List<BusinessTwitterAccounts> twitterPages = getTwitterAccountsList(twitterAccountIds);

			twitterPages.stream().forEach(page -> {
				page.setIsSelected(1);
				if(RESELLER.equals(twitterConnectAccountRequest.getType())) {
					page.setResellerId(twitterConnectAccountRequest.getBusinessId());
				}else {
					page.setEnterpriseId(twitterConnectAccountRequest.getBusinessId());
				}
				page.setAccountId(accountId);
				socialTwitterRepo.saveAndFlush(page);
			});
			if (checkBusinessSMB(business) && isBusinessNotMappedToTwitterAccount(business)) {
				twitterSocialAccountService.saveTwitterLocationMapping(business.getBusinessId(), twitterAccountIds.get(0), req.getBirdeyeUserId(),type, null);
			}else{
				SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest(accountIds,SocialChannel.TWITTER.getName());
				kafkaProducer.sendObject(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);
			}
			releaseLock(req.getChannel(),parentId);
			Map<String, List<ChannelAccountInfo>> accountMap = new HashMap<>();
			List<ChannelAccountInfo> channelAccountInfos=getTwitterAcntInfo(getTwitterAccountsList(twitterAccountIds, enterpriseId,type));
			if(Constants.RESELLER.equals(type)) {
				channelAccountInfos.forEach(info->info.setUserId(req.getEmail()));
			}
			accountMap.put(SocialChannel.TWITTER.getName(), channelAccountInfos);
			channelAccountInfo.setPageTypes(accountMap);
		}
		return channelAccountInfo;
	}
	
	/**
	 * @param twitterAccountsList
	 * @return
	 */
	private List<ChannelAccountInfo> getTwitterAcntInfo(List<BusinessTwitterAccounts> twitterAccountsList) {
			List<ChannelAccountInfo> accountInfo = new ArrayList<>();
			twitterAccountsList.stream().forEach(page -> accountInfo.add(getTwitterAcntInfo(page)));
			return accountInfo;
	}

	private List<BusinessTwitterAccounts> getTwitterAccountsList(List<Long> twitterAccountIds) {
		return socialTwitterRepo.findByProfileIdIn(twitterAccountIds);
	}
	
	private List<BusinessTwitterAccounts> getTwitterAccountsList(List<Long> twitterAccountIds, long enterpriseId, String type) {
		return RESELLER.equals(type)?socialTwitterRepo.findByResellerIdAndProfileIdIn(enterpriseId, twitterAccountIds)
				:socialTwitterRepo.findByEnterpriseIdAndProfileIdIn(enterpriseId, twitterAccountIds);

	}

	@Override
	public void cancelRequest(String channel, Long businessId, Boolean forceCancel) {
		LOGGER.info("[Twitter Setup] Request received to Cancel Twitter Request for business {}", businessId);
		BusinessGetPageRequest req =  businessGetPageService.findLastRequestByEnterpriseIdAndChannelAndRequestType(businessId, SocialChannel.TWITTER.getName(),CONNECT);
		if(Objects.isNull(req)) {
			LOGGER.error("No record found in business get page request for businessId: {}", businessId);
			return;
		}
		if(!forceCancel && Status.INITIAL.getName().equals(req.getStatus())) {
			throw new SocialBirdeyeException(ErrorCodes.INVALID_REQUEST, Constants.INIT_TO_CANCEL_ERROR_REQUEST_MESSAGE);
		}
		req.setStatus(Status.CANCEL.getName());
		req.setUpdated(new Date());
		businessGetPageReqRepo.saveAndFlush(req);
		pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),req.getRequestType(),Status.COMPLETE.getName(), req.getEnterpriseId());
		releaseLock(req.getChannel(), req.getEnterpriseId());
	}

	/**
	 * Update Twitter account with new data, if valid mapping (twitterpage) is available, update that too
	 * @param userId 
	 * @param request 
	 */
	private void updateTwitterAccountIsValid(BusinessTwitterAccounts twitterAccount,  Long businessId, Twitter twitter, AccessToken accessToken, Integer userId, BusinessGetPageRequest request)
			throws TwitterException {
		LOGGER.info("[Twitter Setup] Twitter Access Token Generated. Token {} TokenSecret {} ", accessToken.getToken(), accessToken.getTokenSecret());
		
		TwitterV2ProfileResponse profileResponse = getUserProfileResponse(twitterAccount.getProfileId(),accessToken);
		if (twitterAccount!= null) {
			twitterAccount.setAccessToken(accessToken.getToken());
			twitterAccount.setAccessSecret(accessToken.getTokenSecret());
			twitterAccount.setProfileUrl(TWITTER_COM + twitter.getScreenName());
			twitterAccount.setProfilePicUrl(profileResponse.getProfileImageUrl());
			twitterAccount.setHandle("@" + twitter.getScreenName());
			twitterAccount.setName(profileResponse.getName());
			twitterAccount.setAccountPermissions("read,write,direct_message");
			twitterAccount.setIsValid(1);
			twitterSocialAccountService.updateXValidityType(twitterAccount);
			twitterAccount.setInvalidType(null);
			twitterAccount.setCreatedBy(userId);
			twitterAccount.setUpdatedBy(userId);
			twitterAccount.setUpdatedAt(new Date());
			twitterAccount.setRequestId(request.getId().toString());
			socialTwitterRepo.saveAndFlush(twitterAccount);
			commonService.uploadPageImageToCDN(twitterAccount);
			brokenIntegrationService.pushValidIntegrationStatus(businessId, SocialChannel.TWITTER.getName(),twitterAccount.getId(),1,twitterAccount.getProfileId().toString());
			twitterSocialAccountService.syncPlatformTable(twitterAccount);
			commonService.sendTwitterSetupAuditEvent(SocialSetupAuditEnum.RECONNECT_PAGE.name(), Arrays.asList(twitterAccount),
					String.valueOf(profileResponse.getId()), twitterAccount.getBusinessId(),businessId);
			if(Objects.nonNull(twitterAccount.getBusinessId())) {
				kafkaProducer.sendObject(Constants.TWITTER_PAGE_UPDATE, new LocationPageMappingRequest(twitterAccount.getBusinessId(), String.valueOf(twitterAccount.getProfileId())));
			}
			request.setPageCount(1);
			request.setTotalPages(1);
			request.setEmail("@" + twitter.getScreenName());
		}

		//Marking Request as COMPLETE
		request.setStatus(Status.COMPLETE.getName());
		businessGetPageReqRepo.saveAndFlush(request);
		pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(), request.getRequestType(), request.getStatus(),businessId);
		releaseLock(request.getChannel(),request.getEnterpriseId());
		
		LOGGER.info("[Twitter Setup] Twitter Account Reconnected Successfully, business id {} profile id {}",businessId, twitterAccount.getProfileId());
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialPostTwitterService#getTwitterIntegrationRequestInfo(java.lang.Long, java.lang.String)
	 */
	@Override
	public ChannelPageInfo getTwitterIntegrationRequestInfo(Long businessId, String requestType) {
		ChannelPageInfo response = new ChannelPageInfo();
		BusinessGetPageRequest twitterData = businessGetPageService.findLastRequestByEnterpriseIdAndChannel(businessId, SocialChannel.TWITTER.getName());
		if (twitterData!=null){
			if(twitterData.getStatus().equalsIgnoreCase(Status.INITIAL.getName()) || twitterData.getStatus().equalsIgnoreCase(Status.NO_PAGES_FOUND.getName())) {
				response.setStatus(twitterData.getStatus());
				response.setStatusType(twitterData.getRequestType());
			} else if(CONNECT.equalsIgnoreCase(twitterData.getRequestType()) && twitterData.getStatus().equalsIgnoreCase(Status.FETCHED.getName())) {
				LOGGER.info("[Twitter] isFetched is true for businessId and request Type {} {}",businessId,requestType);
				response.setStatus(Status.FETCHED.getName());
				Map<String, List<ChannelAccountInfo>> pageType = getTwitterAccounts(businessId, requestType);
				response.setPageTypes(pageType);
				response.setStatusType(twitterData.getRequestType());
			} else {
				response.setStatus(Status.COMPLETE.getName());
				response.setStatusType(twitterData.getRequestType());
			}
		} else {
			response.setStatus(Status.COMPLETE.getName());
			response.setStatusType(twitterData!=null?twitterData.getRequestType():requestType);
		}
		return response;
	}

	private List<BusinessGetPageRequest> getMaxRequestForBusiness(Long businessId, String status) {
		return businessGetPageReqRepo.findLastRequestByEnterpriseIdAndStatusAndChannel(businessId, status, SocialChannel.TWITTER.getName());
	}

	/* (non-Javadoc)
	 * @see com.birdeye.social.service.SocialPostTwitterService#reconnectTwitterEnhancedFlow(java.util.List, java.lang.String, java.lang.String, java.lang.String, java.lang.Integer)
	 */
	@Override
	public void reconnectTwitterEnhancedFlow(TwitterAuthRequest twitterAuthRequest)  {

		Long businessId=twitterAuthRequest.getBusinessId();
		List<String> profileIds= twitterAuthRequest.getPageId();
		String requestToken=twitterAuthRequest.getTempAccessToken();
		String requestSecret=twitterAuthRequest.getSecret();
		String oauthVerifier=twitterAuthRequest.getOauthVerifier();
		Integer userId=twitterAuthRequest.getBirdeyeUserId();
		String type= twitterAuthRequest.getType();
		String key = SocialChannel.TWITTER.getName().concat(String.valueOf(businessId));
		boolean lock = redisService.tryToAcquireLock(key);
		LOGGER.info("[Redis Lock] Lock status : {}",lock);
		if (lock) {
			try {
				pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(), RECONNECT, Status.INITIAL.getName(),businessId);
				// Check if another connect request is present in INIT or FETCHED state
				BusinessGetPageRequest existingInProgressReq =Constants.ENTERPRISE.equals(type)? businessGetPageReqRepo.findFirstByEnterpriseIdAndChannelAndStatusIn(
						businessId, SocialChannel.TWITTER.getName(), Arrays.asList(Status.FETCHED.name(), Status.INITIAL.name()))
						:businessGetPageReqRepo.findFirstByResellerIdAndChannelAndStatusIn(
						businessId, SocialChannel.TWITTER.getName(), Arrays.asList(Status.FETCHED.name(), Status.INITIAL.name()));
				if (existingInProgressReq != null) {
					LOGGER.info("reconnectTwitterEnhancedFlow: Existing BusinessGetPageRequest found with status INITIAL/FETCHED {}",
							existingInProgressReq);
					throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
				}
				Business business = businessRepo.findByBusinessId(businessId);
				WebsiteDomainInfo websiteDomainInfo = businessService.getBusinessDomain(business.getId());
				SocialAppCredsInfo domainInfo = socialAppService.getTwitterAppSettings(websiteDomainInfo.getId(), websiteDomainInfo.getDomainName());
				if (domainInfo == null) {
					LOGGER.error("[Twitter Setup] Consumer Token and Secret not found for Business id {}", businessId);
					throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, TOKEN_AND_SECRET_NOT_FOUND);
				}
				Configuration config = new ConfigurationBuilder().build();
				Twitter twitter = new TwitterFactory(config).getInstance();
				twitter.setOAuthConsumer(domainInfo.getChannelClientId(), domainInfo.getChannelClientSecret());
				RequestToken reqTokenObj = new RequestToken(requestToken, requestSecret);
				AccessToken accessToken = twitter.getOAuthAccessToken(reqTokenObj, oauthVerifier);
				final Long twitterProfileId = twitter.getId();

				if ( !profileIds.contains(String.valueOf(twitterProfileId)) ) {
					LOGGER.error("[Twitter Setup] Twitter Account Reconnect Unsuccessful :: List of profileIds {} does not contain integrated profileId {}", profileIds, twitterProfileId);
					pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),RECONNECT,Status.COMPLETE.getName(),businessId,true);
					throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Integrated twitter account " + twitterProfileId + " is not present in list of profileIds");
				} else if ( accessToken == null ) {
					LOGGER.error("[Twitter Setup] Twitter Account Reconnect Unsuccessful :: Null value received for accessToken");
					pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),RECONNECT,Status.COMPLETE.getName(),businessId,true);
					throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Null value received for accessToken");
				}
				
				//Social Accounts
				List<BusinessTwitterAccounts> twitterAccounts = socialTwitterRepo.findByProfileId(twitterProfileId);
				if ( CollectionUtils.isEmpty(twitterAccounts) ) {
					pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(),RECONNECT,Status.COMPLETE.getName(),businessId,true);
					throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Twitter account with profile id " + twitterProfileId + " doesn't exist");
				}
				
				//NOTE : Going forward, BusinessTwitterAccounts data will be used for reconnect as there might be a case when entry is not there in BusinessTwitterPage (No Location Mapping)
				BusinessTwitterAccounts twitterAccount = twitterAccounts.get(0);

				// Add entry in get page request
				BusinessGetPageRequest request = new BusinessGetPageRequest();
				request.setBirdeyeUserId(userId);
				request.setSocialUserId(twitterProfileId.toString());
				request.setChannel(SocialChannel.TWITTER.getName());
				if (ENTERPRISE.equals(type)) {
					request.setEnterpriseId(businessId);
				} else {
					request.setResellerId(businessId);
				}
				request.setPageCount(0);
				request.setStatus(Status.INITIAL.getName());
				request.setRequestType(RECONNECT);
				businessGetPageReqRepo.saveAndFlush(request);
				// Reconnect process
				updateTwitterAccountIsValid(twitterAccount,  business.getBusinessId(), twitter, accessToken, userId, request);
				businessGetPageReqRepo.saveAndFlush(request);
			}  catch (Exception e) {
				LOGGER.error("[Twitter Setup] Error occurred while reconnecting {}", e.getMessage() != null ? e.getMessage() : "Error in Twitter reconnect process");
				// Cleanup redis cache for error cases.
				redisService.release(key);
				LOGGER.info("[Redis Lock] (Twitter Reconnect) Lock released for business {}", businessId);
				if (e instanceof BirdeyeSocialException) {
					BirdeyeSocialException beExp = (BirdeyeSocialException) e;
					if (beExp.getCode() == ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.value())
						throw beExp;
				}
				// Cleanup get pages request
				BusinessGetPageRequest req = checkInProgressRequests(businessId,type);
				if (req != null) {
					req.setPageCount(0);
					req.setTotalPages(0);
					req.setStatus(Status.CANCEL.getName());
					businessGetPageReqRepo.saveAndFlush(req);
					pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(), req.getRequestType(), Status.COMPLETE.getName(),businessId,true);
				}else{
					pushCheckStatusInFirebase(SocialChannel.TWITTER.getName(), RECONNECT,Status.COMPLETE.getName() ,businessId,true);
				}
			}
		} else {
			LOGGER.info("[Redis Lock] (Twitter Reconnect) Lock is already acquired for business {}", businessId);
			throw new BirdeyeSocialException(ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS, ErrorCodes.SOCIAL_CONNECT_ALREADY_IN_PROGRESS.name());
		}
	}
	
	@Override
	public OpenUrlPagesInfo getPagesFetchedByOpenUrl(Long enterpriseId) {
		LOGGER.info("getPagesFetchedByOpenUrl twitter: enterpriseId {}", enterpriseId);
		OpenUrlPagesInfo response = new OpenUrlPagesInfo();
		List<BusinessGetPageOpenUrlRequest> liRequests = businessGetPageOpenUrlReqRepo.findFirstByEnterpriseIdAndChannelAndRequestTypeOrderByCreatedDesc(enterpriseId, SocialChannel.TWITTER.getName(), CONNECT);
		LOGGER.info("getPagesFetchedByOpenUrl: List<BusinessGetPageOpenUrlRequest> got result twitter {}", liRequests);
		if ( liRequests != null && !liRequests.isEmpty() ) {
			BusinessGetPageOpenUrlRequest liRequest = liRequests.get(0);
			if(liRequest.getStatus().equalsIgnoreCase(Status.FETCHED.getName())){
				List<BusinessTwitterAccounts> businessTwitterAccounts = socialTwitterRepo.findByRequestId(liRequest.getId());
				response.setPageTypes(businessTwitterAccounts.stream().map(this::getTwitterAcntInfo).collect(Collectors.toList()));
			}
		}else {
			LOGGER.info("getPagesFetchedByOpenUrl twitter: No BusinessGetPageOpenUrlRequest found with given input");
			response.setStatus(Status.COMPLETE.getName());
			response.setStatusType("connect");
		}
		LOGGER.info("getPagesFetchedByOpenUrl twitter: Returning response {}", response);
		return response;
	}

	@Override
	public OpenUrlPagesInfo connectPagesFetchedByOpenUrl(Long enterpriseId, OpenUrlConnectRequest connectRequest, Integer userId) {
		LOGGER.info("connectPagesFetchedByOpenUrl twitter : enterpriseId {} connectRequest {} userId {}", enterpriseId, connectRequest, userId);
		if ( enterpriseId == null || connectRequest == null || com.birdeye.social.utils.StringUtils.isEmpty(connectRequest.getFirebaseKey()) || CollectionUtils.isEmpty(connectRequest.getPageRequests()) ) {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_ARGUMENT, "Invalid value for enterpriseId/connectRequest/userId");
		}
		final String firebaseKey = connectRequest.getFirebaseKey();
		final List<Long> pageIds = connectRequest.getPageRequests().stream().map(com.birdeye.social.sro.PageRequest::getId).map(Long :: parseLong).collect(Collectors.toList());
		OpenUrlPagesInfo response = new OpenUrlPagesInfo();
		List<BusinessGetPageOpenUrlRequest> twitterRequests = getRequestForOpenUrlBusiness(enterpriseId, Status.FETCHED.getName(), SocialChannel.TWITTER.getName(), CONNECT, firebaseKey);
		if ( CollectionUtils.isEmpty(twitterRequests) ) {
			// case of no request found
			LOGGER.error("connectPagesFetchedByOpenUrl twitter: No rows found with fetched status for pageIds {} enterpriseId {} firebaseKey {}", pageIds, enterpriseId, firebaseKey);
			throw new BirdeyeSocialException(ErrorCodes.STATUS_CHANGED, "Seems request status has already changed");
		} else if ( twitterRequests.size() > 1 ) {
			// case of multiple requests present
			LOGGER.error("connectPagesFetchedByOpenUrl twitter: Multiple rows found with fetched status for pageIds {} and enterpriseId {} firebaseKey {}", pageIds, enterpriseId, firebaseKey);
			throw new BirdeyeSocialException(ErrorCodes.MULTI_FETCHED_ROWS, "Multiple rows with fetched status found in BusinessGetPageOpenUrlRequest");
		}else{
			List<BusinessTwitterAccounts> businessTwitterAccounts = socialTwitterRepo.findByProfileIdIn(pageIds);
			if ( CollectionUtils.isEmpty(businessTwitterAccounts) ) {
				LOGGER.error("connectPagesFetchedByOpenUrl Twitter: No twitter account found with pageIds {}", pageIds);
				throw new BirdeyeSocialException(ErrorCodes.TWITTER_PAGE_NOT_FOUND, "FB Pages not found with given pageIds");
			}
			BusinessLiteRequest businessLiteRequest = new BusinessLiteRequest();
			businessLiteRequest.setKey("businessNumber");
			businessLiteRequest.setValue(enterpriseId);
			BusinessLiteDTO business = businessCoreService.getBusinessLite(businessLiteRequest);
			if ( Objects.isNull(business) ) {
				throw new BirdeyeSocialException(ErrorCodes.BUSINESS_NOT_FOUND, "Business not found using Business Lite API");
			}

			final boolean isBusinessNotMapped = isBusinessNotMappedToTwitterAccount(business);
			final boolean isSmbAndMapped = checkBusinessSMB(business) && !isBusinessNotMapped;
			LOGGER.info("connectPagesFetchedByOpenUrl Twitter: checkBusinessSMB {} isBusinessNotMapped {} invalidSmbCase {}", checkBusinessSMB(business), isBusinessNotMapped, isSmbAndMapped);
			List<BusinessTwitterAccounts> existingPages = new ArrayList<>();
			List<BusinessTwitterAccounts> newPages = new ArrayList<>();
			for ( BusinessTwitterAccounts accounts : businessTwitterAccounts ) {
				if ( accounts.getEnterpriseId() == null && !isSmbAndMapped ) { // case of newly found page
					accounts.setIsSelected(1);
					accounts.setEnterpriseId(enterpriseId);
					accounts.setAccountId(business.getBusinessId());
					socialTwitterRepo.saveAndFlush(accounts);
					newPages.add(accounts);
				} else if ( accounts.getEnterpriseId() != null && accounts.getEnterpriseId().equals(enterpriseId) ) { // case of existing page
					existingPages.add(accounts);
				} else if ( isSmbAndMapped ) { // case of smb account already mapped
					setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
							accounts.getProfileId().toString(), String.valueOf(userId), accounts.toString(), SocialChannel.TWITTER.getName(),
							"SMB account is already mapped"));
				} else { // case of trying to connect page belonging to different enterpriseId
					setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
							accounts.getProfileId().toString(), String.valueOf(userId), accounts.toString(), SocialChannel.TWITTER.getName(),
							"This page is already connected with some other enterpriseId"));
				}
			}

			updateTwitterAccountIsValid(existingPages);
			response.setPageTypes(getTwitterAcntInfo(businessTwitterAccounts));
			response.setStatus(Status.COMPLETE.getName());
			response.setStatusType("connect");
			// update request in db
			twitterRequests.get(0).setStatus(Status.COMPLETE.getName());
			twitterRequests.get(0).setUpdated(new Date());
			businessGetPageOpenUrlReqRepo.saveAndFlush(twitterRequests.get(0));
			LOGGER.info("connectPagesFetchedByOpenUrl Twitter: Request status updated to complete");

			if (checkBusinessSMB(business) && isBusinessNotMapped) {
				try {
					LOGGER.info("connectPagesFetchedByOpenUrl Twitter: Trying to map business {} to pageId {}", business.getBusinessId(), pageIds.get(0));
					saveTwitterLocationMapping(business.getBusinessId(), pageIds.get(0), userId);
				} catch (Exception saveMappingException) {
					// we need to return 200 OK response even if mapping fails
					LOGGER.error("connectPagesFetchedByOpenUrl Twitter: Failed to map business with page with error {}", saveMappingException.getMessage());
					setupAuditRepo.saveAndFlush(new SocialSetupAudit("OPEN_URL_CONNECT", business.getBusinessId(),
							pageIds.get(0).toString(), String.valueOf(userId), null, SocialChannel.TWITTER.getName(),
							"SMB mapping failed"));
				}
			}else{
				if(CollectionUtils.isNotEmpty(newPages)){
					LOGGER.info("publishing SOCIAL_PAGE_CONNECT event for twitter open url for enterpriseID {}",enterpriseId);
					SocialConnectPageRequest socialConnectPageRequest = new SocialConnectPageRequest(
							newPages.stream().map(BusinessTwitterAccounts :: getProfileId).map(aLong -> aLong.toString()).collect(Collectors.toList())
							,SocialChannel.TWITTER.getName());
					kafkaProducer.sendObject(SOCIAL_PAGE_CONNECT,socialConnectPageRequest);
				}
			}
		}
		LOGGER.info("connectPagesFetchedByOpenUrl twitter: Response {}", response);
		return response;
	}

	private void saveTwitterLocationMapping(Integer businessId, Long profileId, Integer userId) {
		List<BusinessTwitterAccounts> businessTwitterAccounts = socialTwitterRepo.findByProfileId(profileId);
		if (CollectionUtils.isEmpty(businessTwitterAccounts)) {
			LOGGER.error("openurl connection : For Twitter page id {} no data found ", profileId);
			throw new BirdeyeSocialException(ErrorCodes.TWITTER_ACCOUNT_NOT_FOUND, "Twitter account data not found");
		}

		BusinessTwitterAccounts twitterAcc = businessTwitterAccounts.get(0);
		twitterAcc.setBusinessId(businessId);
		twitterAcc.setEnabled(CacheManager.getInstance().getCache(SystemPropertiesCache.class).getSocialAutoPostFlag());
		if(Objects.isNull(twitterAcc.getCreatedBy())) {
			twitterAcc.setCreatedBy(userId); //only updated by should be changed
		}
		twitterAcc.setUpdatedBy(userId);
		socialTwitterRepo.saveAndFlush(twitterAcc);
		kafkaProducer.sendObject(Constants.TWITTER_PAGE_MAPPING_ADDED, new LocationPageMappingRequest(businessId, String.valueOf(businessTwitterAccounts.get(0).getProfileId())));
	}

	private void updateTwitterAccountIsValid(List<BusinessTwitterAccounts> existingPages) {
		LOGGER.info("markTwitterLocationMappingAsvalid : Marking BusinessFacebookPageNew as valid for pageIds {}", existingPages);
		if ( existingPages != null && !existingPages.isEmpty() ) {
			Map<Long, BusinessTwitterAccounts> pageIdTwitterPageMap = existingPages.stream()
					.collect(Collectors.toMap(BusinessTwitterAccounts::getProfileId, twitterAccounts -> twitterAccounts, (x, y) -> x));

			List<BusinessTwitterAccounts> twitterLocations = socialTwitterRepo.findByProfileIdIn(new ArrayList<Long>(pageIdTwitterPageMap.keySet()));
			if ( !twitterLocations.isEmpty() ) {
				twitterLocations.forEach(twitterPage -> {
					BusinessTwitterAccounts accounts = pageIdTwitterPageMap.get(twitterPage.getProfileId());
					twitterPage.setIsValid(accounts.getIsValid());
					twitterPage.setInvalidType(accounts.getInvalidType());
					socialTwitterRepo.saveAndFlush(twitterPage);
					if(twitterPage.getIsValid().equals(0)) {
						commonService.sendTwitterSetupAuditEvent(SocialSetupAuditEnum.PAGE_DISCONNECTED.name(), Arrays.asList(twitterPage),
								null, twitterPage.getBusinessId(), twitterPage.getEnterpriseId());
					}
					brokenIntegrationService.pushValidIntegrationStatus(accounts.getEnterpriseId(),SocialChannel.TWITTER.getName(),accounts.getId(),accounts.getIsValid(),accounts.getProfileId().toString());
				});
			}
			LOGGER.info("markTwitterLocationMappingAsInvalid : BusinessTwitterPage {} have been marked as valid", twitterLocations);
		}
	}

	private boolean isBusinessNotMappedToTwitterAccount(BusinessLiteDTO business) {
		return socialTwitterRepo.findByBusinessId(business.getBusinessId()).isEmpty();
	}


	/**
	 * Method to check in progress request for a business in case an exception occurred
	 * @param businessId
	 * @return
	 */
	private BusinessGetPageRequest checkInProgressRequests(Long businessId, String type) {
		LOGGER.info("Exception occurred in reconnect twitter, Checking for in progress request for business id {}", businessId);
		List<BusinessGetPageRequest> underProcessRequests = getRequestForBusiness(businessId, Status.INITIAL.getName(),RECONNECT,type);
		if (CollectionUtils.isNotEmpty(underProcessRequests)) {
			return underProcessRequests.get(0);
		}
		return null;
	}

	private void pushToKafkaForValidity(String channel, Collection<Long> profileIds) {
		ValidityRequestDTO validityRequestDTO = new ValidityRequestDTO();
		validityRequestDTO.setChannel(channel);
		validityRequestDTO.setXProfileIds(profileIds);
		kafkaProducer.sendObject(Constants.CHECK_VALIDITY, validityRequestDTO);
	}

	@Override
	public List<Integer> getMappedResellerLeafLocations(List<Integer> resellerLeafLocationIds) {
		if(CollectionUtils.isNotEmpty(resellerLeafLocationIds)) {
			return socialTwitterRepo.findAllIdByBusinessIdIn(resellerLeafLocationIds);
		}
		return Collections.emptyList();
	}

	/**
	 * @param requestIds
	 */
	@Override
	public List<String> getMappedRequestIds(Set<String> requestIds) {
		return socialTwitterRepo.findDistinctRequestIdByRequestIdIn(requestIds);
	}

	@Override
	public com.twitter.clientlib.model.TweetCreateResponse postV2(TwitterCreds creds, TweetCreateRequest tweetCreateRequest,
																  String mediaType, List<File> fileUploadData, Integer accountId) throws Exception {
		if ("VIDEO".equalsIgnoreCase(mediaType)) {
			return uploadTwitterVideo(creds, fileUploadData.get(0), false, null, null, tweetCreateRequest, accountId);
		} else if ("IMAGE".equalsIgnoreCase(mediaType)) {
			return uploadTwitterMedia(creds, fileUploadData, tweetCreateRequest, accountId);
		}
		return postContent(creds, Collections.emptyList(), tweetCreateRequest);
	}

	// considerForChunkUpload is used to differ twitter video post
	private com.twitter.clientlib.model.TweetCreateResponse uploadTwitterVideo(
			TwitterCreds creds, File file, boolean considerForChunkUpload,
			SocialPostPublishInfo publishInfo, BusinessTwitterAccounts twitterAccounts,
			TweetCreateRequest tweetCreateRequest, Integer accountId) throws Exception {

		com.twitter.clientlib.model.TweetCreateResponse tweetCreateResponse = new com.twitter.clientlib.model.TweetCreateResponse();

		if (considerForChunkUpload) {
			String[] videoIds = publishInfo.getSocialPost().getVideoIds().split(",");
			if (videoIds.length > 1) {
				throw new Exception("Cannot post more than 1 video on Twitter");
			}

			SocialPostsAssets asset = socialPostsAssetService.findById(Integer.parseInt(videoIds[0]));
			MediaUploadRequest request = new MediaUploadRequest(publishInfo.getId(), asset.getId(),
					publishInfo.getSourceId(), String.valueOf(twitterAccounts.getProfileId()));

			if (commonService.isTwitterVideoEligibleForChunkMediaUpload(asset, request)) {
				BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(publishInfo.getEnterpriseId(), false);
				LOGGER.info("[uploadTwitterVideo] Uploading Twitter video in chunks for asset ID {}", asset.getId());

				String mediaCdnUrl = socialPostsAssetService.getCompleteVideoUrlFromPostAsset(asset, businessLiteDTO.getBusinessNumber().toString());
				request.setMediaUrl(mediaCdnUrl);
				kafkaProducer.sendObjectV1(KafkaTopicEnum.SOCIAL_MEDIA_UPLOAD_INIT.getName(), request);
				return tweetCreateResponse;
			}
		}
		// If chunk upload is not considered OR chunk upload is considered but not eligible
		String mediaId;
		TwitterTemplate twitter;
		// 2. APPEND
//		FileSystemResource resource = new FileSystemResource(file);
//		append(twitter, resource, mediaId, accountId);


		if(isTwitterV2MediaUploadEnabled(accountId)) {
			twitter = getTemplateForMediaUploadV2(creds);
			// 1. INIT
			mediaId = initV2(twitter, String.valueOf(file.length()));

			// 2. APPEND
			FileSystemResource resource = new FileSystemResource(file);
			appendV2(twitter, resource, mediaId);

			// 3. FINALIZE
			TwitterMediaResponse finalizeV2Response = finalizeV2(twitter, mediaId);
			if (!checkForStatusV2(twitter, finalizeV2Response)) {
				throw new BirdeyeSocialException(ErrorCodes.TWITTER_MEDIA_POST_FAILED, "Unable to post video to Twitter.");
			}
		} else {
			twitter = createTwitterTemplate(creds);
			// 1. INIT
			mediaId = initV1(twitter, String.valueOf(file.length()));

			// 2. APPEND
			FileSystemResource resource = new FileSystemResource(file);
			appendV1(twitter, resource, mediaId);

			// 3. FINALIZE
			Map<?, ?> finalizeV1Response = finalizeV1(twitter, mediaId);
			if (!checkForStatusV1(twitter, finalizeV1Response)) {
				throw new BirdeyeSocialException(ErrorCodes.TWITTER_MEDIA_POST_FAILED, "Unable to post video to Twitter.");
			}
		}

		return postContent(creds, Collections.singletonList(mediaId), tweetCreateRequest);
	}
	public com.twitter.clientlib.model.TweetCreateResponse uploadTwitterMedia(TwitterCreds creds, List<File> images,
																			  TweetCreateRequest tweetCreateRequest, Integer accountId) {
		Boolean isV2UploadEnabled = isTwitterV2MediaUploadEnabled(accountId);
		TwitterTemplate twitter = isV2UploadEnabled ? getTemplateForMediaUploadV2(creds) : createTwitterTemplate(creds);
		MultiValueMap<String, Object> uploadParams = new LinkedMultiValueMap<>();
		List<String> mediaIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(images)) {
			try {
				for (File image : images) {
					uploadParams.set(MEDIA, new FileSystemResource(image));
					if(isV2UploadEnabled) {
						LOGGER.info("[Twitter] Uploading image to twitter V2 with media {} and params {}", image.getName(), uploadParams);
						byte[] byteResponse = twitter.restOperations().postForObject(MEDIA_UPLOAD_URL_V2, uploadParams, byte[].class);
						TwitterMediaResponse response = parseTwitterMediaResponse(byteResponse);
						LOGGER.info("[Twitter] Response for twitter image upload: {}", response);
						mediaIds.add(response.getData().getId());
					} else {
						HttpHeaders headers = new HttpHeaders();
						headers.add("Accept", "application/json");
						HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(uploadParams, headers);
						LOGGER.info("[Twitter] Uploading image to twitter V1 with media {} and params {}", image.getName(), uploadParams);
						Map<?, ?> response = twitter.restOperations().postForObject(MEDIA_UPLOAD_URL, requestEntity, Map.class);
						String mediaId = response.get(MEDIA_ID).toString();
						LOGGER.info("Uploading image to twitter {} with media_id {}", image.getName(), mediaId);
						mediaIds.add(mediaId);
					}
				}
			} catch (RestClientException ex) {
				handleError(ex.getMessage());
			} catch (Exception e) {
				handleError(e.getMessage());
			}
		}
		return postContent(creds, mediaIds, tweetCreateRequest);
	}

	public void handleError(String ex) {
		Integer code = null;
		String errText = ex;

		if(Objects.nonNull(ex) && ex.contains(TWITTER_ERROR_MSG_403)) {
			errText = TWITTER_ERROR_MSG_403;
		} else if(Objects.nonNull(ex) && ex.contains(TWITTER_ERROR_DUPLICATE_MSG_403)) {
			errText = TWITTER_ERROR_DUPLICATE_MSG_403;
		}else if(Objects.nonNull(ex) && ex.contains(TWITTER_ERROR_ACCESS_RESTRICTED_MSG_403)){
			errText = TWITTER_ERROR_ACCESS_RESTRICTED_MSG_403;
		} else if (com.birdeye.social.utils.StringUtils.isNotEmpty(ex) && ex.contains(TWITTER_UNAUTHORIZED)) {
			errText = TWITTER_ERROR_UNAUTHORIZED;
		} else if(com.birdeye.social.utils.StringUtils.isNotEmpty(ex) && ex.contains(TWITTER_LIMIT_MESSAGE)) {
			errText = TWITTER_LIMIT_ERROR_MESSAGE;
		}else if(com.birdeye.social.utils.StringUtils.isNotEmpty(ex) && ex.contains(TWITTER_ERROR_ACTION_FORBIDDEN_MSG_403)) {
			errText = TWITTER_ERROR_ACTION_FORBIDDEN_MSG_403;
		}else if(com.birdeye.social.utils.StringUtils.isNotEmpty(ex) && ex.contains(TWITTER_ERROR_MEDIA_ID_INVALID)) {
			errText = TWITTER_ERROR_MEDIA_ID_INVALID;
		}

		LOGGER.info("Error Message:{}",errText);
		throw new BirdeyeSocialException(code, errText);
	}

	private String initV1(TwitterTemplate twitter, String length) {
		MultiValueMap<String, Object> initParams = new LinkedMultiValueMap<String, Object>();
		initParams.add(COMMAND, "INIT");
		initParams.add(MEDIA_TYPE, "video/mp4");
		initParams.add(MEDIA_CATEGORY, "tweet_video");
		initParams.add(TOTAL_BYTES, length);

		try {
			Map<?, ?> response = twitter.restOperations().postForObject(MEDIA_UPLOAD_URL, initParams, Map.class);
			LOGGER.info("INIT response V1: {} ", response);
			return response.get(MEDIA_ID).toString();
		} catch (Exception e) {
			handleError(e.getMessage());
		}
		return null;
	}

	private String initV2(TwitterTemplate twitter, String length) {
		MultiValueMap<String, Object> initParams = new LinkedMultiValueMap<String, Object>();
		initParams.add(COMMAND, "INIT");
		initParams.add(MEDIA_TYPE, "video/mp4");
		initParams.add(MEDIA_CATEGORY, "tweet_video");
		initParams.add(TOTAL_BYTES, length);

		try {
			TwitterMediaResponse response = twitter.restOperations().postForObject(MEDIA_UPLOAD_URL_V2, initParams, TwitterMediaResponse.class);
			LOGGER.info("INIT response from twitter V2: {} ", response);
			return response.getData().getId();
		} catch (Exception e) {
			handleError(e.getMessage());
		}
		return null;
	}


	private void appendV1(TwitterTemplate twitter, FileSystemResource resource, String mediaId) {
		byte[] video;
		try {
			video = IOUtils.toByteArray(resource.getInputStream());
		} catch (IOException e) {
			throw new BirdeyeSocialException("Failed to upload video", e);
		}
		int totalBytes = video.length;
		long chunkSize = Long.parseLong(CacheManager.getInstance().getCache(SystemPropertiesCache.class)
				.getProperty(SystemPropertiesCache.MEDIA_UPLOAD_CHUNK_SIZE));
		int segments = (int) Math.ceil((double) totalBytes / (double) chunkSize);
		int start = 0;
		try {
			for (int i = 0; i < segments; i++) {
				int newLength = Math.min(totalBytes, (int) chunkSize);
				byte[] chunkVideo = new byte[newLength];
				System.arraycopy(video, start, chunkVideo, 0, newLength);
				appendAPIV1(twitter, chunkVideo, mediaId, String.valueOf(i));
				totalBytes = totalBytes - newLength;
				start = start + newLength;
			}
		} catch (Exception e) {
			LOGGER.error("Error uploading data to twitter: {}", e.getMessage());
		}
	}

	private void appendV2(TwitterTemplate twitter, FileSystemResource resource, String mediaId) {
		byte[] video;
		try {
			video = IOUtils.toByteArray(resource.getInputStream());
		} catch (IOException e) {
			throw new BirdeyeSocialException("Failed to upload video", e);
		}
		int totalBytes = video.length;
		long chunkSize = Long.parseLong(CacheManager.getInstance().getCache(SystemPropertiesCache.class)
				.getProperty(SystemPropertiesCache.MEDIA_UPLOAD_CHUNK_SIZE));
		int segments = (int) Math.ceil((double) totalBytes / (double) chunkSize);
		int start = 0;
		try {
			for (int i = 0; i < segments; i++) {
				int newLength = Math.min(totalBytes, (int) chunkSize);
				byte[] chunkVideo = new byte[newLength];
				System.arraycopy(video, start, chunkVideo, 0, newLength);
				appendAPIV2(twitter, chunkVideo, mediaId, String.valueOf(i));
				totalBytes = totalBytes - newLength;
				start = start + newLength;
			}
		} catch (Exception e) {
			LOGGER.error("Error uploading data to twitter: {}", e.getMessage());
		}
	}

	private void appendAPIV1(TwitterTemplate twitter, byte[] bytes, String mediaId, String segment) {
		LOGGER.info("Append API called for mediaId: {}, segment: {} and length of bytes: {}", mediaId, segment, bytes.length);
		MultiValueMap<String, Object> appendParams = new LinkedMultiValueMap<String, Object>();
		appendParams.set(COMMAND, APPEND);
		appendParams.set(SEGMENT_INDEX, segment);
		appendParams.set(MEDIA, new ByteArrayResource(bytes));
		appendParams.set(MEDIA_ID, mediaId);
		try {
			LOGGER.info("[Twitter] Request to append video chunk V1 with params: {}", appendParams);
			twitter.restOperations().postForObject(MEDIA_UPLOAD_URL, appendParams, Map.class);
			LOGGER.info("[Twitter] APPEND V1 successful for chunk: {}", segment);
		} catch (Exception e) {
			LOGGER.info("[Twitter] Error while uploading video {}", e.getMessage());
		}
	}

	private void appendAPIV2(TwitterTemplate twitter, byte[] bytes, String mediaId, String segment) {
		LOGGER.info("Append API called for mediaId: {}, segment: {} and length of bytes: {}", mediaId, segment, bytes.length);
		MultiValueMap<String, Object> appendParams = new LinkedMultiValueMap<String, Object>();
		appendParams.set(COMMAND, APPEND);
		appendParams.set(SEGMENT_INDEX, segment);
		appendParams.set(MEDIA, new ByteArrayResource(bytes));
		appendParams.set(MEDIA_ID, mediaId);
		try {
			LOGGER.info("[Twitter] Request to append video chunk V2 with params: {}", appendParams);
			twitter.restOperations().postForObject(MEDIA_UPLOAD_URL_V2, appendParams, Void.class);
			LOGGER.info("[Twitter] APPEND V2 successful for chunk: {}", segment);
		} catch (Exception e) {
			LOGGER.info("[Twitter] Error while uploading video {}", e.getMessage());
		}
	}

	private TwitterMediaResponse finalizeV2(TwitterTemplate twitter, String mediaId) {
		TwitterMediaResponse v2FinalizeResponse = null;
		MultiValueMap<String, Object> finalizeParams = new LinkedMultiValueMap<String, Object>();
		finalizeParams.set(COMMAND, FINALIZE);
		finalizeParams.set(MEDIA_ID, mediaId);
		try {
			v2FinalizeResponse = twitter.restOperations().postForObject(MEDIA_UPLOAD_URL_V2, finalizeParams, TwitterMediaResponse.class);
			LOGGER.info("[Twitter] FINALIZE V2 response: {} ", v2FinalizeResponse);
		} catch (Exception e) {
			LOGGER.error("[Twitter] Error while calling FINALIZE V2 {}", e.getMessage());
			handleError(e.getMessage());
		}
		return v2FinalizeResponse;
	}

	private Map<?,?> finalizeV1(TwitterTemplate twitter, String mediaId) {
		Map<?, ?> response = null;
		MultiValueMap<String, Object> finalizeParams = new LinkedMultiValueMap<String, Object>();
		finalizeParams.set(COMMAND, FINALIZE);
		finalizeParams.set(MEDIA_ID, mediaId);
		try {
			response = twitter.restOperations().postForObject(MEDIA_UPLOAD_URL, finalizeParams, Map.class);
			LOGGER.info("[Twitter] FINALIZE V1 response: {} ", response);
		} catch (Exception e) {
			LOGGER.error("[Twitter] Error while calling FINALIZE V1 {}", e.getMessage());
			handleError(e.getMessage());
		}
		return response;
	}

	private boolean checkForStatusV1(TwitterTemplate twitter, Map<?, ?> finalizeResopnse) {
		Object processingInfo = finalizeResopnse.get("processing_info");
		String mediaId = finalizeResopnse.get(MEDIA_ID).toString();
		if (processingInfo == null) {
			LOGGER.info("Ready for posting tweet with mediaid {}", mediaId);
			return true;
		} else {
			// Check for status.
			Map<?, ?> map = (Map<?, ?>) processingInfo;
			LOGGER.info("Error parsing processing Info using 5 seconds as default");
			LOGGER.info("TwitterServiceIMPL checkForStatus : processing info map is {}", map);
			String state = map.get("state").toString();
			if (PENDING.equals(state)) {
				// Wait for status check and return result
				return statusV1(twitter, mediaId, 15);
			} else if (FAILED.equals(state)) {
				// TODO: Need to review if we need to retry in some other
				// failure cases as well.
				handleTwitterError(map);
			} else {
				LOGGER.info("Exiting from video upload process with state {}", state);
			}
		}
		return false;
	}

	private boolean checkForStatusV2(TwitterTemplate twitter, TwitterMediaResponse finalizeResponse) {
		Map<String, Object> processingInfo;
		String mediaId;
		if(finalizeResponse.getData() != null) {
			processingInfo = finalizeResponse.getData().getProcessing_info();
			mediaId = finalizeResponse.getData().getId();
		} else {
			LOGGER.error("Invalid response from twitter, does not contain data");
			return false;
		}

		// If processing info is absent, we can post the tweet immediately
		if (processingInfo == null) {
			LOGGER.info("Ready for posting tweet with mediaId {}", mediaId);
			return true;
		}

		LOGGER.info("TwitterServiceIMPL checkForStatus : processing_info map is {}", processingInfo);
		// If state is not found in processing_info
		if(processingInfo.get("state") == null) {
			LOGGER.error("Invalid response from twitter processing_info does not contain state");
			return false;
		}

		String state = (String) processingInfo.get("state");

		// Handle different states
		switch (state.toLowerCase()) {
			case PENDING:
			case IN_PROGRESS: //State is pending OR in_progress, we need to wait and check status
				return statusV2(twitter, mediaId, 15);
			case FAILED: // Handle failure by logging the error and throwing an exception
				handleTwitterError(Optional.ofNullable(finalizeResponse.getErrors()));
				break;
			default:
				LOGGER.info("Exiting from video upload process with state {}", state);
				break;
		}
		return false;
	}

	private boolean statusV1(TwitterTemplate twitter, String mediaId, long checkAfterSeconds) {
		int maxTries = 40;
		for (int i = 0; i < maxTries; i++) {
			// Waiting for status to complete.
			LOGGER.info("Waiting {} seconds for status check ", checkAfterSeconds);
			try {
				TimeUnit.SECONDS.sleep(checkAfterSeconds);
			} catch (InterruptedException e) {
				LOGGER.warn("Twitter posting thread interrupted");
			}

			TwitterMediaResponse mediaResponse = null;
			Map<?, ?> response = null;
			try {
				String url = String.format("%s?command=STATUS&media_id=%s", MEDIA_UPLOAD_URL, mediaId);
				LOGGER.info("Twitter upload status check with URL V1: {}", url);
				response = twitter.restOperations().getForObject(url, Map.class);
				LOGGER.info("[Twitter] STATUS response: {}", response);
			} catch (Exception e) {
				LOGGER.info("Error while executing: {}", e.getMessage());
				return false;
			}
			// check for status API response
			if(Objects.nonNull(response) && processMediaStatusResponse(response)) {
				return true;
			}
		}
		LOGGER.warn("Max tries: {} exhausted for Check status API", maxTries);
		return false;
	}

	private boolean statusV2(TwitterTemplate twitter, String mediaId, long checkAfterSeconds) {
		int maxTries = 40;
		for (int i = 0; i < maxTries; i++) {
			// Waiting for status to complete.
			LOGGER.info("Waiting {} seconds for status check ", checkAfterSeconds);
			try {
				TimeUnit.SECONDS.sleep(checkAfterSeconds);
			} catch (InterruptedException e) {
				LOGGER.warn("Twitter posting thread interrupted");
			}

			TwitterMediaResponse mediaResponse = null;
			Map<?, ?> response = null;
			try {
				String url = String.format("%s?command=STATUS&media_id=%s", MEDIA_UPLOAD_URL_V2, mediaId);
				LOGGER.info("Twitter upload status check with URL V2: {}", url);
				mediaResponse = twitter.restOperations().getForObject(url, TwitterMediaResponse.class);
				LOGGER.info("[Twitter] STATUS response: {}", mediaResponse);
			} catch (Exception e) {
				LOGGER.info("Error while executing: {}", e.getMessage());
				return false;
			}
			// check for status API response
			if(Objects.nonNull(mediaResponse) && processMediaStatusResponseV2(mediaResponse)) {
				return true;
			}
		}
		LOGGER.warn("Max tries: {} exhausted for Check status API", maxTries);
		return false;
	}

	private TwitterMediaResponse parseTwitterMediaResponse(byte[] response) throws IOException {
		LOGGER.info("[Twitter] Parsing twitter media response: {}", response);
		ObjectMapper objectMapper = new ObjectMapper();
		TwitterMediaResponse mediaResponse = new TwitterMediaResponse();
		try {
			String responseString = new String(response, StandardCharsets.UTF_8);
			if (responseString.contains("\"data\"")) { //Video Response
				mediaResponse = objectMapper.readValue(response, TwitterMediaResponse.class);
			} else { //IMG,GIF Response
				mediaResponse.setData(objectMapper.readValue(response, TwitterMediaResponseData.class));
			}
			return mediaResponse;
		} catch (Exception e) {
			LOGGER.info("Error decoding binary data: {}", e.getMessage());
		}
		return mediaResponse;
	}

	private boolean processMediaStatusResponseV2(TwitterMediaResponse response) {
		if (response == null
				|| response.getData() == null
				|| response.getData().getProcessing_info() == null
				|| response.getData().getProcessing_info().get("state") == null) {

			LOGGER.error("Invalid response or missing processing info or state");
			return false;
		}

		Map<String, Object> processingInfo = response.getData().getProcessing_info();
		String state = (String) processingInfo.get("state");
		Optional<List<Map<String, Object>>> errors = Optional.ofNullable(response.getErrors());

		switch (state.toLowerCase()) {
			case SUCCEEDED:
				LOGGER.info("Upload succeeded");
				return true;
			case FAILED:
				LOGGER.info("Upload failed");
				handleTwitterError(errors);
				break;
			case IN_PROGRESS:
				// Optionally handle the "in-progress" state
				LOGGER.info("Upload still in progress...");
				if(errors.isPresent()) {
					handleTwitterError(errors);
				}
				break;
			default:
				LOGGER.warn("Unknown state: {}", state);
				break;
		}
		return false;
	}

	private boolean processMediaStatusResponse(Map<?, ?> response) {
		Map<?, ?> processingInfo = (Map<?, ?>) response.get("processing_info");
		String state = processingInfo.get("state").toString();
		if (SUCCEEDED.equalsIgnoreCase(state)) {
			return true;
		} else if (FAILED.equalsIgnoreCase(state)) {
			handleTwitterError(processingInfo);
		} else if (IN_PROGRESS.equalsIgnoreCase(state)) {
			Object error = processingInfo.get("error");
			if (Objects.nonNull(error)) {
				handleTwitterError(processingInfo);
			}
		}
		return false;
	}



	private static void handleTwitterError(Optional<List<Map<String, Object>>> errors) {
		String errorMessage = errors.flatMap(errorList -> errorList.stream().findFirst())
				.map(error -> error.get("detail").toString())
				.orElse("Video upload failed");

		LOGGER.error("Error posting to twitter with error: {} and message: {}", errors, errorMessage);
		throw new BirdeyeSocialException(errorMessage);
	}

	private static void handleTwitterError(Map<?, ?> processingInfo) {
		String errorMessage = "Video upload failed";
		Object error = processingInfo.get("error");
		if (error instanceof Map) {
			Map<?, ?> errorMap = (Map<?, ?>) error;
			String msg = errorMap.get("message").toString();
			if (StringUtils.isNotEmpty(msg)) {
				errorMessage = msg;
			}
		}
		LOGGER.error("Error posting to twitter: {} ", errorMessage);
		throw new BirdeyeSocialException(errorMessage);
	}

	@Override
	public void postContentWithMedia(MediaUploadRequest request, String pageId,
									 SocialPostPublishInfo socialPostPublishInfo) throws Exception {
		List<BusinessTwitterAccounts> twitterAccounts = socialTwitterRepo.findByProfileId(Long.valueOf(pageId));
		if (CollectionUtils.isEmpty(twitterAccounts)) {
			return;
		}
		TwitterData data = twitterRequest(socialPostPublishInfo, null, null);
		TweetCreateRequest tweetCreateRequest = createTweetRequest(data);
		com.twitter.clientlib.model.TweetCreateResponse tweetCreateResponse = postContent(twitterCreds(null, twitterAccounts.get(0)), Collections.singletonList(request.getMediaId()), tweetCreateRequest);
		TweetInfo tweetInfo = MapTwitterData(tweetCreateResponse);
		if(Objects.nonNull(tweetInfo.getId())) {
			LOGGER.info("[postContentWithMedia] Content successfully posted over api call for twitter page {}, post {}", twitterAccounts.get(0).getProfileId(), tweetInfo.getId());
			successPostLogging(socialPostPublishInfo, tweetInfo, twitterAccounts.get(0));
		}
	}

	private void successPostLogging(SocialPostPublishInfo publishInfo, TweetInfo tweetInfo, BusinessTwitterAccounts twitterAccounts) {
		publishInfo.setIsPublished(1);
		publishInfo.setPostId(String.valueOf(tweetInfo.getId()));
		publishInfo.setPostUrl(twitterAccounts.getProfileUrl() + STATUS + tweetInfo.getId());
		SocialPostsAssets videoAsset = socialPostsAssetService.findById(Integer.parseInt(publishInfo.getSocialPost().getVideoIds()));
		videoAsset.setLastUsedDate(Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTime());
		socialPostInfoRepository.saveAndFlush(publishInfo);
		if(SocialPostStatusEnum.PUBLISHED.getId().equals(publishInfo.getIsPublished())
				&& commonService.isBusinessAllowedToSyncBusinessPosts(publishInfo.getEnterpriseId())) {
			kafkaProducer.sendObjectV1(SOCIAL_SYNC_BUSINESS_POSTS.getName(), SocialPostPublishInfoRequest.builder().id(publishInfo.getId()).build());
		}
		kafkaExternalService.publishSocialPostEvent(publishInfo);
	}

	private com.twitter.clientlib.model.TweetCreateResponse postContent(TwitterCreds creds, List<String> mediaId, TweetCreateRequest tweetCreateRequest) {
		if (CollectionUtils.isNotEmpty(mediaId)) {
			TweetCreateRequestMedia tweetCreateRequestMedia = new TweetCreateRequestMedia();
			tweetCreateRequestMedia.setMediaIds(mediaId);
			tweetCreateRequest.setMedia(tweetCreateRequestMedia);
		}
		ResponseEntity<com.twitter.clientlib.model.TweetCreateResponse> responseEntity = null;
		Authorization authorization = new Authorization(creds.getConsumerKey(), creds.getAccessToken(),
				creds.getAccessTokenSecret(),
				creds.getConsumerSecret());
		String url = TWITTER_POST;
		HttpHeaders headers = new HttpHeaders();
		try {
			headers.set(HttpHeaders.AUTHORIZATION, authorization.generateOauthHeader(POST, url, null));
			headers.set(HttpHeaders.CONTENT_TYPE, "application/json");
			headers.set("X-B3-Flags","1");
			HttpEntity requestEntity = new HttpEntity<>(tweetCreateRequest, headers);
			LOGGER.info("Request received from tweet :{} for body :{}", url, JSONUtils.toJSON(requestEntity));
			responseEntity = restClient.exchange(url, HttpMethod.POST, requestEntity,
					com.twitter.clientlib.model.TweetCreateResponse.class);
			LOGGER.info("Response received from tweet, body :{} , header: {}", responseEntity.getBody(),responseEntity.getHeaders());
		} catch (ApiException | TooManyRequestException ex) {
			LOGGER.info("ApiException occurred during tweet posting :{}", ex);
			handleError(ex.getMessage());
		} catch (HttpClientErrorException e) {
			LOGGER.info("HttpClientErrorException occurred during tweet posting :{}", e.getResponseBodyAsString());
			handleError(e.getResponseBodyAsString());
		} catch (HttpServerErrorException e) {
			LOGGER.info("HttpServerErrorException occurred during tweet posting :{}", e.getMessage());
			handleError(e.getMessage());
		} catch (Exception e) {
			LOGGER.info("Exception occurred during tweet posting :{}", e.getMessage());
			handleError(e.getMessage());
		}
		return responseEntity.getBody();
	}

	private boolean isTwitterV2MediaUploadEnabled(Integer accountId) {
		return CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class)
				.getBooleanProperty("enable.twitter.v2.media.upload", false) && isBusinessEligibleForV2Upload(accountId);
	}

	private TwitterTemplate createTwitterTemplate(TwitterCreds creds) {
		return new TwitterTemplate(creds.getConsumerKey(), creds.getConsumerSecret(),
				creds.getAccessToken(), creds.getAccessTokenSecret());
	}

	private TwitterTemplate getTemplateForMediaUploadV2(TwitterCreds creds) {
		TwitterTemplate template = createTwitterTemplate(creds);
		// Added ByteArrayHttpMessageConverter to handle application/octet-stream response for media upload
		((RestTemplate) template.restOperations())
				.getMessageConverters()
				.add(new ByteArrayHttpMessageConverter());
		return template;
	}

	private Boolean isBusinessEligibleForV2Upload(Integer accountId) {
		if(accountId == null) return true;
		String value = CacheManager.getInstance()
				.getCache(SystemPropertiesCache.class)
				.getProperty("business.eligible.twitter.v2.media.upload");

		LOGGER.info("isBusinessEligibleForV2Upload: {} and accountId: {}", value, accountId);
		return "ALL".equalsIgnoreCase(value) || value.contains(String.valueOf(accountId));
	}

}