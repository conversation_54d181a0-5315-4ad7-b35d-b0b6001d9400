package com.birdeye.social.service;
/**
 * <AUTHOR>
 *
 */
import com.birdeye.social.dao.SocialPostsAssetsRepository;
import com.birdeye.social.entities.PostLib;
import com.birdeye.social.entities.PostLibMaster;
import com.birdeye.social.entities.SocialPostsAssets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SocialPostsAssetsServiceImpl implements SocialPostsAssetsService{

    @Autowired
    private SocialPostsAssetsRepository socialPostsAssetsRepository;


    @Override
    public Map<Integer, SocialPostsAssets> getPostAssetsForPostLib(PostLibMaster postLibMaster, List<PostLib> postLibList) {
        Map<Integer, SocialPostsAssets> responseMap = new HashMap<>();

        List<Integer> ids = new ArrayList<>();
        String imageIdsMaster = postLibMaster.getImageIds();
        String videoIdsMaster = postLibMaster.getVideoIds();
        String compressedImageIdsMaster = postLibMaster.getCompressedImageIds();
        if (StringUtils.isNotEmpty(imageIdsMaster)) {
            String[] idsConversion = imageIdsMaster.split(",");
            for (String id : idsConversion) {
                ids.add(Integer.parseInt(id));
            }
        }
        if (StringUtils.isNotEmpty(videoIdsMaster)) {
            String[] idsConversion = videoIdsMaster.split(",");
            for (String id : idsConversion) {
                ids.add(Integer.parseInt(id));
            }
        }
        if (StringUtils.isNotEmpty(compressedImageIdsMaster)) {
            String[] idsConversion = compressedImageIdsMaster.split(",");
            for (String id : idsConversion) {
                ids.add(Integer.parseInt(id));
            }
        }
        for (PostLib postLib : postLibList) {
            String imageIds = postLib.getImageIds();
            String videoIds = postLib.getVideoIds();
            String compressedImageIds = postLib.getCompressedImageIds();
            if (StringUtils.isNotEmpty(imageIds)) {
                String[] idsConversion = imageIds.split(",");
                for (String id : idsConversion) {
                    ids.add(Integer.parseInt(id));
                }
            }
            if (StringUtils.isNotEmpty(videoIds)) {
                String[] idsConversion = videoIds.split(",");
                for (String id : idsConversion) {
                    ids.add(Integer.parseInt(id));
                }
            }
            if (StringUtils.isNotEmpty(compressedImageIds)) {
                String[] idsConversion = compressedImageIds.split(",");
                for (String id : idsConversion) {
                    ids.add(Integer.parseInt(id));
                }
            }
        }


        if (CollectionUtils.isNotEmpty(ids)) {
            List<SocialPostsAssets> socialPostsAssetsList = socialPostsAssetsRepository.findByIds(new HashSet<>(ids));
            if (CollectionUtils.isNotEmpty(socialPostsAssetsList)) {
                responseMap = socialPostsAssetsList.stream().collect(Collectors.toMap(SocialPostsAssets::getId, s -> s));
            }
        }

        return responseMap;
    }
}
