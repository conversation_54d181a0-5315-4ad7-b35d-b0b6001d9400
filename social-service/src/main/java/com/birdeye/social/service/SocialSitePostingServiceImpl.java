package com.birdeye.social.service;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.*;
import com.birdeye.social.dao.competitor.FacebookCompetitorMappingRepo;
import com.birdeye.social.dao.competitor.InstagramCompetitorMappingRepo;
import com.birdeye.social.dao.competitor.TwitterCompetitorMappingRepo;
import com.birdeye.social.dto.BusinessCustomFieldDto;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.businessProfile.BusinessProfileDTO;
import com.birdeye.social.dto.businessProfile.CategoryInformation;
import com.birdeye.social.dto.businessProfile.CustomField;
import com.birdeye.social.entities.*;
import com.birdeye.social.entities.competitor.FacebookCompetitorMapping;
import com.birdeye.social.entities.competitor.InstagramCompetitorMapping;
import com.birdeye.social.entities.competitor.TwitterCompetitorMapping;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.exception.ExternalAPIErrorCode;
import com.birdeye.social.external.request.google.ActionType;
import com.birdeye.social.external.request.google.LocalPostOffer;
import com.birdeye.social.external.request.google.LocalPostTopicType;
import com.birdeye.social.external.request.linkedin.TargetAudienceResponse;
import com.birdeye.social.external.request.nexus.CheckStatusRequest;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.google.GMBPost.GoogleLocalPost;
import com.birdeye.social.google.GMBPost.GoogleLocalPostRequest;
import com.birdeye.social.google.GMBPost.GooglePostCallToAction;
import com.birdeye.social.google.GMBPost.GooglePostMediaRequest;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.model.*;
import com.birdeye.social.model.Youtube.YoutubeCategory.YoutubeCategory;
import com.birdeye.social.model.Youtube.YoutubePlaylist.YoutubePlaylist;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.dao.*;
import com.birdeye.social.platform.entities.Business;
import com.birdeye.social.platform.entities.User;
import com.birdeye.social.service.SocialPostOperationService.PostOperation;
import com.birdeye.social.service.SocialPostOperationService.PostOperationFactory;
import com.birdeye.social.service.SocialPostOperationService.PostOperationUtil;
import com.birdeye.social.service.Youtube.YoutubeService;
import com.birdeye.social.service.Youtube.YoutubeVideoUpload;
import com.birdeye.social.service.applePost.SocialPostAppleService;
import com.birdeye.social.service.tiktok.SocialTiktokService;
import com.birdeye.social.utils.CoreUtils;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ForkJoinTask;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.birdeye.social.utils.ConversionUtils.convertEventDateAndTime;
import static com.birdeye.social.utils.ConversionUtils.convertGMBOffers;

@Service
public class SocialSitePostingServiceImpl implements SocialSitePostingService {

	@Autowired
	private BusinessFacebookPageRepository fbPageRepo;

	@Autowired
	private SocialFBPageRepository socialFBPageRepository;

	@Autowired
	private SocialPostInfoRepository socialPostPublishInfoRepository;

	@Autowired
	private SocialPostFacebookService socialPostFacebookService;

	@Autowired
	private BusinessTwitterPageRepository twitterPageRepository;
	
	@Autowired
	private SocialTwitterAccountRepository socialTwitterRepo;

	@Autowired
	private ISocialPostsAssetService socialPostsAssetService;

	@Autowired
	private SocialPostTwitterService socialPostTwitterService;

	@Autowired
	private SocialPostsAssetsRepository socialPostsAssetsRepository;

	@Autowired
	private BusinessLinkedinPageRepository linkedinPageRepository;

	@Autowired
	private SocialPostLinkedinService socialPostLinkedinService;

	@Autowired
	private BusinessRepository businessRepo;

	@Autowired
	private BusinessUserRepository businessUserRepo;

	@Autowired
	private BusinessInstagramAccountRepository instagramAccountRepository;

	@Autowired
	private SocialPostInstagramService socialPostInstagramService;

	@Autowired
	private CommonService commonService;

	@Autowired
	private BusinessGMBLocationRepository gmbRepo;

	@Autowired
	private BusinessGMBLocationRawRepository businessGMBLocationRawRepository;

	@Autowired
	private GMBPostService gmbPostService;

	@Autowired
	private SocialPostScheduleInfoRepo socialPostScheduleInfoRepository;

	@Autowired
	private IBusinessCoreService businessCoreService;

	@Autowired
	private SocialPostAuditRepository socialPostAuditRepository;

	@Autowired
	private IRedisExternalService redisExternalService;

	@Autowired
	private NexusService nexusService;
	@Autowired
	private KafkaExternalService kafkaExternalService;

	@Autowired
	private YoutubeVideoUpload youtubeVideoUpload;

	private YoutubeService youtubeService;

	@Autowired
	private BusinessYoutubeChannelRepository businessYoutubeChannelRepository;

	@Autowired
	private IPermissionMappingService permissionMappingService;

	@Autowired
	private BusinessRepository businessRepository;

	@Autowired
	private SocialPostActivityService socialPostActivityService;

	@Autowired
	private IRedisLockService redisService;

	@Autowired
	private DeletePostRequestRepo deletePostRequestRepo;
	@Autowired
	private KafkaProducerService kafkaProducerService;

	@Autowired
	private BusinessAppleLocationRepo appleLocationRepo;

	@Autowired
	private BusinessTiktokAccountsRepository tiktokAccountsRepository;

	@Autowired
	private SocialPostAppleService socialPostAppleService;

	@Autowired
	private SocialTiktokService socialTiktokService;

	@Autowired
	private PostOperationFactory postOperationFactory;
	@Autowired
	private EsService esService;
	@Autowired
	private PostOperationUtil postOperationUtil;

	@Autowired
	private FacebookCompetitorMappingRepo fbCompetitorMappingRepo;

	@Autowired
	private InstagramCompetitorMappingRepo igCompetitorMappingRepo;

	@Autowired
	private TwitterCompetitorMappingRepo twitterCompetitorMappingRepo;

	private static final String FAILED_EVENT_TOPIC = "notification-schedule";
	private static final String INVALID_REQUEST = "Invalid Request";
	private static final String DELETE_ACTIVITY = "DELETE";



	private static final Logger LOGGER = LoggerFactory.getLogger(SocialSitePostingServiceImpl.class);
	private static final String STATUS_POSTED_TEXT_FAILED = "Posted failed since text is not set";
	private static final String ACTION_POSTING_FAILED ="POSTING_FAILED";
	private static final String ERROR_ACCESSING_VIDEO_FILE = "Error in accessing video file";

	@Override
	public void postOnSocialSites(List<SocialPostPublishInfo> publishInfoList) {
		
		boolean linkPostingEnabled=CacheManager.getInstance().getCache(SystemPropertiesCache.class).isImageOrVedioLinkPostingEnabled();
		File videoFile = null;
		if (CollectionUtils.isNotEmpty(publishInfoList)) {
			if(publishInfoList.get(0).getSocialPost().getVideoIds() != null){
				videoFile = getFile(publishInfoList.get(0),Constants.VIDEO);
			}
			for (SocialPostPublishInfo publishInfo : publishInfoList) {
				if (publishInfo.getIsPublished() == null || publishInfo.getIsPublished() == 0) {
					if (publishInfo.getSourceId() == SocialChannel.FACEBOOK.getId()) { //facebook
						try {
							if (linkPostingEnabled) {
								socialPostFacebookService.postOnFacebookV1(publishInfo, videoFile, CoreUtils.getURLwithHTTPPrefix(publishInfo.getSocialPost().getLinkPreviewUrl()));
							} else {
								socialPostFacebookService.postOnFacebook(publishInfo, videoFile);
							}
						} catch (Exception ex) {
							handleFailedPost(publishInfo, ex);
							continue;
						}
					} else if (publishInfo.getSourceId() == SocialChannel.TWITTER.getId()) {
						try {
							socialPostTwitterService.postOnTwitter(publishInfo, videoFile,new LocationTagMetaDataRequest());
						} catch (Exception ex) {
							handleFailedPost(publishInfo, ex);
							continue;
						}
					} else if (publishInfo.getSourceId() == SocialChannel.LINKEDIN.getId()) { // Linkedin
						try {
							socialPostLinkedinService.postOnLinkedIn(publishInfo, videoFile);
						} catch (Exception ex) {
							handleFailedPost(publishInfo, ex);
							continue;
						}
					} else if (publishInfo.getSourceId() == SocialChannel.INSTAGRAM.getId()) {
						LOGGER.info("Instagram will be supported in Q3 2021.");
//						try {
//							// set publish status 1 :: published before posting in instagram
//							setInstagramPostAsPublished(publishInfo);
//						} catch (Exception ex) {
//							handleFailedPost(publishInfo, ex);
//						}
					} else if ( publishInfo.getSourceId() == SocialChannel.GOOGLE.getId() ) { // Google
						LOGGER.warn("G+ is dead.");
//						try {
//						try {
//							//TODO: G+ is no longer supported.
//							socialPostGMBService.postOnGMB(publishInfo, business);
//						} catch(Exception ex) {
//							handleFailedPost(publishInfo, ex);
//						}
					} else {
						// TODO: string representation for publishInfo for debugging.
						LOGGER.warn("Unsupportes source {} for business {} ", publishInfo.getSourceId(), publishInfo.getBusinessId());
					}
				}
			}
		}
	}

	public void publishFailedSocialPostEvent(SocialPostPublishInfo publishInfo) {
		LOGGER.info("inside emailAlertNotification : {}",publishInfo.getId());
		String topic =FAILED_EVENT_TOPIC;
		kafkaProducerService.sendObject(topic, publishInfo);
	}


	@Override
	public void scheduledPostOnSocialSites(SocialPostPublishInfo publishInfo, List<String> failedPageIds) throws Exception {
		if(!checkPageValidity(publishInfo)) {
			String channelName = SocialChannel.getSocialChannelNameById(publishInfo.getPageId());
			LOGGER.info("page is marked as invalid {}, therefore not posting", publishInfo.getBusinessId());
			List<PermissionMapping> pmList = permissionMappingService.getDataByChannelAndParentErrorCodeAndPermissionCodeNull("ALL", ErrorCodes.INVALID_PAGE.value(), null);
			String errorMessage = null;
			if(CollectionUtils.isNotEmpty(pmList)) {
				errorMessage = pmList.get(0).getErrorMessage();
			} else {
				PermissionMapping pm = permissionMappingService.getDataByChannelAndPermissionCode(channelName, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
				if(Objects.nonNull(pm)) {
					errorMessage =  pm.getErrorMessage();
				}
			}
			publishInfo.setIsPublished(2);
			publishInfo.setFailureReason(errorMessage);
			socialPostPublishInfoRepository.saveAndFlush(publishInfo);
			publishFailedSocialPostEvent(publishInfo);
			updateSocialPostAudit(publishInfo.getSocialPostId(), "Posted on "+channelName, "POSTING_FAILED",
					publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());
			if(Objects.nonNull(failedPageIds) && CollectionUtils.isNotEmpty(failedPageIds))
				failedPageIds.add(publishInfo.getExternalPageId());
			return ;
		}
		boolean linkPostingEnabled=CacheManager.getInstance().getCache(SystemPropertiesCache.class).isImageOrVedioLinkPostingEnabled();
		File videoFile = null;
		if (Objects.nonNull(publishInfo)) {
			if( (publishInfo.getSourceId().equals(SocialChannel.YOUTUBE.getId()) ||
					publishInfo.getSourceId().equals(SocialChannel.TWITTER.getId()))
					&& Objects.nonNull(publishInfo.getSocialPost().getVideoIds()) && !publishInfo.getSocialPost().getVideoIds().isEmpty()){
				try {
					videoFile = getFile(publishInfo,Constants.VIDEO);
				} catch (Exception e) {
					String channel = SocialChannel.getSocialChannelNameById(publishInfo.getSourceId());
					PermissionMapping pm = errorHandler(channel, ERROR_ACCESSING_VIDEO_FILE);
					LOGGER.warn("Error downloading file for {} due to {} ", publishInfo.getId(), e.getMessage());
					publishInfo.setIsPublished(2);
					publishInfo.setFailureReason(Objects.nonNull(pm)?pm.getErrorMessage(): "An unknown error occurred");
					socialPostPublishInfoRepository.saveAndFlush(publishInfo);
					publishFailedSocialPostEvent(publishInfo);
					updateSocialPostAudit(publishInfo.getSocialPostId(), "Error downloading video file", "POSTING_FAILED",
							publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());
					if(Objects.nonNull(failedPageIds) && CollectionUtils.isNotEmpty(failedPageIds))
						failedPageIds.add(publishInfo.getExternalPageId());
					throw new Exception(e.getMessage());
				}
			}
			LocationTagMetaDataRequest request = new LocationTagMetaDataRequest();
			if(Objects.nonNull(publishInfo.getSocialPost().getPostMetadata())){
				request = getLocationTagMetaData(publishInfo.getSocialPost().getPostMetadata());
			}

			SocialPost socialPost = publishInfo.getSocialPost();
			// Replace tokens if applicable
			List<MentionData> mentionDataList = new ArrayList<>();
			String postText=Objects.nonNull(publishInfo.getSocialPost().getPostText())?publishInfo.getSocialPost().getPostText():"";
			if(Objects.nonNull(publishInfo.getSocialPost().getMentions()) && !publishInfo.getSocialPost().getMentions().isEmpty()) {
				mentionDataList = JSONUtils.collectionFromJSON(publishInfo.getSocialPost().getMentions(), MentionData.class);
			}
			boolean isReviewAutoShare = Objects.nonNull(socialPost.getReviewId()) && BooleanUtils.isTrue(socialPost.getAutoShare());
			//don't replace tokens if it's a case of review auto share
			if (!isReviewAutoShare) {
				postText = getProfileData(publishInfo.getSocialPost().getPostText(),publishInfo.getBusinessId(),mentionDataList,publishInfo.getSourceId());
			}
			socialPost.setPostText(postText);
			LOGGER.info("Posting for channel: {} with text: {} ", publishInfo.getSourceId(), socialPost.getPostText());
			publishInfo.setSocialPost(socialPost);
			if(Objects.isNull(publishInfo.getSocialPost().getPostText())) {
				publishInfo.setIsPublished(2);
				PermissionMapping pm = errorHandlerService(Constants.ALL_CHANNEL,ErrorCodes.POST_TEXT_NOT_SET.value(), -1, ExternalAPIErrorCode.POST_TEXT_NOT_SET.getDescription());
				publishInfo.setFailureReason(pm.getErrorMessage());
				publishInfo.setBucket(pm.getBucket());
			//	publishInfo.setFailureReason("Cannot post since post text is not set");
				publishInfo.setFailureCode(0);
				socialPostPublishInfoRepository.saveAndFlush(publishInfo);
				kafkaExternalService.publishSocialPostEvent(publishInfo);
				updateSocialPostAudit(publishInfo.getSocialPostId(), "Posted on fb", "POSTING_FAILED",
						publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());
				if(Objects.nonNull(failedPageIds) && CollectionUtils.isNotEmpty(failedPageIds))
					failedPageIds.add(publishInfo.getExternalPageId());
			} else {
				if (publishInfo.getIsPublished() == null || publishInfo.getIsPublished() == 0) {
					//TODO: New channels are aded here
						if (publishInfo.getSourceId() == SocialChannel.FACEBOOK.getId()) { //facebook
							try {
								if (linkPostingEnabled) {
									socialPostFacebookService.postOnFacebookV1(publishInfo, videoFile, CoreUtils.getURLwithHTTPPrefix(publishInfo.getSocialPost().getLinkPreviewUrl()));
								} else {
									socialPostFacebookService.postOnFacebook(publishInfo, videoFile);
								}
								updateSocialPostAudit(publishInfo.getSocialPostId(), "Posted on fb", "POSTED_SUCCESSFULLY",
										publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());
							} catch (Exception ex) {
								handleFailedPost(publishInfo, ex);
								if(Objects.nonNull(failedPageIds) && CollectionUtils.isNotEmpty(failedPageIds))
									failedPageIds.add(publishInfo.getExternalPageId());
							}
						} else if (publishInfo.getSourceId() == SocialChannel.TWITTER.getId()) {
							try {
								socialPostTwitterService.postOnTwitter(publishInfo, videoFile,request);
							} catch (Exception ex) {
								handleFailedPost(publishInfo, ex);
								if(Objects.nonNull(failedPageIds))
									failedPageIds.add(publishInfo.getExternalPageId());
							}
						} else if (publishInfo.getSourceId() == SocialChannel.LINKEDIN.getId()) { // Linkedin
							try {
								socialPostLinkedinService.postOnLinkedIn(publishInfo, videoFile);
							} catch (Exception ex) {
								handleFailedPost(publishInfo, ex);
								if(Objects.nonNull(failedPageIds) && CollectionUtils.isNotEmpty(failedPageIds))
									failedPageIds.add(publishInfo.getExternalPageId());
							}
						} else if (publishInfo.getSourceId() == SocialChannel.INSTAGRAM.getId()) { //Instagram
							try {
								socialPostInstagramService.postOnInstagram(publishInfo);
							} catch (Exception ex) {
								handleFailedPost(publishInfo, ex);
								if(Objects.nonNull(failedPageIds) && CollectionUtils.isNotEmpty(failedPageIds))
									failedPageIds.add(publishInfo.getExternalPageId());
							}
						} else if (publishInfo.getSourceId() == SocialChannel.YOUTUBE.getId()){
							try {
								youtubeVideoUpload.postVideo(publishInfo,videoFile);
							}catch (Exception e){
								handleFailedPost(publishInfo,e);
								if(Objects.nonNull(failedPageIds) && CollectionUtils.isNotEmpty(failedPageIds))
									failedPageIds.add(publishInfo.getExternalPageId());
							}
						}else if (publishInfo.getSourceId() == SocialChannel.GOOGLE.getId()) { // Google
							googlePosting(publishInfo);

						} else if (publishInfo.getSourceId() == SocialChannel.APPLE_CONNECT.getId()) {
							try {
								socialPostAppleService.postOnApple(publishInfo);
							}catch (Exception e){
								handleFailedPost(publishInfo,e);
								if(Objects.nonNull(failedPageIds) && CollectionUtils.isNotEmpty(failedPageIds))
									failedPageIds.add(publishInfo.getExternalPageId());
							}
						} else if(publishInfo.getSourceId() == SocialChannel.TIKTOK.getId()) {
							try {
								socialTiktokService.postOnTikTok(publishInfo);
							} catch (Exception e) {
								handleFailedPost(publishInfo,e);
								if(Objects.nonNull(failedPageIds) && CollectionUtils.isNotEmpty(failedPageIds))
									failedPageIds.add(publishInfo.getExternalPageId());
							}
						}
						else {

						// TODO: string representation for publishInfo for debugging.
						LOGGER.warn("Unsupportes source {} for business {} ", publishInfo.getSourceId(), publishInfo.getBusinessId());
					}
				}
			}

		}
	}

	private void googlePosting(SocialPostPublishInfo publishInfo){
		try {
			boolean postQuotaExceeded = gmbPostQuotaCheck(publishInfo.getBusinessId(), publishInfo.getSourceId());
			if(postQuotaExceeded) {
				throw new BirdeyeSocialException(ErrorCodes.GMB_API_LIMIT_EXCEEDED.value(), "Post limit exceeded");
			}
			GoogleLocalPostRequest googleLocalPostRequest = new GoogleLocalPostRequest();
			GMBPostMetadataRequest metaData = getGMBPostMetaData(publishInfo.getSocialPost().getPostMetadata());
			googleLocalPostRequest.setSummary(StringUtils.isEmpty(publishInfo.getSocialPost().getPostText()) ? null : publishInfo.getSocialPost().getPostText());
			googleLocalPostRequest.setLanguageCode("en-US");

			if(Objects.nonNull(metaData)) {
				if(StringUtils.isNotEmpty(metaData.getButtonType())) {
					ActionType actionTypeEnum;
					try {
						actionTypeEnum = ActionType.valueOf(metaData.getButtonType().toUpperCase());
					} catch (IllegalArgumentException e) {
						throw new BirdeyeSocialException(ErrorCodes.GMB_INCORRECT_ACTION_TYPE, "Invalid button type: " + metaData.getButtonType() +
								" for social post with businessId: " + publishInfo.getBusinessId());
					}
					GooglePostCallToAction callToAction = new GooglePostCallToAction();
					callToAction.setActionType(metaData.getButtonType());

					if (!ActionType.CALL.equals(actionTypeEnum) && StringUtils.isNotEmpty(metaData.getButtonUrl())) {
						String callToActionUrl = getProfileData(metaData.getButtonUrl(), publishInfo.getBusinessId(), null, null);
						if (StringUtils.isEmpty(callToActionUrl)) {
							dataMissingException(publishInfo, ErrorCodes.CALL_TO_ACTION_URL.value(), ExternalAPIErrorCode.CALL_TO_ACTION_URL.getDescription());
							return;
						}
						callToAction.seturl(callToActionUrl);
					}

					googleLocalPostRequest.setCallToAction(callToAction);
				}
				googleLocalPostRequest.setTopicType(metaData.getTopicType());
				if(LocalPostTopicType.OFFER.name().equalsIgnoreCase(metaData.getTopicType()) && Objects.nonNull(metaData.getOffer())) {
					createOfferPayload(metaData,publishInfo,googleLocalPostRequest);
					if(Objects.nonNull(publishInfo.getIsPublished()) && publishInfo.getIsPublished() == 2)
						return;
				}

			}

			if(Objects.nonNull(publishInfo.getSocialPost().getImageIds())) {
				List<GooglePostMediaRequest> mediaRequests = getMediaRequest(publishInfo.getSocialPost().getImageIds(), publishInfo.getEnterpriseId());
				if (!mediaRequests.isEmpty()) {
					googleLocalPostRequest.setMedia(mediaRequests);
				}
			}

			GoogleLocalPost postInfo = gmbPostService.createPost(googleLocalPostRequest, publishInfo.getBusinessId());
			if(Objects.nonNull(postInfo)) {
				GooglePostStatus postStatus = GooglePostStatus.valueOf(postInfo.getState());
				switch (postStatus){
					case PROCESSING :
					case LOCAL_POST_STATE_UNSPECIFIED:
						LOGGER.info("Info for the post id :{} and status : {}",postInfo.getName() , postInfo.getState());
						publishInfo.setIsPublished(SocialPostStatusEnum.PROCESSING.getId());
						break;
					case REJECTED:
						publishInfo.setIsPublished(SocialPostStatusEnum.FAILED.getId());
						break;
					case LIVE:
						publishInfo.setIsPublished(SocialPostStatusEnum.PUBLISHED.getId());
						break;
				}
				publishInfo.setPostId(postInfo.getName());
				publishInfo.setPostUrl(postInfo.getSearchUrl());
				saveToSocialPublishInfo(publishInfo);
				kafkaExternalService.publishSocialPostEvent(publishInfo);
				updateSocialPostAudit(publishInfo.getSocialPostId(), "Posting on google", "INPROGRESS",
						publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());
			}
		} catch(BirdeyeSocialException ex) {
			//need to updated birdeye exception here. retry can be when addition columns are added i.e
			// 1. retry counter 2. type of exception (retryable, non retryable) 3. quota limit per day
			if(MapUtils.isEmpty(ex.getData())) {
				Map<String, Object> errorMap = new HashMap<>();
				errorMap.put("error_code", ex.getErrorCode());
				errorMap.put("error_message", ex.getMessage());
				ex.setData(errorMap);
			}
			boolean isEligibleForRetry = commonService.retryPostIfEligible(ex, publishInfo);
			if(!isEligibleForRetry) {
				PermissionMapping pm = errorHandlerForGoogleService(ex);
				if(Objects.nonNull(pm) && pm.getMarkInvalid() == 1) {
					kafkaExternalService.markPageInvalid(SocialChannel.GMB.getName(), publishInfo.getExternalPageId());
				}
				publishInfo.setIsPublished(2);
				publishInfo.setBucket(pm.getBucket());

				publishInfo.setFailureReason(pm.getErrorMessage());

				publishInfo.setFailureCode(ex.getCode());
				socialPostPublishInfoRepository.saveAndFlush(publishInfo);
				kafkaExternalService.publishSocialPostEvent(publishInfo);
				//handleFailedPost(publishInfo, ex);
				updateSocialPostAudit(publishInfo.getSocialPostId(), "Failed to publish on google", "POSTING_FAILED",
						publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());
			}
		}
	}

	private void dataMissingException(SocialPostPublishInfo publishInfo, int value, String description) {
		publishInfo.setIsPublished(2);
		PermissionMapping pm = errorHandlerService(Constants.GMB,value, -1, description);
		publishInfo.setFailureReason(pm.getErrorMessage());
		publishInfo.setBucket(pm.getBucket());
		//	publishInfo.setFailureReason("Cannot post since post text is not set");
		publishInfo.setFailureCode(0);
		socialPostPublishInfoRepository.saveAndFlush(publishInfo);
		kafkaExternalService.publishSocialPostEvent(publishInfo);
		updateSocialPostAudit(publishInfo.getSocialPostId(), "Failed to publish on google", "POSTING_FAILED",
				publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());
	}

	private void createOfferPayload(GMBPostMetadataRequest metaData,SocialPostPublishInfo publishInfo,
									GoogleLocalPostRequest googleLocalPostRequest) {
		LocalPostOffer offer = convertGMBOffers(metaData.getOffer());
		if(StringUtils.isNotEmpty(metaData.getOffer().getLink())){
			String url = getProfileData(metaData.getOffer().getLink(),publishInfo.getBusinessId(),null,null);
			if(StringUtils.isEmpty(url)){
				dataMissingException(publishInfo,ErrorCodes.OFFER_URL_INVALID.value(), ExternalAPIErrorCode.OFFER_URL_INVALID.getDescription());
				return;
			}
			offer.setRedeemOnlineUrl(url);
		}
		if(StringUtils.isNotEmpty(metaData.getOffer().getTermsConditions())){
			String url = getProfileData(metaData.getOffer().getTermsConditions(),publishInfo.getBusinessId(),null,null);
			if(StringUtils.isEmpty(url)){
				dataMissingException(publishInfo,ErrorCodes.TERMS_AND_CONDITION_URL_INVALID.value(), ExternalAPIErrorCode.TERMS_AND_CONDITION_URL_INVALID.getDescription());
				return;
			}
			offer.setTermsConditions(url);
		}
		googleLocalPostRequest.setOffer(offer);
		googleLocalPostRequest.setEvent(convertEventDateAndTime(metaData));
	}

	@Retryable(value = BirdeyeSocialException.class,maxAttempts = 3,backoff = @Backoff(delay = 1000))
	private void saveToSocialPublishInfo(SocialPostPublishInfo publishInfo) {
		try {
			socialPostPublishInfoRepository.saveAndFlush(publishInfo);
		} catch (Exception e) {
			LOGGER.error("Exception Occurred while updating social_post_publish info {}", e.getMessage());
			throw new BirdeyeSocialException(ErrorCodes.SQL_EXCEPTION, e.getMessage());
		}
	}
	private boolean checkPageValidity(SocialPostPublishInfo publishInfo) {
		String channelName = SocialChannel.getSocialChannelNameById(publishInfo.getSourceId());
		boolean isValidPage = true;
		switch (SocialChannel.getSocialChannelByName(channelName)) {
			case FACEBOOK:
				BusinessFBPage businessFacebookPage = socialFBPageRepository.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
				if(Objects.isNull(businessFacebookPage) || businessFacebookPage.getIsValid()==0) isValidPage = false;
				break;
			case TWITTER:
				BusinessTwitterAccounts twitterAccounts = socialTwitterRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
				if(Objects.isNull(twitterAccounts) || twitterAccounts.getIsValid()==0) isValidPage = false;
				break;
			case LINKEDIN:
				BusinessLinkedinPage linkedinPage = linkedinPageRepository.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
				if(Objects.isNull(linkedinPage) || linkedinPage.getIsValid()==0) isValidPage = false;
				break;
			case INSTAGRAM:
				BusinessInstagramAccount businessInstagramAccount = instagramAccountRepository.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
				if(Objects.isNull(businessInstagramAccount) || businessInstagramAccount.getIsValid()==0) isValidPage = false;
				break;
			case YOUTUBE:
				BusinessYoutubeChannel businessYoutubeChannel = businessYoutubeChannelRepository.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
				if(Objects.isNull(businessYoutubeChannel) || businessYoutubeChannel.getIsValid()==0) isValidPage = false;
				break;
			case GMB:
				BusinessGoogleMyBusinessLocation gmbLocation = businessGMBLocationRawRepository.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
				if(Objects.isNull(gmbLocation) || gmbLocation.getIsValid()==0) isValidPage = false;
				break;
			case APPLE_CONNECT:
				BusinessAppleLocation appleLocation = appleLocationRepo.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
				if(Objects.isNull(appleLocation)) isValidPage = false;
				break;
			case TIKTOK:
				BusinessTiktokAccounts tiktokAccounts = tiktokAccountsRepository.findByIdAndBusinessId(publishInfo.getPageId(), publishInfo.getBusinessId());
				if(Objects.isNull(tiktokAccounts)) isValidPage = false;
				break;
			default:
				isValidPage =  false;
		}
		return isValidPage;
	}

	private PermissionMapping errorHandlerService(String channel, Integer failureCode, Integer errorSubCode, String message) {
		PermissionMapping pmDefault = new PermissionMapping();

		List<PermissionMapping> permissionMappings =permissionMappingService.getDataByChannelAndParentErrorCodeAndPermissionCode(channel,
				failureCode, errorSubCode);

		if(CollectionUtils.isEmpty(permissionMappings)) {
			return permissionMappingService.getDataByChannelAndPermissionCodeAndModule(channel,
					Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR,Constants.PUBLISH_MODULE);
		}
		if(permissionMappings.size() == 1) return permissionMappings.get(0);

		for(PermissionMapping pm: permissionMappings) {
			if(com.birdeye.social.utils.StringUtils.isNotEmpty(message) && message.contains(pm.geterrorActualMessage())){
				return pm;
			}
		}
		return pmDefault;

	}

	private PermissionMapping errorHandlerForGoogleService(BirdeyeSocialException ex) {
		Integer errorCode = ex.getErrorCode();
		String message = ex.getMessage();
		// no need to check sub-code, text can be parsed on the basis of parentCode and error text.
		List<PermissionMapping> permissionMappings =permissionMappingService.getDataByChannelAndParentErrorCodeAndPermissionCodeAndModule(Constants.GMB,
					errorCode, -1,SocialMessageModule.PUBLISH);

		if(CollectionUtils.isEmpty(permissionMappings)) {
			LOGGER.info("New error found for GMB posting with errorCode: {} subcode: {} and message: {}", errorCode, message);
			return permissionMappingService.getDataByChannelAndPermissionCodeAndModule(SocialChannel.GMB.getName(),
					Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR,Constants.PUBLISH_MODULE);
		}
		if(permissionMappings.size() == 1) return permissionMappings.get(0);

		for(PermissionMapping pm: permissionMappings) {
			if(StringUtils.isNotEmpty(message) && message.contains(pm.geterrorActualMessage())){
				return pm;
			}
		}
		return null;

	}

	private PermissionMapping errorHandler(String channel, String message) {
		PermissionMapping pm = permissionMappingService.getDataByChannelAndActualErrorText(channel, ERROR_ACCESSING_VIDEO_FILE);
		if(Objects.isNull(pm)) {
			pm = permissionMappingService.getDataByChannelAndPermissionCode(channel, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
		}
		return pm;
	}

	private String convertMentionToPostText(String postText, List<MentionData> mentionDataList, Integer sourceId) {
		LOGGER.info("Convert text for social channel {}",sourceId);
		String text = postText;
		if(CollectionUtils.isEmpty(mentionDataList) || Objects.isNull(text) || text.isEmpty()){
			return text;
		}
		try{
			for (MentionData mentionData : mentionDataList) { // twitter , facebook mention
				if (text.contains(makeText(mentionData.getValue()))
						&& SocialChannel.FACEBOOK.getName().equalsIgnoreCase(SocialChannel.getSocialChannelNameById(sourceId))
						&& mentionData.getChannel().equalsIgnoreCase(SocialChannel.getSocialChannelNameById(sourceId))) {
					text = text.replace(makeText(mentionData.getValue()), "@[" + mentionData.getValue() + "]");
				}else if (text.contains(makeText(mentionData.getValue())) &&
						mentionData.getChannel().equalsIgnoreCase(SocialChannel.getSocialChannelNameById(sourceId))){
					text = text.replace(makeText(mentionData.getValue()), "@" + mentionData.getValue());
				} else if (text.contains(makeText(mentionData.getValue())) &&
						!mentionData.getChannel().equalsIgnoreCase(SocialChannel.getSocialChannelNameById(sourceId))) {
					text = text.replace(makeText(mentionData.getValue()), "");
				}
			}
		}catch (Exception e) {
			LOGGER.error("Error occurred while converting post text for channel {} with error",sourceId, e);
			return null;
		}
		return text;
	}

	private String makeText(String value) {
		value = "[@"+value+"]";
		return value;
	}

//	public static boolean onlyDigits(String str) {
//		String regex = "[0-9]+";
//		Pattern p = Pattern.compile(regex);
//		if (str == null) {
//			return false;
//		}
//		Matcher m = p.matcher(str);
//		return m.matches();
//	}


	private LocationTagMetaDataRequest getLocationTagMetaData(String postMetadata) {
		LocationTagMetaDataRequest request  = new LocationTagMetaDataRequest();
		SocialPostSchedulerMetadata map = JSONUtils.fromJSON(postMetadata, SocialPostSchedulerMetadata.class);
		if(Objects.nonNull(map) && Objects.nonNull(map.getLocationTagMetaData())){
			request = JSONUtils.fromJSON(map.getLocationTagMetaData(),LocationTagMetaDataRequest.class);
		}
		return request;
	}


	private boolean gmbPostQuotaCheck(Integer businessId, Integer sourceId) {
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
		Date date = new Date();
		String key = "GMB_POST_QUOTA:".concat(businessId.toString()).concat(sourceId.toString()).concat(dateFormat.format(date));
		Optional<Object> data = redisExternalService.get(key);
		LOGGER.info("Social Post: GMB quota key: {}", key);

		if(data.isPresent()){
			LOGGER.info("Social Post: GMB post quota for businessId: {} is: {}", businessId, data.get());

			Integer limit =  Integer.parseInt((String) data.get());
			if(limit <= 0) {
				return true;
			} else {
				limit -= 1;
				redisExternalService.set(key, limit.toString());
				return false;
			}
		} else {
			String limit = "25";
			redisExternalService.set(key, limit);
			return false;
		}
	}

	private void updateSocialPostAudit(Integer postId, String status, String action, Integer sourceId, Integer businessId, Integer enterpriseId) {
		try {
			SocialPostAudit socialPostAudit = socialPostAuditRepository.findFirstByPostId(postId);
			if (Objects.nonNull(socialPostAudit)) {
				socialPostAudit.setAction(action);
				socialPostAudit.setSourceId(sourceId);
				socialPostAudit.setBusinessId(businessId);
				socialPostAudit.setEnterpriseId(enterpriseId); // need to check that
				socialPostAudit.setStatus(socialPostAudit.getStatus().concat(" | ").concat(status));
				socialPostAuditRepository.save(socialPostAudit);
			}
		} catch (Exception e) {
			LOGGER.info("Exception occurred while updating audit {}", e.getMessage(), e);
		}

	}

	private String addCustomFields(String input, List<CustomField> customFields) {
		String newTextWithCustom= input;
		if(CollectionUtils.isNotEmpty(customFields)) {
			for (CustomField customField: customFields) {
				String mapKey = customField.getFieldName();
				String mapValue = Objects.nonNull(customField.getFieldValue())?customField.getFieldValue():
						Objects.nonNull(customField.getDefaultValue())?customField.getDefaultValue(): "[null]";
				newTextWithCustom = newTextWithCustom.replace("[" + mapKey + "]", mapValue);
			}
		}
		return newTextWithCustom;
	}

	private String getCategory(CategoryInformation categoryInformation) {
		String category = "";
		List<String> categoryList = new ArrayList<>();
		if(Objects.nonNull(categoryInformation) && Objects.nonNull(categoryInformation.getParentCategory())) {
			categoryList.add(categoryInformation.getParentCategory().getName());
			if(CollectionUtils.isNotEmpty(categoryInformation.getChildCategory())) {
				categoryInformation.getChildCategory().forEach(childCategory-> categoryList.add(childCategory.getName()));
			}
			category = String.join(",",categoryList);
		}
		return category;
	}

	@Override
	public String getProfileData(String input, Integer businessId,List<MentionData> mentionData,Integer sourceId) {
		String newPostText = input;

		try {
			if(StringUtils.isEmpty(newPostText)) {
				return "";
			}

			String patternString = "(?s).*(\\[.*?\\]).*";
			Pattern pattern = Pattern.compile(patternString);
			Matcher matcher = pattern.matcher(newPostText);

			if(matcher.matches()) {
				newPostText = convertMentionToPostText(newPostText,mentionData,sourceId);
				LOGGER.info("Post text after mention conversion: {}", newPostText);
				Business	business =  businessRepo.findById(businessId);
				BusinessProfileDTO bP = businessCoreService.getProfileData(business.getBusinessId());
				if(newPostText.contains("[Location name]")) {
					newPostText = newPostText.replace("[Location name]", Objects.nonNull(bP.getBusinessData().getName()) ? bP.getBusinessData().getName() : "[null]");
				}
				if(newPostText.contains("[Microsite URL]")){
					newPostText = newPostText.replace("[Microsite URL]", (Objects.nonNull(bP.getInternal()) && StringUtils.isNotEmpty(bP.getInternal().getPublicProfileUrl())) ? bP.getInternal().getPublicProfileUrl() : "[null]");
				}
				if(newPostText.contains("[Website URL]")) {
					newPostText = newPostText.replace("[Website URL]", Objects.nonNull(bP.getBusinessData().getWebsiteUrl()) ? bP.getBusinessData().getWebsiteUrl() : "[null]");
					if(!newPostText.contains("[null]") && !isValidDomainName(newPostText)) {
						newPostText = "[null]";
					}
				}
				if(newPostText.contains("[Address]")) {
					if(Objects.nonNull(bP.getBusinessData().getLocations()) && Objects.nonNull(bP.getBusinessData().getLocations().getAddress1()) ) {
						newPostText = newPostText.replace("[Address]", bP.getBusinessData().getLocations().getAddress1());
					} else {
						newPostText = newPostText.replace("[Address]", "[null]");

					}
				}
				if(newPostText.contains("[City]")) {
					if(Objects.nonNull(bP.getBusinessData().getLocations()) && Objects.nonNull(bP.getBusinessData().getLocations().getCity()) ) {
						newPostText = newPostText.replace("[City]", bP.getBusinessData().getLocations().getCity());
					} else {
						newPostText = newPostText.replace("[City]", "[null]");

					}
				}
				if(newPostText.contains("[Zipcode]")) {
					if(Objects.nonNull(bP.getBusinessData().getLocations()) && Objects.nonNull(bP.getBusinessData().getLocations().getZip()) ) {
						newPostText = newPostText.replace("[Zipcode]", bP.getBusinessData().getLocations().getZip());
					} else {
						newPostText = newPostText.replace("[Zipcode]", "[null]");

					}
				}
				if(newPostText.contains("[State]")) {
					if(Objects.nonNull(bP.getBusinessData().getLocations()) && Objects.nonNull(bP.getBusinessData().getLocations().getState()) ) {
						newPostText = newPostText.replace("[State]", bP.getBusinessData().getLocations().getState());
					} else {
						newPostText = newPostText.replace("[State]", "[null]");

					}
				}
				if(newPostText.contains("[Country]")) {
					if(Objects.nonNull(bP.getBusinessData().getLocations()) && Objects.nonNull(bP.getBusinessData().getLocations().getCountryName()) ) {
						newPostText = newPostText.replace("[Country]", bP.getBusinessData().getLocations().getCountryName());
					} else {
						newPostText = newPostText.replace("[Country]", "[null]");

					}
				}
				if(newPostText.contains("[Phone]")) {
					newPostText = newPostText.replace("[Phone]", Objects.nonNull(bP.getBusinessData().getPhone()) ? bP.getBusinessData().getPhone() : "[null]");
				}

				 if(newPostText.contains("[Category]")) {
					newPostText = newPostText.replace("[Category]", Objects.nonNull(bP.getInternal().getCategoryInformation()) ? getCategory(bP.getInternal().getCategoryInformation()) : "[null]");
				}
				 if(newPostText.contains("[Business Description]")) {
					newPostText = newPostText.replace("[Business Description]", Objects.nonNull(bP.getInternal().getDescription()) ? bP.getInternal().getDescription() :  "[null]");
				}
				 if(newPostText.contains("[Payment methods]")) {
					newPostText = newPostText.replace("[Payment methods]", Objects.nonNull(bP.getAdditionalData().getPayment()) ? String.join(",",bP.getAdditionalData().getPayment()) : "[null]");
				}
				 if(newPostText.contains("[Facebook profile]")) {
					newPostText = newPostText.replace("[Facebook profile]", Objects.nonNull( bP.getSocialProfilesData().getFacebookUrl()) ?  bP.getSocialProfilesData().getFacebookUrl() : "[null]");
				}
				 if(newPostText.contains("[Appointment URL]")) {
					 if(StringUtils.isNotEmpty(bP.getAdditionalData().getAppointmentLink())) {
						 newPostText = newPostText.replace("[Appointment URL]", bP.getAdditionalData().getAppointmentLink());
					 } else if(StringUtils.isNotEmpty(bP.getAdditionalData().getReservationLink())) {
						 newPostText = newPostText.replace("[Appointment URL]", bP.getAdditionalData().getReservationLink());
					 } else {
						 newPostText = newPostText.replace("[Appointment URL]", "[null]");
					 }
				}
				 if(newPostText.contains("[X (Twitter) profile]")) {
					newPostText = newPostText.replace("[X (Twitter) profile]",  Objects.nonNull(bP.getSocialProfilesData().getTwitterUrl()) ? bP.getSocialProfilesData().getTwitterUrl() : "[null]");
				}
				 if(newPostText.contains("[Menu URL]")) {
					newPostText = newPostText.replace("[Menu URL]", Objects.nonNull(bP.getAdditionalData().getMenuLink()) ? bP.getAdditionalData().getMenuLink() : "[null]");
				}
				 if(newPostText.contains("[Order Online URL]")) {
					newPostText = newPostText.replace("[Order Online URL]", Objects.nonNull(bP.getAdditionalData().getReservationLink()) ? bP.getAdditionalData().getReservationLink() : "[null]" );
				}
			Pattern patternForCustomFields = Pattern.compile(patternString);
			matcher = patternForCustomFields.matcher(newPostText);
			if(matcher.matches()) {
				BusinessCustomFieldDto customFields = businessCoreService.getCustomFieldsTokenData(business.getId());
				if(Objects.nonNull(customFields)){
					newPostText = addCustomFields(newPostText, customFields.getCustomFields());
				}
			}
			//Remove code block below once the UI fix is live
			if(newPostText.toLowerCase().contains("[location name]")){
					Pattern locationNamePattern = Pattern.compile("\\[location name\\]", Pattern.CASE_INSENSITIVE);
					Matcher locationNamePatternMatcher = locationNamePattern.matcher(newPostText);
					newPostText = locationNamePatternMatcher.replaceAll(Objects.nonNull(bP.getBusinessData().getName()) ? bP.getBusinessData().getName() : "[null]");
			}

			LOGGER.info("Social Post: post text captured with template, updated text is {}", newPostText);
			String patternStringForMention = "(?s).*([^@]\\[.*?\\]).*";
			Pattern patternForMention = Pattern.compile(patternStringForMention);
			matcher = patternForMention.matcher(newPostText);
			if(matcher.matches()) {
				return null;
			}
			}
		} catch (Exception e) {
			LOGGER.error("Social Post: Error in replacing business tokens, for input text {} and businessId {}", input,
					businessId, e);
			return null;
		}
		return newPostText.contains("[null]") ? null : newPostText;

	}

	public static boolean isValidDomainName(String caption) {
		String urlRegex = "(https?:\\/\\/(www\\.)?[a-zA-Z0-9-@:%._\\+~#=]{2,256}\\.[a-z]{2,6}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)?)";
		Pattern pattern = Pattern.compile(urlRegex, Pattern.CASE_INSENSITIVE);
		Matcher matcher = pattern.matcher(caption);
		// Check for URLs
		boolean found = false;
		while (matcher.find()) {
			found = true;
		}
		return found;
	}


	private List<GooglePostMediaRequest> getMediaRequest(String imageIds, Integer businessId) {
		List<GooglePostMediaRequest> googlePostMediaRequestList = new ArrayList<>();

		BusinessLiteDTO businessLiteDTO = businessCoreService.getBusinessLite(businessId, false);

		String[] imageIdsConversion = imageIds.split(",");
		List<Integer> ids = new ArrayList<>();
		for (String imageId : imageIdsConversion) {
			ids.add(Integer.parseInt(imageId));
		}
		List<String> imageUrls = socialPostsAssetService.findImageUrlsByIds(ids, businessLiteDTO.getBusinessNumber().toString());

		for (String imageUrl : imageUrls) {
			GooglePostMediaRequest googlePostMediaRequest = new GooglePostMediaRequest();
			googlePostMediaRequest.setSourceUrl(imageUrl);
			googlePostMediaRequest.setMediaFormat("PHOTO");
			googlePostMediaRequestList.add(googlePostMediaRequest);
		}

		return Arrays.asList(googlePostMediaRequestList.get(0));

	}

	private GMBPostMetadataRequest getGMBPostMetaData(String data) {
		GMBPostMetadataRequest googleLocalPostRequest = null;
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			Map<String, ?> map = JSONUtils.fromJSON(data, Map.class);

			googleLocalPostRequest = objectMapper.readValue(map.get("gmbPostMetaData").toString(), GMBPostMetadataRequest.class);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return googleLocalPostRequest;
	}

	private String getSocialPostType(String data) {
		String type = null;
		try {
			ObjectMapper objectMapper = new ObjectMapper();
			Map<String, ?> map = JSONUtils.fromJSON(data, Map.class);

			type = objectMapper.readValue((String) map.get("postType"), String.class);
		} catch (IOException e) {
			e.printStackTrace();
		}
		return type;
	}

	private void setInstagramPostAsPublished(SocialPostPublishInfo publishInfo) {
		LOGGER.info("Setting published statys as 1 for instagram post :: {}", publishInfo);
		publishInfo.setIsPublished(1);
		socialPostPublishInfoRepository.saveAndFlush(publishInfo);
	}

	
	private void handleFailedPost(SocialPostPublishInfo publishInfo, Exception ex) {
		LOGGER.warn("Error posting to source {} due to {} ", publishInfo.getSourceId(), ex.getMessage());
		publishInfo.setIsPublished(2);
		publishInfo.setFailureReason(ex.getMessage());
		socialPostPublishInfoRepository.saveAndFlush(publishInfo);
		updateSocialPostAudit(publishInfo.getSocialPostId(), "Posted on twitter", "POSTING_FAILED",
				publishInfo.getSourceId(), publishInfo.getBusinessId(), publishInfo.getEnterpriseId());
	}
	
	@Override
	public List<BusinessFBPage> getValidFbPages(Business business, User user) throws Exception {
		List<BusinessFBPage> activeFbPages = new ArrayList<>();
		List<BusinessFBPage> businessFacebookPages =null;
		List<Integer> businessIds = getBusinessIdsForEnterprise(business, user);
		if (CollectionUtils.isEmpty(businessIds)) {
			LOGGER.error("[Social] No businessIds found.");
		}
		businessFacebookPages = socialFBPageRepository.findByBusinessIdIn(businessIds);
		if ((businessFacebookPages == null) || (businessFacebookPages.isEmpty())) {
			if (business.getParentId() != null) {
				businessFacebookPages = socialFBPageRepository.findByBusinessId(business.getId());
			}
		}

		if(CollectionUtils.isNotEmpty(businessFacebookPages)){
			for (BusinessFBPage fbPage : businessFacebookPages){
				if(fbPage.getIsValid() ==1){
					activeFbPages.add(fbPage);
				}
			}
		}

		if (CollectionUtils.isEmpty(activeFbPages)) {
			LOGGER.info("No pages exist in the DB, Exiting");
			throw new BirdeyeSocialException("No facebook page associated with business");
		}
		return activeFbPages;
	}

	@Override
	public List<BusinessGoogleMyBusinessLocation> getValidGMBPages(Business business, User user) throws Exception {
		List<BusinessGoogleMyBusinessLocation> activeGmbPages = new ArrayList<>();
		List<BusinessGoogleMyBusinessLocation> gmbPages = null;
		List<Integer> businessIds = getBusinessIdsForEnterprise(business, user);
		if (CollectionUtils.isEmpty(businessIds)) {
			LOGGER.error("[Social] No businessIds found.");
		}
		gmbPages = businessGMBLocationRawRepository.findByBusinessIdIn(businessIds);
		if (CollectionUtils.isEmpty(gmbPages)) {
			if (business.getParentId() != null) {
				gmbPages = businessGMBLocationRawRepository.findByBusinessIdIn(Arrays.asList(business.getId()));
			}
		}

		if(CollectionUtils.isNotEmpty(gmbPages)){
			for (BusinessGoogleMyBusinessLocation gmbPage : gmbPages){
				if(gmbPage.getIsValid() ==1){
					activeGmbPages.add(gmbPage);
				}
			}
		}

		if (CollectionUtils.isEmpty(activeGmbPages)) {
			LOGGER.info("No pages exist in the DB, Exiting");
			throw new BirdeyeSocialException("No GMB page associated with business");
		}
		return activeGmbPages;
	}

	@Override
	public List<BusinessTwitterAccounts> getTwitterConfig(Business business, User user) throws Exception{
		List<BusinessTwitterAccounts> twitterConfigs = null;
		List<Integer> businessIds = getBusinessIdsForEnterprise(business, user);
		if (CollectionUtils.isEmpty(businessIds)) {
			LOGGER.error("[Social] No businessIds found.");
		}
		twitterConfigs = socialTwitterRepo.findByBusinessIdIn(businessIds);
		if (CollectionUtils.isEmpty(twitterConfigs)) {
			if (business.getParentId() != null) {
				twitterConfigs = socialTwitterRepo.findByBusinessId(business.getParentId().getId());
			}
		}

		if (CollectionUtils.isEmpty(twitterConfigs)) {
			LOGGER.info("No twitter config exist in the DB, Exiting");
			throw new BirdeyeSocialException("No twitter config associated with business");
		}
		return twitterConfigs;
	}

	private File getFile(SocialPostPublishInfo publishInfo,String type) {
		File file = null;
		if(publishInfo.getSocialPost().getVideoIds().isEmpty()){
			return null;
		}
		Business business;
		if (publishInfo.getEnterpriseId() != null) {
			business = businessRepo.findById(publishInfo.getEnterpriseId());
		} else {
			business = businessRepo.findById(publishInfo.getBusinessId());
		}
		SocialPostsAssets postAsset;
			if (Constants.VIDEO.equalsIgnoreCase(type)) {
				postAsset = socialPostsAssetService.findById(getVideoOrImageId(publishInfo.getSocialPost().getVideoIds()));
				file = commonService.getFile(business.getBusinessId(), socialPostsAssetService.getCompleteVideoUrlFromPostAsset(postAsset, business.getBusinessId().toString()));
			} else {
				postAsset = socialPostsAssetService.findById(getVideoOrImageId(publishInfo.getSocialPost().getImageIds()));
				file = commonService.getFile(business.getBusinessId(), socialPostsAssetService.getCompleteImageUrlFromPostAsset(postAsset, business.getBusinessId().toString()));
			}
		return file;
	}

	private Integer getVideoOrImageId(String assetIds) {
		if(assetIds.isEmpty()){
			return null;
		}
		String[] ids = assetIds.split(",");
		List<Integer> listOfIds = new ArrayList<>();
		for(String id : ids ){
			listOfIds.add(Integer.parseInt(id));
		}
		return listOfIds.get(0);
	}

	private Integer getVideoIds(String videoIds) {
		if(videoIds.isEmpty()){
			return null;
		}
		String[] ids = videoIds.split(",");
		List<Integer> listOfIds = new ArrayList<>();
		for(String id : ids ){
			listOfIds.add(Integer.parseInt(id));
		}
		return listOfIds.get(0);
	}

	@Override
	public List<BusinessLinkedinPage> getLinkedinPages(Business business, User user) throws Exception{
		List<BusinessLinkedinPage> activeLinkedInPages = new ArrayList<>();
		List<BusinessLinkedinPage> linkedinConfigs = null;
		List<Integer> businessIds = getBusinessIdsForEnterprise(business, user);
		if (CollectionUtils.isEmpty(businessIds)) {
			LOGGER.error("[Social] No businessIds found.");
		}
		linkedinConfigs = linkedinPageRepository.findByBusinessIdIn(businessIds);
		if (CollectionUtils.isEmpty(linkedinConfigs)) {
			if (business.getParentId() != null) {
				linkedinConfigs = linkedinPageRepository.findByBusinessId(business.getParentId().getId());
			}
		}
		if(CollectionUtils.isNotEmpty(linkedinConfigs)){
			for(BusinessLinkedinPage linkedinPage : linkedinConfigs){
				if(linkedinPage.getIsValid() == 1){
					activeLinkedInPages.add(linkedinPage);
				}
			}
		}


		if (CollectionUtils.isEmpty(activeLinkedInPages)) {
			LOGGER.info("No linkedin config exist in the DB, Exiting");
			throw new BirdeyeSocialException("No linkedin config associated with business");
		}
		return activeLinkedInPages;
	}
	
	private List<Integer> getBusinessIdsForEnterprise(Business business, User user) {
		List<Integer> businessIds = null;
		if (null != business && null != business.getId()) {
			if ("Business".equals(business.getType()) || "Product".equals(business.getType())) {
				if (business.getEnterpriseId() == null) {
					businessIds = new ArrayList<>();
					businessIds.add(business.getId());
				} else {
					businessIds = businessRepo.findEnterpriseLocations(business.getEnterpriseId());
					if ( user != null ) {
						businessIds = businessUserRepo.findBusiness(user.getId(), businessIds);
					}
				}
			} else if ("Enterprise-Location".equals(business.getType()) || "Enterprise-Product".equals(business.getType())) {
				businessIds = businessRepo.findEnterpriseLocations(business.getId());
				if ( user != null ) {
					businessIds = businessUserRepo.findBusiness(user.getId(), businessIds);
				}
			}
		}
		return businessIds;
	}

	@Override
	public void retryProcessingPosts(List<SocialPostPublishInfo> processingPosts) {
		List<SocialPostPublishInfo> instagramPosts;
		List<SocialPostPublishInfo> twitterPosts;

		instagramPosts = processingPosts.stream().filter(post->post.getSourceId()==SocialChannel.INSTAGRAM.getId()).collect(Collectors.toList());
		twitterPosts = processingPosts.stream().filter(post->post.getSourceId()==SocialChannel.TWITTER.getId()).collect(Collectors.toList());

		if(CollectionUtils.isNotEmpty(instagramPosts)) {
			try {
				socialPostInstagramService.retryPosts(instagramPosts);
			} catch (Exception e) {
				LOGGER.info("[IG retry]Something went wrong while retrying Instagram posts");
			}
		}

		if(CollectionUtils.isNotEmpty(twitterPosts)) {
			//retry for twitter
		}
	}

	@Override
	public SocialMentionsData searchMentionData(String channel, String search, Integer businessId, Long enterpriseId, String nextToken) {
		SocialMentionsData socialMentionsData = new SocialMentionsData();
		List<SocialMentionData> socialMentionDataList = new ArrayList<>();
		if(SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)){
			socialMentionsData = socialPostTwitterService.searchMentionData(search,businessId,enterpriseId, nextToken);

			List<String> compId = socialMentionsData.getSocialMentionDataList().stream().map(v -> v.getId()).collect(Collectors.toList());


			List<TwitterCompetitorMapping> twitterCompetitorMappings =  twitterCompetitorMappingRepo.findByCompetitorIdInAndEnterpriseId(compId, enterpriseId);

			for(SocialMentionData mentionData : socialMentionsData.getSocialMentionDataList()) {
				boolean pagePresent = twitterCompetitorMappings.stream().anyMatch(v -> v.getCompetitorId().equals(mentionData.getId()));
				mentionData.setPagePresent(pagePresent);
			}

		}else if(SocialChannel.FACEBOOK.getName().equalsIgnoreCase(channel)){
			socialMentionsData = socialPostFacebookService.searchPageMentions(search,enterpriseId, nextToken);

			List<String> compId = socialMentionsData.getSocialMentionDataList().stream().map(v -> v.getId()).collect(Collectors.toList());

			List<FacebookCompetitorMapping> facebookCompetitorMapping = fbCompetitorMappingRepo.findByCompetitorIdInAndEnterpriseId(compId, enterpriseId);

			for(SocialMentionData mentionData : socialMentionsData.getSocialMentionDataList()) {
				boolean pagePresent = facebookCompetitorMapping.stream().anyMatch(v -> v.getCompetitorId().equals(mentionData.getId()));
				mentionData.setPagePresent(pagePresent);
			}

		}else if(SocialChannel.LINKEDIN.getName().equalsIgnoreCase(channel)){
		    socialMentionDataList = socialPostLinkedinService.searchPageMentions(search,enterpriseId);
			if(CollectionUtils.isNotEmpty(socialMentionDataList)){
				socialMentionsData.setSocialMentionDataList(socialMentionDataList);
			}
		} else if(SocialChannel.INSTAGRAM.getName().equalsIgnoreCase(channel)) {
			socialMentionDataList = socialPostInstagramService.searchMentionData(search, businessId,enterpriseId);
			if(CollectionUtils.isNotEmpty(socialMentionDataList)){

				List<String> compId = socialMentionDataList.stream().map(v -> v.getId()).collect(Collectors.toList());
				List<InstagramCompetitorMapping> instagramCompetitorMappings = igCompetitorMappingRepo.findByCompetitorIdInAndEnterpriseId(compId, enterpriseId);

				for(SocialMentionData mentionData : socialMentionDataList) {
					boolean pagePresent = instagramCompetitorMappings.stream().anyMatch(v -> v.getCompetitorId().equals(mentionData.getId()));
					mentionData.setPagePresent(pagePresent);
				}

				socialMentionsData.setSocialMentionDataList(socialMentionDataList);
			}
		}
		return socialMentionsData;
	}

	@Override
	public SocialLocationsTagData searchLocationTagData(String channel, String search, Integer businessId){
		SocialLocationsTagData socialLocationsTagData = new SocialLocationsTagData();
		if(SocialChannel.TWITTER.getName().equalsIgnoreCase(channel)){
			socialLocationsTagData.setSocialLocationTagResponses(socialPostTwitterService.searchLocationTagData(search,businessId));
		}
		return socialLocationsTagData;
	}

	@Override
	public List<TargetAudienceResponse> getTargetAudienceList(String pageId, String category) {
		return socialPostLinkedinService.getTargetAudienceList(pageId,category);
	}

	@Override
	public List<YoutubeCategory> getYoutubeCategories(String pageId) throws Exception {
		return youtubeVideoUpload.getYoutubeCategories(pageId);
	}

	@Override
	public List<YoutubePlaylist> getPlaylistForChannel(String pageId) throws Exception {
		return youtubeVideoUpload.getPlaylistForChannel(pageId);
	}

	private void updateEsForAnalyze(Integer postId, List<String> pageIds) {
		LOGGER.info("Updating es for deleted post: {}",postId);
		esService.updateDocumentForDeletedPost(postId, pageIds);
	}
	@Override
	@Async
	public void deletePublishedPostForChannel(List<SocialPostPublishInfo> socialPostPublishInfoList, DeletePostRequest request, Integer businessId,
											  Integer userId, Integer postId) {
		String key = Constants.POST_DELETE_KEY+"_"+postId;
		BusinessLiteDTO business = businessCoreService.getBusinessLite(businessId, true);
		request.setStatus(DeletePostRequestStatus.PROCESSING.getName());
		pushCheckStatusInFirebase(DELETE_ACTIVITY, DeletePostRequestStatus.PROCESSING.getName(), postId, false, null);
		deletePostRequestRepo.saveAndFlush(request);
		if (Objects.isNull(business)) {
			LOGGER.error("business not found for business id: {}", businessId);
			request.setStatus(DeletePostRequestStatus.FAILED.getName());
			pushCheckStatusInFirebase(DELETE_ACTIVITY, DeletePostRequestStatus.FAILED.getName(), postId, true, null);
			deletePostRequestRepo.saveAndFlush(request);
			redisService.release(key);
			throw new BirdeyeSocialException("No business found");
		}
		ForkJoinPool forkJoinPool = new ForkJoinPool(4);
		try {
			Map<String, List<String>> deletedPostMap = new ConcurrentHashMap<>();
			forkJoinPool.invoke(ForkJoinTask.adapt(() -> socialPostPublishInfoList.parallelStream().forEach(publishedPost->{
				try {
					deletePostsPerChannel(publishedPost);
					String channelName = SocialChannel.getSocialChannelNameById(publishedPost.getSourceId());
					if(!deletedPostMap.containsKey(channelName)) {
						List<String> pageIds = new ArrayList<>();
						pageIds.add(publishedPost.getExternalPageId()); //putIfAbsent
						deletedPostMap.put(channelName, pageIds);
					} else {
						deletedPostMap.get(channelName).add(publishedPost.getExternalPageId());
					}
				} catch (Exception e) {
					LOGGER.error("delete failed for channel: {} and postPublishInfoId: {}", SocialChannel.getSocialChannelById(publishedPost.getSourceId()), publishedPost.getId());
				}
			})));
			List<String> deletedPageIds = deletedPostMap.getOrDefault(SocialChannel.getSocialChannelNameById(socialPostPublishInfoList.get(0).getSourceId()),new ArrayList<>());
			int postCount = deletedPageIds.size();
			if(postCount>0) {
				PostActivityDelete postActivityDelete = new PostActivityDelete(deletedPostMap);
				socialPostActivityService.sendEventToSaveActivity(postId, userId, PostActivityType.DELETE.getName(), postActivityDelete, businessId);
				updateEsForAnalyze(postId, deletedPageIds);
				pushCheckStatusInFirebase(DELETE_ACTIVITY, DeletePostRequestStatus.COMPLETED.getName(), postId, false, postCount);
			} else {
				LOGGER.info("No post was deleted for post id: {}",request.getPostId());
				pushCheckStatusInFirebase(DELETE_ACTIVITY, DeletePostRequestStatus.FAILED.getName(), postId, true, null);
			}
			request.setDeletePostCount(postCount);
			request.setStatus(DeletePostRequestStatus.COMPLETED.getName());

			deletePostRequestRepo.saveAndFlush(request);
		} catch (Exception e) {
			LOGGER.error("error in processing delete job for post id: {} due to error: {}", postId, e.getMessage());
			request.setStatus(DeletePostRequestStatus.FAILED.getName());
			pushCheckStatusInFirebase(DELETE_ACTIVITY, DeletePostRequestStatus.FAILED.getName(), postId, true, null);
			deletePostRequestRepo.saveAndFlush(request);
		} finally {
			forkJoinPool.shutdown();
		}
		redisService.release(key);
	}

	private void updateSocialPostScheduleEntry(Map<String, List<String>> deletedPostMap, Integer postId) {
		if(MapUtils.isEmpty(deletedPostMap)) {
			return;
		}

		List<Integer> sourceIds = deletedPostMap.keySet().stream().map(s->SocialChannel.getSocialChannelByName(s).getId()).collect(Collectors.toList());

		List<SocialPostScheduleInfo> socialPostScheduleInfoList = socialPostScheduleInfoRepository.findBySocialPostIdAndSourceIdIn(postId, sourceIds);
		List<Integer> deletePostIds = new ArrayList<>();
		List<SocialPostScheduleInfo> updateList = new ArrayList<>();

		for(SocialPostScheduleInfo scheduleInfo: socialPostScheduleInfoList) {
			List<String> existedPageList = scheduleInfo.getPageIds();
			String channel = SocialChannel.getSocialChannelNameById(scheduleInfo.getSourceId());
			if(deletedPostMap.containsKey(channel)) {
				List<String> deletePageList = deletedPostMap.get(channel);
				if(CollectionUtils.isNotEmpty(deletePageList)) {
					existedPageList.removeAll(deletePageList);
				}
			}

			if(CollectionUtils.isEmpty(existedPageList)) deletePostIds.add(scheduleInfo.getId());
			else {
				scheduleInfo.setPageIds(existedPageList);
				updateList.add(scheduleInfo);
			}
		}

		if(CollectionUtils.isNotEmpty(deletePostIds)) {
			socialPostScheduleInfoRepository.deleteByIdIn(deletePostIds);
		}

		if(CollectionUtils.isNotEmpty(updateList)) {
			socialPostScheduleInfoRepository.save(updateList);
		}
	}

	@Override
	public void pushCheckStatusInFirebase(String requestType, String status, Integer postId, Boolean error, Integer count) {
		CheckStatusRequest checkStatusRequest = new CheckStatusRequest(status,requestType, error, count);
		nexusService.insertDataInFirebaseWithKey(FireBaseConstants.getSocialPostDeleteProgressStatusTopic(postId),checkStatusRequest, postId.toString());
		LOGGER.info("pushed check status in firebase for post with payload :{} {}",postId,checkStatusRequest);
	}

	private void deletePostsPerChannel(SocialPostPublishInfo publishedPost) throws Exception {
		Integer sourceId = publishedPost.getSourceId();
		if(!SocialPostStatusEnum.PUBLISHED.getId().equals(publishedPost.getIsPublished())
				&& !(SocialPostStatusEnum.EDITED.getId().equals(publishedPost.getIsPublished()) && SocialPostStatusEnum.FAILED.getId().equals(publishedPost.getEditedIsPublished()))) {
			LOGGER.info("published post for id: {} is not a published post", publishedPost.getId());
			return;
		}
		if(StringUtils.isEmpty(publishedPost.getPostId())) {
			LOGGER.info("post id for  id: {} is not available", publishedPost.getId());
			return;
		}

		PostOperation execute = postOperationFactory.getPostOperationChannel(SocialChannel.getSocialChannelNameById(sourceId))
				.orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
		execute.deletePost(publishedPost);
		publishedPost.setIsPublished(SocialPostStatusEnum.DELETED.getId());
		socialPostPublishInfoRepository.saveAndFlush(publishedPost);
	}

	@Override
	public void checkIgContainerStatus(IgContainerCheckDTO igContainerCheckDTO, boolean markFailed) throws BirdeyeSocialException {
		socialPostInstagramService.checkIgContainerStatus(igContainerCheckDTO, markFailed);
	}

	@Override
	public void updatePostOnSocialSites(SocialPostPublishInfo publishInfo) throws Exception {
		if(!checkPageValidity(publishInfo)) {
			String channelName = SocialChannel.getSocialChannelNameById(publishInfo.getPageId());
			LOGGER.info("page is marked as invalid {}, therefore not posting", publishInfo.getBusinessId());
			List<PermissionMapping> pmList = permissionMappingService.getDataByChannelAndParentErrorCodeAndPermissionCodeNull("ALL", ErrorCodes.INVALID_PAGE.value(), null);
			String errorMessage = null;
			if(CollectionUtils.isNotEmpty(pmList)) {
				errorMessage = pmList.get(0).getErrorMessage();
			} else {
				PermissionMapping pm = permissionMappingService.getDataByChannelAndPermissionCode(channelName, Constants.ERROR_CONSTANT_FOR_UNKNOWN_ERROR);
				if(Objects.nonNull(pm)) {
					errorMessage =  pm.getErrorMessage();
				}
			}
			publishInfo.setIsPublished(2);
			publishInfo.setFailureReason(errorMessage);
			socialPostPublishInfoRepository.saveAndFlush(publishInfo);
			postOperationUtil.markOriginalPost(publishInfo);
			publishFailedSocialPostEvent(publishInfo);
			return ;
		}

		SocialPost socialPost = publishInfo.getSocialPost();
		// Replace tokens if applicable
		List<MentionData> mentionDataList = new ArrayList<>();
		String postText=Objects.nonNull(publishInfo.getSocialPost().getPostText())?publishInfo.getSocialPost().getPostText():"";
		if(org.apache.commons.lang3.StringUtils.isNotEmpty(publishInfo.getSocialPost().getMentions())) {
			mentionDataList = JSONUtils.collectionFromJSON(publishInfo.getSocialPost().getMentions(), MentionData.class);
		}
		boolean isReviewAutoShare = Objects.nonNull(socialPost.getReviewId()) && BooleanUtils.isTrue(socialPost.getAutoShare());
		//don't replace tokens if it's a case of review auto share
		if (!isReviewAutoShare) {
			postText = getProfileData(publishInfo.getSocialPost().getPostText(),publishInfo.getBusinessId(),mentionDataList,publishInfo.getSourceId());
		}
		socialPost.setPostText(postText);
		LOGGER.info("Posting for channel: {} with text: {} ", publishInfo.getSourceId(), socialPost.getPostText());
		publishInfo.setSocialPost(socialPost);

		if(Objects.isNull(publishInfo.getSocialPost().getPostText())) {
			LOGGER.error("Post text not set for post id: {}", publishInfo.getPostId());
			publishInfo.setIsPublished(2);
			PermissionMapping pm = errorHandlerService(Constants.ALL_CHANNEL,ErrorCodes.POST_TEXT_NOT_SET.value(), -1, ExternalAPIErrorCode.POST_TEXT_NOT_SET.getDescription());
			publishInfo.setFailureReason(pm.getErrorMessage());
			publishInfo.setBucket(pm.getBucket());
			publishInfo.setFailureCode(0);
			socialPostPublishInfoRepository.saveAndFlush(publishInfo);
			postOperationUtil.markOriginalPost(publishInfo);
			kafkaExternalService.publishSocialPostEvent(publishInfo);
		} else {
			PostOperation execute = postOperationFactory.getPostOperationChannel(SocialChannel.getSocialChannelNameById(publishInfo.getSourceId()))
					.orElseThrow(() -> new IllegalArgumentException(INVALID_REQUEST));
			execute.editPublishedPost(publishInfo);
		}

	}
}
