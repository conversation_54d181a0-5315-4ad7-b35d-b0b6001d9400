package com.birdeye.social.service;

import com.birdeye.social.constant.SocialTagEntityType;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.sro.SocialBulkTagEntityEvent;
import com.birdeye.social.sro.SocialTagEntityMappingActionEvent;
import com.birdeye.social.sro.SocialTagEntityMappingRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> on 21/12/23
 */
@Service
public class SocialTagEventService {

    @Autowired
    private KafkaProducerService kafkaProducerService;

    private static final String socialPostTagMappingEventTopic = "social-post-tag-map-event";

    @Value("${social.engage.tag.mapping.event.topic}")
    private String socialEngageTagMappingEventTopic;

    private static final String socialAssetTagMappingEventTopic= "social-asset-tag-map-event";

    private static final String socialDraftTagMappingEventTopic = "social-draft-tag-map-event";

    private static final String socialPostLibTagMappingEventTopic = "social-post-lib-tag-map-event";
    private static final String socialAiPostLibTagMappingEventTopic = "social-ai-post-tag-map-event";

    @Value("${social.entity.bulk.tag.mapping.event.topic}")
    private String socialEntityBulkTagMappingEventTopic;

    @Value("${asset.lib.social.tag.migration.event.topic}")
    private String assetLibraryToSocialTagMigrationEventTopic;


    public void publishTagEntityMappingActionEvent(Long entityId, SocialTagEntityMappingActionEvent tagEntityMappingActionEvent, SocialTagEntityType entityType) {
        String eventTopic = getTagEntityMappingEventTopic(entityType);
        kafkaProducerService.sendObjectWithKeyV1(String.valueOf(entityId), eventTopic, tagEntityMappingActionEvent);
    }

    public void publishTagBulkEntityMappingActionEvent(Long entityId, SocialTagEntityType entityType, SocialTagEntityMappingRequest socialTagEntityMappingRequest,
                                                       Integer accountId, Long userId, Long businessNumber) {
        SocialBulkTagEntityEvent socialBulkTagEntityEvent = new SocialBulkTagEntityEvent(entityId, entityType, socialTagEntityMappingRequest, accountId, userId, businessNumber);
        kafkaProducerService.sendObjectWithKeyV1(String.valueOf(entityId), socialEntityBulkTagMappingEventTopic, socialBulkTagEntityEvent);
    }

    public void publishAssetLibTagMappingMigrationEvent(Integer accountId){
        Map<String, Object> event = new HashMap<>();
        event.put("accountId", accountId);
        kafkaProducerService.sendObjectWithKeyV1(String.valueOf(accountId), assetLibraryToSocialTagMigrationEventTopic, event);
    }

    private String getTagEntityMappingEventTopic(SocialTagEntityType entityType) {
        switch (entityType) {
            case POST:
                return socialPostTagMappingEventTopic;
            case ENGAGE:
                return socialEngageTagMappingEventTopic;
            case ASSET:
                return socialAssetTagMappingEventTopic;
            case DRAFT:
                return socialDraftTagMappingEventTopic;
            case POST_LIB:
                return socialPostLibTagMappingEventTopic;
            case AI_POST:
                return socialAiPostLibTagMappingEventTopic;
        }
        throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Invalid Tag Mapping Entity Type");
    }
}
