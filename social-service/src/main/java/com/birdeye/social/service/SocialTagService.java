package com.birdeye.social.service;

import com.birdeye.social.constant.SocialTagEntityType;
import com.birdeye.social.sro.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> on 21/12/23
 */
public interface SocialTagService {

    SocialTagFetchAllResponse getAllTags(SocialTagFetchAllRequest socialTagFetchAllRequest, Integer accountId, String timezoneId,
                                         Integer startIndex, Integer pageSize, String sortBy, Integer sortOrder, Boolean liteVersion);

    List<SocialTagOperationResponse> performTagOperations(List<SocialTagOperationRequest> tagOperationRequests, Integer accountId, Long accountNum, Long userId);

    void performTagEntityMappingOperations(SocialTagEntityMappingRequest socialTagEntityMappingRequest, SocialTagEntityType entityType, Long entityId,
                                           Integer accountId, Long userId, Long businessNumber, Boolean isFreshRequest);


    Map<Long, List<SocialTagBasicDetail>> getEntityIdToBasicTagDetailListMap(Collection<Long> entityIds, SocialTagEntityType entityType);

    List<SocialTagBasicDetail> getBasicTagDetailForSingleEntityId(Long entityId, SocialTagEntityType entityType);

    void performTagEntityMappingBulkOperations(SocialTagEntityBulkMappingRequest socialTagEntityBulkMappingRequest, SocialTagEntityType entityType,
                                               Integer accountId, Long userId, Long businessNumber);

    void deleteAllTagEntityMapping(Long entityId, SocialTagEntityType entityType, Integer accountId);

    Boolean isUntaggedRequest(Collection<Long> tagIds);

    List<Long> findEntityIdByEntityTypeAndTagIdIn(Collection<Long> tagIds, SocialTagEntityType entityType);

    void evictAllTagsCacheByAccountId(Integer accountId);

    void migrateAssetLibraryTagAsSocialTag();

    void migrateAssetLibraryTagAsSocialTag(Integer accountId);

    Map<Long, SocialTagBasicDetail> getTagIdsBasicDetailsFilteredOnEnterpriseId(Set<Long> tagIds, Integer enterpriseId);

    Set<Long> getTagIdsFromEntityId(Long entityId, SocialTagEntityType socialTagEntityType);

}