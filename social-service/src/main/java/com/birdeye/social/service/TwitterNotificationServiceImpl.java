package com.birdeye.social.service;

import com.birdeye.social.constant.*;
import com.birdeye.social.dao.EngageFeedDetailsRepo;
import com.birdeye.social.dao.EngageUserDetailsRepo;
import com.birdeye.social.dao.SocialTwitterAccountRepository;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import com.birdeye.social.entities.EngageFeedDetails;
import com.birdeye.social.entities.EngageUserDetails;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.insights.constants.ElasticConstants;
import com.birdeye.social.model.FbNotification.EngageNotificationDetails;
import com.birdeye.social.model.TwitterDeleteEvent;
import com.birdeye.social.model.TwitterFollowEvent;
import com.birdeye.social.model.TwitterFollowNotification;
import com.birdeye.social.model.TwitterNotification;
import com.birdeye.social.model.engageV2.message.EventUpdateRequest;
import com.birdeye.social.nexus.NexusService;
import com.birdeye.social.platform.dao.BusinessTwitterPageRepository;
import com.birdeye.social.service.SocialEngageService.converter.EngageConverterServiceImpl;
import com.birdeye.social.twitter.*;
import com.birdeye.social.utils.DateTimeUtils;
import com.birdeye.social.utils.JSONUtils;
import com.birdeye.social.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.action.get.GetResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

@Service
public class TwitterNotificationServiceImpl implements ITwitterNotificationService{
    private static final Logger LOG = LoggerFactory.getLogger(TwitterNotificationServiceImpl.class);
    public static final String TWITTER_BASE_URL = "https://twitter.com/";
    public static final String TWITTER_STATUS_CONST = "/status/";
    public static final String PHOTO="photo";
    public static final String VIDEO="video";
    public static final String GIF = "animated_gif";



    @Autowired
    private SocialTwitterAccountRepository socialTwitterRepo;
    @Autowired
    private EngageFeedDetailsRepo engageFeedDetailsRepo;

    @Autowired
    private NexusService nexusService;
    @Autowired
    private KafkaProducerService kafkaProducerService;

    @Autowired
    private BusinessTwitterPageRepository businessTwitterPageRepository;
    @Autowired
    private EsService esService;

    @Autowired
    private EngageUserDetailsRepo engageUserDetailsRepo;

    @Autowired
    private EngageConverterServiceImpl engageConverterService;




    @Override
    public void processTwitterNotification(TwitterNotification twitterNotification) {
        LOG.info("processTwitterNotification :{}", JSONUtils.toJSON(twitterNotification));
        ArrayList<MediaTweet> createEvents = twitterNotification.getTweet_create_events();
        String topicName = "";
        for(MediaTweet event : createEvents){
            if(Objects.nonNull(event.getQuoted_status())){
                /** Quoted tweet case**/
                LOG.info("Quote Tweet : {}", event.getIdStr());
                topicName = KafkaTopicEnum.TWITTER_QUOTE_TWEET_EVENT.getName();
            }else if(Objects.nonNull(event.getRetweetedStatus())){
                /** ReTweet case**/
                LOG.info("Retweet Tweet : {}", event.getIdStr());
                topicName = KafkaTopicEnum.TWITTER_RE_TWEET_EVENT.getName();
            }else if(Objects.nonNull(event.getIn_reply_to_status_id_str())){
                /** Comment case**/
                LOG.info("Comment Tweet : {}", event.getIdStr());
                topicName = KafkaTopicEnum.TWITTER_COMMENT_EVENT.getName();
            }else if(Objects.nonNull(event.getEntities()) && Objects.nonNull(event.getEntities().getUserMentions())){
                /** Mention case**/
                LOG.info("Mention Tweet : {}", event.getIdStr());
                topicName = KafkaTopicEnum.TWITTER_MENTION_EVENT.getName();
            }
            if(Objects.nonNull(topicName)) {
                kafkaProducerService.sendObjectV1(topicName, twitterNotification);
            }
        }
    }

    @Override
    public void receiveNewFollowerNotification(TwitterFollowNotification twitterNotification) {
        Long forUserId = twitterNotification.getFor_user_id();
        LOG.info("Received webhook for twitter new Follower : {}", forUserId);
        BusinessTwitterAccounts bizTwitterAccount = getBusinessTwitterPage(forUserId);
        if (bizTwitterAccount != null) {
            List<TwitterFollowEvent> followEvents = twitterNotification.getFollow_events();
            for(TwitterFollowEvent followEvent : followEvents){
                String type = followEvent.getType();
                String pageId = String.valueOf(bizTwitterAccount.getProfileId());
                String postId = forUserId + "-" + followEvent.getTarget().getId() + "-" + followEvent.getCreated_timestamp();
                Integer sourceId = SocialChannel.TWITTER.getId();
                Boolean ownActivity=checkOwnActivity(forUserId, followEvent.getSource());
                if(!postAlreadyExists(postId, type,pageId) && ownActivity){
                    EngageNotificationDetails notificationDetails = convertFollowerNotificationToESObject(bizTwitterAccount, followEvent, postId);
                    Integer acknowledgementId = validateContentDetails(notificationDetails);
                    if(Objects.isNull(acknowledgementId)) {
                        LOG.info("Twitter follow event is already processed, must be duplicate. exiting process : {}",forUserId);
                        return;
                    }
                    notificationDetails.setRawFeedId(acknowledgementId);
                    notificationDetails.setFeedId(String.valueOf(acknowledgementId));
                    notificationDetails.setPostId(String.valueOf(acknowledgementId));
                    notificationDetails.setEngageFeedId(String.valueOf(acknowledgementId));
                    kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);

                } else if (!ownActivity) {
                    List<EngageUserDetails> userDetailsList = engageUserDetailsRepo.findByAuthorIdAndPageIdAndSourceId(followEvent.getTarget().getId(),
                            String.valueOf(forUserId),sourceId);
                    if(CollectionUtils.isEmpty(userDetailsList)) {
                        LOG.info("No entry found for engage user for request {}", userDetailsList);
                        EngageUserDetails user = new EngageUserDetails();
                        user.setAuthorId(followEvent.getTarget().getId());
                        user.setAuthorName(followEvent.getTarget().getScreen_name());
                        user.setPageId(pageId);
                        user.setIsBlocked(0);
                        user.setIsFollowed(0);
                        user.setSourceId(sourceId);
                        engageUserDetailsRepo.saveAndFlush(user);
                        return;
                    }
                    EngageUserDetails userDetails = userDetailsList.get(0);
                    if(type.equalsIgnoreCase(EngageV2FeedTypeEnum.FOLLOW.name())) {
                        userDetails.setIsFollowed(1);
                    } else {
                        userDetails.setIsFollowed(0);
                    }
                    engageUserDetailsRepo.saveAndFlush(userDetails);
                } else{
                    LOG.info("Twitter Notification : Data already exist for :{}", forUserId);
                }
            }

        }
    }


    private boolean checkOwnActivity(Long forUserId, com.birdeye.social.model.TwitterUser target) {
        if(Objects.nonNull(target) && target.getId().equalsIgnoreCase(String.valueOf(forUserId))){
            LOG.info("Follow Activity found for business : {}, hence skipping", forUserId);
            return false;
        }
        return true;
    }

    @Override
    public void receiveMentionNotification(TwitterNotification twitterNotification) {
        Long forUserId = twitterNotification.getFor_user_id();
        LOG.info("Received webhook for twitter user mention : {}", forUserId);
        BusinessTwitterAccounts bizTwitterAccount = getBusinessTwitterPage(twitterNotification.getFor_user_id());
        if (bizTwitterAccount != null) {
            ArrayList<MediaTweet> events = twitterNotification.getTweet_create_events();
            for(MediaTweet mentionEvent : events){
                String type = EngageV2FeedTypeEnum.POST.name();
                String postId = mentionEvent.getIdStr();
                String pageId = String.valueOf(bizTwitterAccount.getProfileId());
                if(!postAlreadyExists(postId, type,pageId)){
                    EngageNotificationDetails notificationDetails = convertMentionNotificationToESObject(bizTwitterAccount, mentionEvent, postId, forUserId);
                    Integer acknowledgementId = validateContentDetails(notificationDetails);
                    if(Objects.isNull(acknowledgementId)) {
                        LOG.info("Twitter mention event is already processed, must be duplicate. exiting process : {}", forUserId);
                        return;
                    }
                    notificationDetails.setRawFeedId(acknowledgementId);
                    kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);
                }else{
                    LOG.info("Twitter Notification : Data already exist for :{}", forUserId);
                }
            }

        }
    }

    @Override
    public void receiveReTweetNotification(TwitterNotification twitterNotification) {
        Long forUserId = twitterNotification.getFor_user_id();
        LOG.info("Received webhook for twitter Retweet : {}", forUserId);
        BusinessTwitterAccounts bizTwitterAccount = getBusinessTwitterPage(twitterNotification.getFor_user_id());
        if (bizTwitterAccount != null) {
            ArrayList<MediaTweet> events = twitterNotification.getTweet_create_events();
            for(MediaTweet mentionEvent : events){
                String type = EngageV2FeedTypeEnum.RETWEET.name();
                String postId = mentionEvent.getIdStr();
                String pageId = String.valueOf(bizTwitterAccount.getProfileId());
                if(!postAlreadyExists(postId, type,pageId)){
                    EngageNotificationDetails notificationDetails = convertReTweetNotificationToESObject(bizTwitterAccount, mentionEvent, postId,forUserId);
                    Integer acknowledgementId = validateContentDetails(notificationDetails);
                    if(Objects.isNull(acknowledgementId)) {
                        LOG.info("Twitter reTweet event is already processed, must be duplicate. exiting process : {}", forUserId);
                        return;
                    }
                    notificationDetails.setRawFeedId(acknowledgementId);
                    kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);
                }else{
                    LOG.info("Twitter Notification Retweet: Data already exist for :{}", forUserId);
                }
            }
        }
    }

    @Override
    public void receiveQuoteTweetNotification(TwitterNotification twitterNotification) {
        Long forUserId = twitterNotification.getFor_user_id();
        LOG.info("Received webhook for twitter QuoteTweet : {}", forUserId);
        BusinessTwitterAccounts bizTwitterAccount = getBusinessTwitterPage(twitterNotification.getFor_user_id());
        if (bizTwitterAccount != null) {
            ArrayList<MediaTweet> events = twitterNotification.getTweet_create_events();
            for(MediaTweet mentionEvent : events){
                String type = EngageV2FeedTypeEnum.QUOTE_TWEET.name();
                String postId = mentionEvent.getIdStr();
                String pageId= String.valueOf(bizTwitterAccount.getProfileId());
                if(!postAlreadyExists(postId, type,pageId)){
                    EngageNotificationDetails notificationDetails = convertQuoteNotificationToESObject(bizTwitterAccount, mentionEvent, postId,forUserId);
                    Integer acknowledgementId = validateContentDetails(notificationDetails);
                    if(Objects.isNull(acknowledgementId)) {
                        LOG.info("Twitter Quote tweet event is already processed, must be duplicate. exiting process : {}", forUserId);
                        return;
                    }
                    notificationDetails.setRawFeedId(acknowledgementId);
                    kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);
                }else{
                    LOG.info("Twitter Notification QuoteTweet: Data already exist for :{}", forUserId);
                }
            }
        }
    }

    @Override
    public void receiveDeleteNotification(TwitterDeleteEvent twitterDeleteEvent) {
        if (Objects.isNull(twitterDeleteEvent) || CollectionUtils.isEmpty(twitterDeleteEvent.getTweet_delete_events())) {
            LOG.info("tweet_delete_events list is empty");
            return;
        }
        twitterDeleteEvent.getTweet_delete_events().forEach(deleteEvent -> {
            TwitterStatus status = deleteEvent.getStatus();
            EventUpdateRequest eventUpdateRequest = EventUpdateRequest.builder()
                    .eventId(status.getId())
                    .eventType(EngageActionsEnum.DELETE_CONTENT)
                    .sourceId(SocialChannel.TWITTER.getId())
                    .build();
            kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_SAVE_REACTION.getName(), eventUpdateRequest);
        });
    }

    @Override
    public void receiveCommentNotification(TwitterNotification twitterNotification) {
        Long forUserId = twitterNotification.getFor_user_id();
        LOG.info("Received webhook for twitter user comment : {}", forUserId);
        BusinessTwitterAccounts bizTwitterAccount = getBusinessTwitterPage(twitterNotification.getFor_user_id());
        if (bizTwitterAccount != null) {
            ArrayList<MediaTweet> events = twitterNotification.getTweet_create_events();
            for(MediaTweet mentionEvent : events){
                String type = EngageV2FeedTypeEnum.POST.name();
                String postId = mentionEvent.getIdStr();
                String pageId= String.valueOf(bizTwitterAccount.getProfileId());
                if(!postAlreadyExists(postId, type,pageId)){
                    EngageNotificationDetails notificationDetails = convertCommentNotificationToESObject(bizTwitterAccount, mentionEvent, postId, forUserId);
                    if(notificationDetails.getPostId() != null){
                        Integer acknowledgementId = validateContentDetails(notificationDetails);
                        if(Objects.isNull(acknowledgementId)) {
                            LOG.info("Twitter comment event is already processed, must be duplicate. exiting process : {}", forUserId);
                            return;
                        }
                        notificationDetails.setRawFeedId(acknowledgementId);
                        notificationDetails.setAddedFromDashboard(twitterNotification.isAddedFromDashboard());
                        kafkaProducerService.sendObjectV1(FBNotificationKafkaEventNameEnum.SOCIAL_ENGAGE_FEED_ES_TOPIC.getName(), notificationDetails);
                    }else{
                        LOG.info("receiveCommentNotification Post id null for {}",twitterNotification);
                    }

                }else{
                    LOG.info("Twitter Notification : Data already exist for :{}", forUserId);
                }
            }

        }
    }

    @Override
    public void receiveFavoriteReactionNotification(TwitterNotification twitterNotification) {
        if(Objects.isNull(twitterNotification) || CollectionUtils.isEmpty(twitterNotification.getFavorite_events())){
            LOG.info("Event is empty :{}",twitterNotification);
            return;
        }
        List<FavoriteEvents> favoriteEvents = twitterNotification.getFavorite_events();
        favoriteEvents.forEach(event -> {
            if(Objects.isNull(event.getFavorited_status())){
                LOG.info("Favorite Status is null : {}",twitterNotification);
                return;
            }
            MediaTweet status = event.getFavorited_status();
            if(Objects.isNull(status.getUser()) || Objects.isNull(event.getUser())) {
                LOG.info("User is null for request :{}",twitterNotification);
                return;
            }
            EventUpdateRequest eventUpdateRequest = EventUpdateRequest.builder()
                    .eventId(status.getIdStr())
                    .eventType(EngageActionsEnum.LIKE)
                    .from(event.getUser().getIdStr())
                    .sourceId(SocialChannel.TWITTER.getId())
                    .build();
            kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_SAVE_REACTION.getName(), eventUpdateRequest);

        });
    }

    @Override
    public void processBlockNotification(TwitterNotification twitterNotification) {
        if(Objects.isNull(twitterNotification)  ||  CollectionUtils.isEmpty(twitterNotification.getBlock_events())){
            LOG.info("Block Status is null : {}",twitterNotification);
            return;
        }
        List<TwitterEvent> blockEvents = twitterNotification.getBlock_events();
        blockEvents.forEach(event -> {
            if(Objects.isNull(event.getTarget()) || Objects.isNull(event.getSource())){
                LOG.info("Target or source is empty : {}",event);
                return;
            }
            EventUpdateRequest eventUpdateRequest = EventUpdateRequest.builder()
                    .eventId(event.getSource().getId())
                    .targetEventId(event.getTarget().getId())
                    .sourceId(SocialChannel.TWITTER.getId())
                    .build();
            if(Objects.equals(event.getType(),"block")){
                eventUpdateRequest.setEventType(EngageActionsEnum.BLOCK_USER);
            }else {
                eventUpdateRequest.setEventType(EngageActionsEnum.UNBLOCK_USER);
            }
            kafkaProducerService.sendObjectV1(KafkaTopicEnum.SOCIAL_SAVE_REACTION.getName(), eventUpdateRequest);
        });
    }

    private EngageNotificationDetails fetchEsDocByFeedIdAndPageId(String feedId, String pageId) throws IOException {
        return engageConverterService.fetchEsDocByFeedIdAndPageId(feedId, pageId);
    }
    private EngageNotificationDetails convertFollowerNotificationToESObject(BusinessTwitterAccounts bizTwitterAccount, TwitterFollowEvent followEvent, String postId) {
        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
        notificationDetails.setPageId(bizTwitterAccount.getProfileId()+"");
        notificationDetails.setPageName(bizTwitterAccount.getHandle().substring(1));
        notificationDetails.setPostId(postId);
        notificationDetails.setFeedId(postId);
        notificationDetails.setEngageFeedId(postId);
        notificationDetails.setEventParentId(postId);
        notificationDetails.setAuthorId(followEvent.getSource().getId());
        notificationDetails.setAuthorName(followEvent.getSource().getName());
        notificationDetails.setAuthorUsername(followEvent.getSource().getScreen_name());
        notificationDetails.setAuthorProfileImage(followEvent.getSource().getProfileImageUrlHttps());
        notificationDetails.setReviewerUrl(TWITTER_BASE_URL + followEvent.getSource().getScreenName());
        notificationDetails.setEventAction(followEvent.getType());
        notificationDetails.setIsLikedByAdmin(false);
        notificationDetails.setIsCompleted(false);
        notificationDetails.setIsAdminComment(false);
        notificationDetails.setIsParentComment(false);
        notificationDetails.setSourceId(SocialChannel.TWITTER.getId());
        notificationDetails.setChannel(SocialChannel.TWITTER.getName());
        notificationDetails.setType(EngageV2FeedTypeEnum.FOLLOW.name());
        notificationDetails.setCanReplyPrivately(true);
        notificationDetails.setHideOnThread(false);
        //Set followers and follow count of the source
        notificationDetails.setFollowers(Objects.nonNull(followEvent.getSource()) ? followEvent.getSource().getFollowers_count() : 0);
        notificationDetails.setFollows(Objects.nonNull(followEvent.getSource()) ? followEvent.getSource().getFriends_count() : 0);

        Date date = new Date(followEvent.getCreated_timestamp());
        notificationDetails.setFeedDate(DateTimeUtils.localToUTCSqlFormat(date));
        notificationDetails.setIsEdited(false);
        return notificationDetails;
    }
    private EngageNotificationDetails convertMentionNotificationToESObject(BusinessTwitterAccounts bizTwitterAccount, MediaTweet event, String postId, Long forUserId) {
        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
        TwitterUser user = event.getUser();
        notificationDetails.setPageId(bizTwitterAccount.getProfileId()+"");
        notificationDetails.setLocationId(bizTwitterAccount.getBusinessId());
        notificationDetails.setAccountId(bizTwitterAccount.getAccountId());
        notificationDetails.setPageName(bizTwitterAccount.getHandle().substring(1));
        notificationDetails.setPostId(postId);
        notificationDetails.setFeedId(postId);
        notificationDetails.setEngageFeedId(postId);
        notificationDetails.setEventParentId(postId);
        if(Objects.nonNull(user)){
            notificationDetails.setAuthorId(String.valueOf(user.getId()));
            notificationDetails.setAuthorName(user.getName());
            notificationDetails.setAuthorUsername(user.getScreen_name());
            notificationDetails.setAuthorProfileImage(user.getProfileImageUrlHttps());
            notificationDetails.setPostUrl(TWITTER_BASE_URL + event.getUser().getScreenName() + TWITTER_STATUS_CONST + event.getIdStr());
            notificationDetails.setFeedUrl(notificationDetails.getPostUrl());
            notificationDetails.setReviewerUrl(TWITTER_BASE_URL + event.getUser().getScreenName());
        }

        notificationDetails.setText(checkTextWithImage(event));

        notificationDetails.setEventAction("MENTION");
        notificationDetails.setIsLikedByAdmin(false);
        notificationDetails.setIsCompleted(false);
        notificationDetails.setIsAdminComment(false);
        notificationDetails.setIsParentComment(false);
        notificationDetails.setSourceId(SocialChannel.TWITTER.getId());
        notificationDetails.setChannel(SocialChannel.TWITTER.getName());
        notificationDetails.setType(EngageV2FeedTypeEnum.POST.name());
        notificationDetails.setSubType(EngageV2FeedSubTypeEnum.MENTION.name());
        notificationDetails.setCanReplyPrivately(true);
        if(user.getId().equalsIgnoreCase(String.valueOf(forUserId)))
        {
            LOG.info("userId is same for forUserId {} and userId {}",forUserId,user.getId());
        }
        notificationDetails.setHideOnThread(user.getId().equalsIgnoreCase(String.valueOf(forUserId)) ? true : false);
        notificationDetails.setFeedDate(DateTimeUtils.localToUTCSqlFormat(event.getCreatedAt()));
        notificationDetails.setIsEdited(false);
        if(Objects.nonNull(event.getExtendedEntitiesData()) && Objects.nonNull(event.getExtendedEntitiesData().getMedia())){
            List<String> imageUrls = new ArrayList<>();
            List<String> videoUrls = new ArrayList<>();
            for(ExtendedMediaEntity media : event.getExtendedEntitiesData().getMedia()){
                if(media.getType() != null && media.getType().equalsIgnoreCase(PHOTO)){
                    imageUrls.add(media.getMedia_url_https());
                }else if(media.getType() != null && (media.getType().equalsIgnoreCase(VIDEO) ||
                        media.getType().equalsIgnoreCase(GIF) )){
                    videoUrls.add(media.getVideoInfo().getVariants().get(0).getUrl());
                }
                notificationDetails.setPostUrl(media.getExpanded_url());
            }
            notificationDetails.setImageUrls(imageUrls);
            notificationDetails.setVideoUrls(videoUrls);
        }
        return notificationDetails;
    }
    private EngageNotificationDetails convertReTweetNotificationToESObject(BusinessTwitterAccounts bizTwitterAccount, MediaTweet event, String postId,Long forUserId) {
        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
        TwitterUser user = event.getUser();
        MediaTweet retweet = event.getRetweetedStatus();
        notificationDetails.setPageId(bizTwitterAccount.getProfileId()+"");
        notificationDetails.setLocationId(bizTwitterAccount.getBusinessId());
        notificationDetails.setAccountId(bizTwitterAccount.getAccountId());
        notificationDetails.setPageName(bizTwitterAccount.getHandle().substring(1));
        notificationDetails.setPostId(postId);
        notificationDetails.setFeedId(postId);
        notificationDetails.setEngageFeedId(postId);
        notificationDetails.setEventParentId(postId);
        if(Objects.nonNull(user)){
            notificationDetails.setAuthorId(user.getIdStr());
            notificationDetails.setAuthorName(user.getName());
            notificationDetails.setAuthorUsername(user.getScreen_name());
            notificationDetails.setAuthorProfileImage(user.getProfileImageUrlHttps());
            notificationDetails.setPostUrl(TWITTER_BASE_URL + event.getUser().getScreenName() + TWITTER_STATUS_CONST + event.getIdStr());
            notificationDetails.setFeedUrl(notificationDetails.getPostUrl());
            notificationDetails.setReviewerUrl(TWITTER_BASE_URL + event.getUser().getScreenName());
        }

      //  notificationDetails.setText(checkTextWithImage(event));
        notificationDetails.setText(getDisplayText(retweet.getText(),retweet.getDisplay_text_range()));
        notificationDetails.setParentPostText(notificationDetails.getText());
        notificationDetails.setEventAction("ReTweet");
        notificationDetails.setIsLikedByAdmin(false);
        notificationDetails.setIsCompleted(false);
        notificationDetails.setIsAdminComment(false);
        notificationDetails.setIsParentComment(false);
        notificationDetails.setSourceId(SocialChannel.TWITTER.getId());
        notificationDetails.setChannel(SocialChannel.TWITTER.getName());
        notificationDetails.setType(EngageV2FeedTypeEnum.RETWEET.name());
        notificationDetails.setCanReplyPrivately(true);
        //notificationDetails.setHideOnThread(false);
        notificationDetails.setHideOnThread(user.getId().equalsIgnoreCase(String.valueOf(forUserId)) ? true : false);
        notificationDetails.setFeedDate(DateTimeUtils.localToUTCSqlFormat(event.getCreatedAt()));
        notificationDetails.setIsEdited(false);

        if(retweet != null){
            EngageNotificationDetails retweetObject = new EngageNotificationDetails();
            retweetObject.setFeedDate(retweet.getCreatedAt());
            retweetObject.setId(retweet.getIdStr());
            retweetObject.setFeedId(retweet.getIdStr());


            if(Objects.nonNull(retweet.getUser())){
                retweetObject.setAuthorId(retweet.getUser().getIdStr());
                retweetObject.setAuthorName(retweet.getUser().getName());
                retweetObject.setAuthorUsername(retweet.getUser().getScreen_name());
                retweetObject.setAuthorProfileImage(retweet.getUser().getProfileImageUrlHttps());
                retweetObject.setPostUrl(TWITTER_BASE_URL + retweet.getUser().getScreenName() + TWITTER_STATUS_CONST + retweet.getIdStr());
                retweetObject.setFeedUrl(retweetObject.getPostUrl());
               // retweetObject.setText(retweet.getText());
                retweetObject.setReviewerUrl(TWITTER_BASE_URL + retweet.getUser().getScreenName());
            }

            retweetObject.setText(getDisplayText(retweet.getText(),retweet.getDisplay_text_range()));

            if(Objects.nonNull(retweet.getExtendedEntitiesData()) && Objects.nonNull(retweet.getExtendedEntitiesData().getMedia())){
                List<String> imageUrls = new ArrayList<>();
                List<String> videoUrls = new ArrayList<>();
                for(ExtendedMediaEntity media : retweet.getExtendedEntitiesData().getMedia()){
                    if(media.getType() != null && media.getType().equalsIgnoreCase(PHOTO)){
                        imageUrls.add(media.getMedia_url_https());
                    }else if(media.getType() != null && (media.getType().equalsIgnoreCase(VIDEO) ||
                     media.getType().equalsIgnoreCase(GIF))){
                        videoUrls.add(media.getVideoInfo().getVariants().get(0).getUrl());
                    }
                    notificationDetails.setPostUrl(media.getExpanded_url());
                    retweetObject.setPostUrl(media.getExpanded_url());
                }
                notificationDetails.setImageUrls(imageUrls);
                notificationDetails.setVideoUrls(videoUrls);
                retweetObject.setImageUrls(imageUrls);
                retweetObject.setVideoUrls(videoUrls);
            }
            else if(Objects.nonNull(retweet.getExtendedData()) && Objects.nonNull(retweet.getExtendedData().getMedia())){
                List<String> imageUrls = new ArrayList<>();
                List<String> videoUrls = new ArrayList<>();
                for(ExtendedMediaEntity media : retweet.getExtendedData().getMedia()){
                    if(media.getType() != null && media.getType().equalsIgnoreCase(PHOTO)){
                        imageUrls.add(media.getMedia_url_https());
                    }else if(media.getType() != null && (media.getType().equalsIgnoreCase(VIDEO) ||
                            media.getType().equalsIgnoreCase(GIF))){
                        videoUrls.add(media.getVideoInfo().getVariants().get(0).getUrl());
                    }
                    notificationDetails.setPostUrl(media.getExpanded_url());
                    retweetObject.setPostUrl(media.getExpanded_url());
                }
                notificationDetails.setImageUrls(imageUrls);
                notificationDetails.setVideoUrls(videoUrls);
                retweetObject.setImageUrls(imageUrls);
                retweetObject.setVideoUrls(videoUrls);
            }

           // notificationDetails.setParentPostText(retweet.getText());
            notificationDetails.setSubEvent(retweetObject);

        }
        return notificationDetails;
    }

    private String getDisplayText(String text, List<Integer> displayTextRange){
        if(Objects.nonNull(displayTextRange)){
            String displayText=text;
            try {
                displayText = text.substring(displayTextRange.get(0),
                        displayTextRange.get(1));
            }catch (Exception e){
                LOG.info("Error occurred while getting display text data :{}",displayText);
            }
          return displayText;
        }
        return text;
    }

    private EngageNotificationDetails convertQuoteNotificationToESObject(BusinessTwitterAccounts bizTwitterAccount, MediaTweet event, String postId,Long forUserId) {
        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
        TwitterUser user = event.getUser();
        MediaTweet quotedStatus = event.getQuoted_status();
        notificationDetails.setPageId(bizTwitterAccount.getProfileId()+"");
        notificationDetails.setLocationId(bizTwitterAccount.getBusinessId());
        notificationDetails.setAccountId(bizTwitterAccount.getAccountId());
        notificationDetails.setPageName(bizTwitterAccount.getHandle().substring(1));
        notificationDetails.setPostId(postId);
        notificationDetails.setFeedId(postId);
        notificationDetails.setEngageFeedId(postId);
        notificationDetails.setEventParentId(postId);
        if(Objects.nonNull(user)){
            notificationDetails.setAuthorId(user.getIdStr());
            notificationDetails.setAuthorName(user.getName());
            notificationDetails.setAuthorUsername(user.getScreen_name());
            notificationDetails.setAuthorProfileImage(user.getProfileImageUrlHttps());
            notificationDetails.setPostUrl(TWITTER_BASE_URL + event.getUser().getScreenName() + TWITTER_STATUS_CONST + event.getIdStr());
            notificationDetails.setFeedUrl(notificationDetails.getPostUrl());
            notificationDetails.setReviewerUrl(TWITTER_BASE_URL + event.getUser().getScreenName());
        }
        notificationDetails.setText(checkTextWithImage(event));
        notificationDetails.setEventAction("QuoteTweet");
        notificationDetails.setIsLikedByAdmin(false);
        notificationDetails.setIsCompleted(false);
        notificationDetails.setIsAdminComment(false);
        notificationDetails.setIsParentComment(false);
        notificationDetails.setSourceId(SocialChannel.TWITTER.getId());
        notificationDetails.setChannel(SocialChannel.TWITTER.getName());
        notificationDetails.setType(EngageV2FeedTypeEnum.QUOTE_TWEET.name());
        notificationDetails.setCanReplyPrivately(true);
        //notificationDetails.setHideOnThread(false);
        notificationDetails.setHideOnThread(user.getId().equalsIgnoreCase(String.valueOf(forUserId)) ? true : false);
        notificationDetails.setFeedDate(DateTimeUtils.localToUTCSqlFormat(event.getCreatedAt()));
        notificationDetails.setIsEdited(false);
        if( Objects.nonNull(event.getExtendedEntitiesData()) && Objects.nonNull(event.getExtendedEntitiesData().getMedia())){
            List<String> imageUrls = new ArrayList<>();
            List<String> videoUrls = new ArrayList<>();
            for(ExtendedMediaEntity media : event.getExtendedEntitiesData().getMedia()){
                if(media.getType() != null && media.getType().equalsIgnoreCase(PHOTO)){
                    imageUrls.add(media.getMedia_url_https());
                }else if(media.getType() != null && (media.getType().equalsIgnoreCase(VIDEO) ||
                        media.getType().equalsIgnoreCase(GIF))){
                    videoUrls.add(media.getVideoInfo().getVariants().get(0).getUrl());
                }
                notificationDetails.setPostUrl(media.getExpanded_url());
                notificationDetails.setFeedUrl(notificationDetails.getPostUrl());
            }
            notificationDetails.setImageUrls(imageUrls);
            notificationDetails.setVideoUrls(videoUrls);
            notificationDetails.setFeedUrl(notificationDetails.getPostUrl());
        }
        if(quotedStatus != null){
            EngageNotificationDetails quoteObject = new EngageNotificationDetails();
            quoteObject.setFeedDate(quotedStatus.getCreatedAt());
            quoteObject.setId(quotedStatus.getIdStr());
            if(Objects.nonNull(quotedStatus.getUser())){
                quoteObject.setAuthorId(quotedStatus.getUser().getIdStr());
                quoteObject.setAuthorName(quotedStatus.getUser().getName());
                quoteObject.setAuthorUsername(quotedStatus.getUser().getScreen_name());
                quoteObject.setAuthorProfileImage(quotedStatus.getUser().getProfileImageUrlHttps());
                quoteObject.setPostUrl(TWITTER_BASE_URL + quotedStatus.getUser().getScreenName() + TWITTER_STATUS_CONST + quotedStatus.getIdStr());
                quoteObject.setFeedUrl(quoteObject.getPostUrl());
                quoteObject.setReviewerUrl(TWITTER_BASE_URL + quotedStatus.getUser().getScreenName());
            }

            quoteObject.setText(getDisplayText(quotedStatus.getText(),quotedStatus.getDisplay_text_range()));
            if( Objects.nonNull(quotedStatus.getExtendedEntitiesData()) && Objects.nonNull(quotedStatus.getExtendedEntitiesData().getMedia())){
                List<String> imageUrls = new ArrayList<>();
                List<String> videoUrls = new ArrayList<>();
                for(ExtendedMediaEntity media : quotedStatus.getExtendedEntitiesData().getMedia()){
                    if(media.getType() != null && media.getType().equalsIgnoreCase(PHOTO)){
                        imageUrls.add(media.getMedia_url_https());
                    }else if(media.getType() != null && (media.getType().equalsIgnoreCase(VIDEO) ||
                            media.getType().equalsIgnoreCase(GIF))){
                        videoUrls.add(media.getVideoInfo().getVariants().get(0).getUrl());
                    }
                    quoteObject.setPostUrl(media.getExpanded_url());
                    quoteObject.setFeedUrl(quoteObject.getPostUrl());

                }
                quoteObject.setImageUrls(imageUrls);
                quoteObject.setVideoUrls(videoUrls);
            }
            else if(Objects.nonNull(quotedStatus.getExtendedData()) && Objects.nonNull(quotedStatus.getExtendedData().getMedia())){
                List<String> imageUrls = new ArrayList<>();
                List<String> videoUrls = new ArrayList<>();
                for(ExtendedMediaEntity media : quotedStatus.getExtendedData().getMedia()){
                    if(media.getType() != null && media.getType().equalsIgnoreCase(PHOTO)){
                        imageUrls.add(media.getMedia_url_https());
                    }else if(media.getType() != null && (media.getType().equalsIgnoreCase(VIDEO) ||
                            media.getType().equalsIgnoreCase(GIF))){
                        videoUrls.add(media.getVideoInfo().getVariants().get(0).getUrl());
                    }
                    quoteObject.setPostUrl(media.getExpanded_url());
                    quoteObject.setFeedUrl(quoteObject.getPostUrl());

                }
                quoteObject.setImageUrls(imageUrls);
                quoteObject.setVideoUrls(videoUrls);
            }

            notificationDetails.setParentPostText(quotedStatus.getText());
            notificationDetails.setSubEvent(quoteObject);


        }
        return notificationDetails;
    }
    private EngageNotificationDetails convertCommentNotificationToESObject(BusinessTwitterAccounts bizTwitterAccount, MediaTweet event, String postId,
                                                                           Long forUserId) {
        EngageNotificationDetails notificationDetails = new EngageNotificationDetails();
        TwitterUser user = event.getUser();
        notificationDetails.setPageId(bizTwitterAccount.getProfileId()+"");
        notificationDetails.setAccountId(bizTwitterAccount.getAccountId());
        notificationDetails.setLocationId(bizTwitterAccount.getBusinessId());
        notificationDetails.setPageName(bizTwitterAccount.getHandle().substring(1));
        notificationDetails.setFeedId(postId);
        notificationDetails.setEngageFeedId(postId);
        if(Objects.nonNull(user)){
            notificationDetails.setAuthorId(user.getIdStr());
            notificationDetails.setAuthorName(user.getName());
            notificationDetails.setAuthorUsername(user.getScreen_name());
            notificationDetails.setAuthorProfileImage(user.getProfileImageUrlHttps());
            notificationDetails.setPostUrl(TWITTER_BASE_URL + event.getUser().getScreenName() + TWITTER_STATUS_CONST + event.getIdStr());
            notificationDetails.setFeedUrl(notificationDetails.getPostUrl());
            notificationDetails.setReviewerUrl(TWITTER_BASE_URL + event.getUser().getScreenName());
        }
        notificationDetails = setParentCommentDetails(notificationDetails, event, bizTwitterAccount.getProfileId().toString());

        if( Objects.nonNull(event.getExtendedEntitiesData()) && Objects.nonNull(event.getExtendedEntitiesData().getMedia())){
            List<String> imageUrls = new ArrayList<>();
            List<String> videoUrls = new ArrayList<>();
            for(ExtendedMediaEntity media : event.getExtendedEntitiesData().getMedia()){
                if(media.getType() != null && media.getType().equalsIgnoreCase(PHOTO)){
                    imageUrls.add(media.getMedia_url_https());
                }else if(media.getType() != null && (media.getType().equalsIgnoreCase(VIDEO) ||
                 media.getType().equalsIgnoreCase(GIF))){
                    videoUrls.add(media.getVideoInfo().getVariants().get(0).getUrl());
                }
            }
            notificationDetails.setImageUrls(imageUrls);
            notificationDetails.setVideoUrls(videoUrls);
        }
        notificationDetails.setText(checkTextWithImage(event));

        notificationDetails.setEventAction("COMMENT");
        notificationDetails.setIsLikedByAdmin(false);
        notificationDetails.setIsCompleted(false);
        notificationDetails.setIsAdminComment(user.getId().equalsIgnoreCase(String.valueOf(forUserId)) ? true : false);
        notificationDetails.setSourceId(SocialChannel.TWITTER.getId());
        notificationDetails.setChannel(SocialChannel.TWITTER.getName());
        notificationDetails.setType(EngageV2FeedTypeEnum.COMMENT.name());
        notificationDetails.setCanReplyPrivately(true);
        notificationDetails.setHideOnThread(false);
        notificationDetails.setFeedDate(DateTimeUtils.localToUTCSqlFormat(event.getCreatedAt()));
            notificationDetails.setIsEdited(false);
        return notificationDetails;
    }

    private EngageNotificationDetails setParentCommentDetails(EngageNotificationDetails notificationDetails, MediaTweet event, String pageId) {
        if(Objects.nonNull(event.getIn_reply_to_status_id_str())){
            try{


                EngageNotificationDetails feed = fetchEsDocByFeedIdAndPageId(event.getIn_reply_to_status_id_str(), pageId);
                if(feed != null && feed.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.POST.name())){
                    notificationDetails.setPostId(event.getIn_reply_to_status_id_str());
                    notificationDetails.setEngageFeedId(event.getIn_reply_to_status_id_str());
                    notificationDetails.setEventParentId(event.getIn_reply_to_status_id_str());
                    notificationDetails.setParentPostText(feed.getText());
                    notificationDetails.setIsParentComment(true);
                }else if(feed != null && feed.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.COMMENT.name())){
                    notificationDetails.setPostId(feed.getPostId());
                    notificationDetails.setEngageFeedId(feed.getPostId());

                    if(feed.getIsParentComment())
                        notificationDetails.setEventParentId(event.getIn_reply_to_status_id_str());
                    else
                        notificationDetails.setEventParentId(feed.getEventParentId());
                    notificationDetails.setSubType(EngageV2FeedSubTypeEnum.REPLY.name());
                    notificationDetails.setIsParentComment(false);
                    notificationDetails.setParentPostText(feed.getParentPostText());

                } else if  (feed != null && feed.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.QUOTE_TWEET.name())){
                    notificationDetails.setPostId(event.getIn_reply_to_status_id_str());
                    notificationDetails.setEngageFeedId(event.getIn_reply_to_status_id_str());
                    notificationDetails.setEventParentId(event.getIn_reply_to_status_id_str());
                    notificationDetails.setParentPostText(feed.getText());
                    notificationDetails.setIsParentComment(true);
                } else if(feed != null && feed.getType().equalsIgnoreCase(EngageV2FeedTypeEnum.RETWEET.name())){
                    notificationDetails.setPostId(event.getIn_reply_to_status_id_str());
                    notificationDetails.setEngageFeedId(event.getIn_reply_to_status_id_str());
                    notificationDetails.setEventParentId(event.getIn_reply_to_status_id_str());
                    notificationDetails.setParentPostText(feed.getText());
                    notificationDetails.setIsParentComment(true);
            }
            else if(Objects.isNull(feed)){
                    // fetch old post or comment
                }
            }catch(IOException e){
                LOG.info("Twitter Comment webhook :Error while reading data from ES: {}",event,e);
            }
        }
        return notificationDetails;
    }

    public EngageNotificationDetails getFeedDocumentFromEs(String contentId) throws IOException {
        GetResponse doc = esService.fetchEsDocumentByDocId(contentId, ElasticConstants.SOCIAL_ENGAGE_V2_FEED.getName() );
        if(doc.isExists()) {
            return JSONUtils.fromJSON(doc.getSourceAsString(), EngageNotificationDetails.class);
        }
        return null;
    }
    private String checkTextWithImage(MediaTweet event) {
        int count = 0;
        if(Objects.nonNull(event.getEntities()) && CollectionUtils.isNotEmpty(event.getEntities().getMedia())){
            List<TwitterMedia> mediaList = event.getEntities().getMedia();
            for (TwitterMedia twitterMedia : mediaList) {
                count = StringUtils.isNotEmpty(twitterMedia.getUrl()) ?twitterMedia.getUrl().length() : 0;
            }
        }
        if(CollectionUtils.isNotEmpty(event.getDisplay_text_range())){
            return event.getText().substring(event.getDisplay_text_range().get(0),event.getText().length() - count);
        }
        return event.getText();
    }

    private BusinessTwitterAccounts getBusinessTwitterPage(Long profileId) {
        List<BusinessTwitterAccounts> twitterAccounts = socialTwitterRepo.findByProfileId(profileId);
        if(CollectionUtils.isNotEmpty(twitterAccounts)){
            Optional<BusinessTwitterAccounts> bTwitterAccount = twitterAccounts.stream().filter(e->Objects.nonNull(e.getBusinessId())).findFirst();
            if(bTwitterAccount.isPresent()){
                return bTwitterAccount.get();
            } else  {
                LOG.error("No business found with twitter account {}",profileId);
            }
        }
        return null;
    }
    private boolean postAlreadyExists(String feedId, String type, String pageId) {
        return Objects.nonNull(engageFeedDetailsRepo.findByEngageIdAndTypeAndPageId(feedId, type,pageId));
    }
    private Integer validateContentDetails(EngageNotificationDetails details) {
        EngageFeedDetails existingPost = engageFeedDetailsRepo.findFirstByFeedIdAndPageId(details.getFeedId(),details.getPageId());
        if (Objects.isNull(existingPost)) { // create new entry in DB
            EngageFeedDetails data = new EngageFeedDetails();
            data.setEngageId(details.getPostId());
            data.setChannel(SocialChannel.TWITTER.getName());
            data.setText(details.getText());
            data.setPageId(details.getPageId());
            data.setFeedId(details.getFeedId());
            data.setAuthorId(details.getAuthorId());
            data.setParentId(details.getEventParentId());
            data.setType(details.getType());
            data.setFeedUrl(details.getPostUrl());
            data.setAuthorName(details.getAuthorName());
            data.setAuthorUrl(details.getReviewerUrl());
            data.setFeedDate(details.getFeedDate());
            data.setImageUrls(Objects.nonNull(details.getImageUrls()) ? details.getImageUrls().toString() : null);
            return engageFeedDetailsRepo.save(data).getId();
        }else{
            return null;
        }
    }
}
