package com.birdeye.social.service.approvalworkflow;


import com.birdeye.social.entities.SocialPost;
import com.birdeye.social.model.*;
import com.birdeye.social.model.approval_workflow.*;

import java.io.IOException;
import java.util.List;

public interface ApprovalWorkflowService {
    ApprovalWorkflowResponse getAllApprovals(ApprovalWorkFlowRequest approvalRequest,Integer businessId,Long businessNumber,Integer userId,Integer startIndex,Integer pageSize,String sortParam,String sortOrder);

    List<ApprovalWorkFlowData> getApprovalDataFromPostId(Integer userId, Integer businessId, Long enterpriseId, String postId, boolean isPublicPost, String timeZone);

    ApprovalWorkFlowData getApprovalDataFromPostId(Integer userId, Integer businessId, Long enterpriseId, Integer postId, boolean isPublicPost, String timeZone);

    void updateCountForApproval(SocialPostEsRequest socialPostEsRequest);

    ApprovalCountResponse getApprovalCount(Integer userId, Integer businessId);

    void resetApprovalCount(ResetCountRequest resetCountRequest);

    ApprovalWorkFlowData getApprovalDataFromPostId(Integer postId, Long enterpriseId) throws IOException;

    void processReminderEvent(SocialPostPostIdRequest socialPostPostIdRequest) throws Exception;

    void processRejectedEvent(SocialPostPostIdRequest socialPostPostIdRequest, List<Integer> socialPostIds) throws Exception;

    void processExpiredEvent(SocialPostPostIdRequest socialPostPostIdRequest, List<Integer> socialPostIds) throws Exception;

    ApprovalEmailResponse sendApprovalEmail(Long enterpriseId, ApprovalEmailReminder approvalEmailReminder, Integer businessId, Integer userId) throws Exception;

    void processApprovedEvent(SocialPostPostIdRequest postIdRequest, List<Integer> socialPostIds) throws Exception;

    void processScheduleEvent(SocialPostPostIdRequest postIdRequest, List<Integer> socialPostIds) throws Exception;

    ApprovalWorkflowEvent getApprovalDataFromApproval(ApprovalEventFromSocial approvalEventFromSocial) throws Exception;

    void deleteApproval(ApprovalEventFromSocial approvalEventFromSocial) throws Exception;

    ApprovalWorkflowEvent createOrUpdateApprovalEvent(SocialPostInputMessageRequest socialPostInput, SocialPost oldPost,String activity);

    SocialPostEsRequest findByPostId(Integer postId);

    void updateTagInEs(Integer postId, Integer enterpriseId);

    void processApprovalWorkflowEvent(SocialPostPostIdRequest postIdRequest, String operation) throws Exception;

    void processApprovalAndSendRequestToCore(ApprovalBulkRequest approvalBulkRequest, String action, Integer userId);

    void handleFailedEvent(Integer socialPostId);
}
