package com.birdeye.social.service.doup;

import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessLinkedinPageRepository;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import com.birdeye.social.entities.SocialStreams;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.model.SocialStreamConnectRequest;
import com.birdeye.social.service.CommonService;
import com.birdeye.social.service.SocialErrorMessagePageService;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.socialReseller.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LinkedinDoupConsumeRecords implements DoupConsumeRecords{

    @Autowired
    private BusinessLinkedinPageRepository linkedInRepo;

    @Autowired
    private SocialErrorMessagePageService socialErrorMessageService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private KafkaProducerService kafkaProducer;

    @Override
    public String channelName() {
        return SocialChannel.LINKEDIN.getName();
    }

    @Override
    public SocialResellerReportUploadDTO processMappingIntegrationReport(Long resellerId, Integer size, Integer page) {
        try {
            SocialResellerReportUploadDTO resellerReportUploadDTO = new SocialResellerReportUploadDTO();
            Page<BusinessLinkedinPage> unmappedLinkedinAccounts = linkedInRepo.findByResellerIdAndBusinessIdIsNull(resellerId, new PageRequest(page, size));
            if (Objects.isNull(unmappedLinkedinAccounts.getContent()) ) {
                return null;
            }

            List<SocialResellerReportLinkedinPages> socialResellerReportLinkedinPagesList =
                    unmappedLinkedinAccounts.getContent().stream().map(data -> {
                        SocialResellerReportLinkedinPages conf = new SocialResellerReportLinkedinPages();
                        conf.setPageId(data.getProfileId());
                        conf.setPageName(getLinkedinPageName(data));
                        return conf;
                    } ).collect(Collectors.toList());

            resellerReportUploadDTO.setData(socialResellerReportLinkedinPagesList);
            resellerReportUploadDTO.setPageCount(unmappedLinkedinAccounts.getTotalPages());
            resellerReportUploadDTO.setTotalCount(unmappedLinkedinAccounts.getTotalElements());

            return resellerReportUploadDTO;

        } catch (Exception e) {
            log.error("something went wrong while fetching the location mapping data for resellerId {}, error {}", resellerId, e);
            return null;
        }
    }

    private String getLinkedinPageName(BusinessLinkedinPage businessLinkedinPage) {
        if(businessLinkedinPage.getPageType().equalsIgnoreCase(LinkedinPageTypeEnum.COMPANY.getName())){
           return businessLinkedinPage.getCompanyName();
        }else{
            String fullName = null;
            if(StringUtils.isNotBlank(businessLinkedinPage.getFirstName()) && StringUtils.isNotBlank(businessLinkedinPage.getLastName())){
                fullName = businessLinkedinPage.getFirstName()+ " "+businessLinkedinPage.getLastName();
            }else{
                fullName = businessLinkedinPage.getFirstName();
            }
            return fullName;
        }
    }

    @Override
    public  List<Integer> processLocationUnMappingIntegration(Long resellerId) {
            return linkedInRepo.findByResellerIdAndBusinessIdIsNotNull(resellerId);
    }


    @Override
    public SocialResellerBulkStatusDTO mapPageWithLocation(SocialResellerBulkImportDTO data, Long resellerId, BusinessLiteDTO businessDetails,
                                                           Long enterpriseId, Integer accountId) {
        SocialResellerBulkStatusDTO socialResellerBulkStatus = new SocialResellerBulkStatusDTO();
        try {
            socialResellerBulkStatus.setEventId(data.getEventId());
            socialResellerBulkStatus.setOperation("ADD");

            String pageId= data.getData().getSocialPageId();
            Integer businessId = businessDetails.getBusinessId();


            BusinessLinkedinPage businessLinkedinPage = linkedInRepo.findByProfileId(pageId);

            if(Objects.isNull(businessLinkedinPage) ) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.PAGE_NOT_FOUND.name(),
                        SocialChannel.LINKEDIN.getLabel());
                return createLinkedinDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            } else if(Objects.nonNull(businessLinkedinPage.getBusinessId())) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.PAGE_ALREADY_MAPPED_ERR.name(),
                        SocialChannel.LINKEDIN.getLabel());
                return createLinkedinDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.DUPLICATE.name());
            } else if (!businessLinkedinPage.getResellerId().equals(resellerId)) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.ACCOUNT_ACCESS_ERR.name(),
                        SocialChannel.LINKEDIN.getLabel());
                return createLinkedinDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            } else if (linkedInRepo.existsByBusinessId(businessId)) {
                String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.BUSINESS_MAPPED_ERR.name(),
                        SocialChannel.LINKEDIN.getLabel());
                return createLinkedinDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
            }

            businessLinkedinPage.setBusinessId(businessId);
            businessLinkedinPage.setEnterpriseId(enterpriseId);
            businessLinkedinPage.setAccountId(accountId);

            linkedInRepo.saveAndFlush(businessLinkedinPage);
            socialResellerBulkStatus.setStatus(ResellerMappingStatusEnum.SUCCESS.name());
            socialResellerBulkStatus.setErrorMessage("");
            commonService.sendLinkedinSetupAuditEvent(SocialSetupAuditEnum.ADD_MAPPING.name(),  Arrays.asList(businessLinkedinPage), null, businessId,businessLinkedinPage.getResellerId());

            log.info("[Linkedin Setup] Linkedin Account with profileId: {} successfully mapped to locationId: {}", businessLinkedinPage.getProfileId(), businessId);
            kafkaProducer.sendObject(Constants.LINKEDIN_PAGE_MAPPING_ADDED, new LocationPageMappingRequest(businessLinkedinPage.getBusinessId(), String.valueOf(businessLinkedinPage.getProfileId()), String.valueOf(businessLinkedinPage.getPageType())));

            return socialResellerBulkStatus;
        } catch (Exception e) {
            log.error("RESELLER_MAPPING_EVENT: FOR Linkedin, cannot map location with reseller {} {}", resellerId, e.toString() );
            String errMessage = socialErrorMessageService.getMessage(SocialErrorMessagesEnum.UNKNOWN_ERR.name(),SocialChannel.LINKEDIN.getLabel());
            return createLinkedinDoupErrorResponse(socialResellerBulkStatus,data.getEventId(),errMessage,ResellerMappingStatusEnum.FAILURE.name());
        }
    }

    private SocialResellerBulkStatusDTO createLinkedinDoupErrorResponse(SocialResellerBulkStatusDTO socialResellerBulkStatus, long eventId,
                                                                        String errMessage, String status) {
        socialResellerBulkStatus.setEventId(eventId);
        socialResellerBulkStatus.setStatus(status);
        socialResellerBulkStatus.setOperation(Constants.REJECTED);
        socialResellerBulkStatus.setErrorMessage(errMessage);
        return socialResellerBulkStatus;
    }

	@Override
	public List<Integer> getMappedLocations(List<Long> bizHierarchyList) {
		return linkedInRepo.findByEnterpriseIdInAndBusinessIdIsNotNull(bizHierarchyList);
	}
}
