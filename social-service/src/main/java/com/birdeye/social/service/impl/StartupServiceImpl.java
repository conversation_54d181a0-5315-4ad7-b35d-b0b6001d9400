/**
 *
 */
package com.birdeye.social.service.impl;

import com.birdeye.social.cache.*;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.dao.SocialFeatureRestrictBusinessRepository;
import com.birdeye.social.dao.SocialPropertyRepository;
import com.birdeye.social.entities.SocialFeatureRestrictBusiness;
import com.birdeye.social.entities.SocialProperty;
import com.birdeye.social.model.SocialPropertyRequest;
import com.birdeye.social.service.IStartupService;
import com.birdeye.social.service.ratelimit.RateLimitService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 */

@Service("startupServiceimpl")
public class StartupServiceImpl implements IStartupService {
	
	private static final Logger			LOGGER	= LoggerFactory.getLogger(StartupServiceImpl.class);

	@Autowired
	private SocialPropertyRepository socialPropertyRepository;
	@Autowired
	private RateLimitService rateLimitService;

	@Autowired
	private SocialFeatureRestrictBusinessRepository restrictFeatureRepo;

	// At second :00 of minute :51 of every hour
	private static final String			AUTO_RELOAD_CACHE_CRON_PATTERN	= "0 54 * * * *";

	@PostConstruct
	public void init() {
		loadSystemPropertyCache();
//		loadParametersCache();
		loadRestrictedFeatureCache();
		loadRateLimitCache();
		loadRateLimitMaster();
	}

	@Override
	public void loadSelectiveCache(String type) throws Exception {
		if (StringUtils.equalsIgnoreCase("systemproperty", type)) {
			loadSystemPropertyCache();
		}if(StringUtils.equalsIgnoreCase("socialFeatureRestrictedCache", type)) {
			loadRestrictedFeatureCache();
		}
	}

	@Scheduled(cron = AUTO_RELOAD_CACHE_CRON_PATTERN)
	@Override
	public void loadAllCache() {
		loadSystemPropertyCache();
//		loadParametersCache();
		loadRestrictedFeatureCache();
		loadRateLimitCache();
		loadRateLimitMaster();
	}

	private void loadSystemPropertyCache() {
		LOGGER.info("STARTED loading SystemPropertiesCache from social_property table.");//NOSONAR
		List<SocialProperty> systemPropertyroperties = socialPropertyRepository.findAll();
		SystemPropertiesCache systemPropertiesCache = new SystemPropertiesCache();
		systemPropertyroperties.stream().filter(systemProperty -> systemProperty.getName() != null && systemProperty.getValue() != null)
		.forEach(systemProperty -> systemPropertiesCache.addProperty(systemProperty.getName(), systemProperty.getValue()));
		CacheManager.getInstance().setCache(systemPropertiesCache);
		LOGGER.info("ENDED loading SystemPropertiesCache");//NOSONAR
	}

//	private void loadParametersCache() {
//		LOGGER.info("STARTED loading ParametersCache from parameters table.");
//		ParametersCache parametersCache = new ParametersCache();
//		parametersRepository.findAll()
//				.stream()
//				.filter(param -> param.getName() != null && param.getValue() != null)
//				.forEach(param -> parametersCache.addProperty(param.getName(), param.getValue()));
//		CacheManager.getInstance().setCache(parametersCache);
//		LOGGER.info("ENDED loading ParametersCache");
//	}

	private void loadRestrictedFeatureCache() {
		LOGGER.info("STARTED loading RestrictedFeatureCache from social_feature_restricted_business table");
		List<SocialFeatureRestrictBusiness> restrictFeatures = restrictFeatureRepo.findAll();
		SocialFeatureRestrictedCache restrictedFeatureCache = new SocialFeatureRestrictedCache();
		restrictFeatures.forEach(entry -> {
			if(null != entry.getBusinessId() && 0 != entry.getBusinessId()) { // businessId is restricted, add to businessId map
				restrictedFeatureCache.addToRestrictedBusiness(entry.getBusinessId(), entry.getRestrictedFeatures());

			} else if(Objects.nonNull(entry.getType()) && Objects.equals(Constants.ALL,entry.getAccountType())) { // type is restricted, add to type map
				restrictedFeatureCache.addToRestrictedType(entry.getType(), entry.getRestrictedFeatures());

			} else if (Objects.nonNull(entry.getAccountType()) && Objects.equals(Constants.ALL,entry.getType())) { // accounttype is restricted, add to accounttype map
				restrictedFeatureCache.addToRestrictedAccounttype(entry.getAccountType(), entry.getRestrictedFeatures());

			} else if (Objects.nonNull(entry.getType()) && Objects.nonNull(entry.getAccountType())) { // add to type+accounttype map
				restrictedFeatureCache.addToRestrictedType_AccountType(entry.getType() + "_" +entry.getAccountType() , entry.getRestrictedFeatures());
			}
		});

		CacheManager.getInstance().setCache(restrictedFeatureCache);
		LOGGER.info("ENDED loading RestrictedFeatureCache");//NOSONAR
	}

	private void loadRateLimitCache() {
		rateLimitService.loadRateLimitCache();
	}

	private void loadRateLimitMaster() {
		rateLimitService.loadRateLimitMaster();
		rateLimitService.loadRateLimitDomainInfo();
	}

	@Override
	public boolean updateProperty(SocialPropertyRequest socialPropertyRequest) {
		try {
			LOGGER.info("Started updating property : {} ", socialPropertyRequest);
			switch (socialPropertyRequest.getRequestType().toLowerCase()) {
				case "insert":
					insertSocialPropertyData(socialPropertyRequest);
					break;
				case "update":
					updateSocialPropertyData(socialPropertyRequest);
					break;
				case "delete":
					deleteSocialPropertyData(socialPropertyRequest);
					break;
				default:
					LOGGER.info("Invalid Request");
			}
			LOGGER.info("Property updated successfully.");
			loadSystemPropertyCache();
			return true;
		} catch (Exception e) {
			LOGGER.info("Error in DB operation on social property table : {} ", e.getMessage());
			return false;
		}
	}


	public void insertSocialPropertyData(SocialPropertyRequest request) {

		List<SocialProperty> socialPropertyList =
				request.getData().stream()
						.map(temp -> {
							SocialProperty socialProperty = new SocialProperty();
							socialProperty.setName(temp.getPropertyName());
							socialProperty.setValue(temp.getPropertyValue());
							socialProperty.setCreatedAt(new Date());
							socialProperty.setUpdatedAt(new Date());
							return socialProperty;
						})
						.collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(socialPropertyList)) {
			socialPropertyRepository.save(socialPropertyList);
		} else {
			LOGGER.info("No insert operation performed in social_property table");
		}
	}
	public void updateSocialPropertyData(SocialPropertyRequest request){
		request.getData().forEach(data -> {
			if (Objects.isNull(data.getPropertyName())) {
				LOGGER.error("Update operation can not be performed");
				return;
			}
			SocialProperty socialProperty = socialPropertyRepository.findByName(data.getPropertyName());

			if (Objects.isNull(socialProperty)) {
				LOGGER.error("Update operation can not be performed on :{}", data.getPropertyName());
				return;
			}
			Date currentDate = new Date();
			socialProperty.setValue(data.getPropertyValue());
			socialProperty.setUpdatedAt(currentDate);
			socialPropertyRepository.saveAndFlush(socialProperty);
		});
	}
	public void deleteSocialPropertyData(SocialPropertyRequest request){

		List<SocialProperty> socialPropertyList =
				request.getData().stream().filter(data -> Objects.nonNull(data.getId()))
						.map(temp -> {
							SocialProperty socialProperty = new SocialProperty();
							socialProperty.setId(temp.getId());
							return socialProperty;
						})
						.collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(socialPropertyList)) {
			socialPropertyRepository.deleteInBatch(socialPropertyList);
		} else {
			LOGGER.info("No delete operation performed in social_property table");
		}
	}
}
