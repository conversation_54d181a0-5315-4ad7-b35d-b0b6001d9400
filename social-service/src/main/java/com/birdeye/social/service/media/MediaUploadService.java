package com.birdeye.social.service.media;

import com.birdeye.social.external.request.mediaupload.MediaUploadRequest;
import com.birdeye.social.external.request.mediaupload.MediaUploadChunkRequest;
import com.birdeye.social.external.request.mediaupload.MediaUploadRequestForAsset;

public interface MediaUploadService {
    void initiateMediaUpload(MediaUploadRequest mediaInitiateRequest);

    void saveMediaChunkFromPictures(MediaUploadChunkRequest mediaUploadChunkRequest);

    void processVideoChunkUpload(MediaUploadRequestForAsset request);

    void uploadChunkForMediaRequest(MediaUploadRequestForAsset request);

    MediaUploadRequest uploadCaptionAndThumbnail(MediaUploadRequest mediaUploadRequest);

    MediaUploadRequest finalizeUpload(MediaUploadRequest mediaUploadRequest);

    MediaUploadRequest checkUploadStatus(MediaUploadRequest mediaUploadRequest) throws Exception;

    void postContentWithMedia(MediaUploadRequest request);

    void exceptionHandler(MediaUploadRequest request);
}
