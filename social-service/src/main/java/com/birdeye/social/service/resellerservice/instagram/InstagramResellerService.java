package com.birdeye.social.service.resellerservice.instagram;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.*;
import com.birdeye.social.dao.BusinessInstagramAccountRepository;
import com.birdeye.social.entities.BusinessGetPageRequest;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.model.ChannelAuthRequest;
import com.birdeye.social.model.ChannelConnectedPageInfo;
import com.birdeye.social.model.PageConnectionStatus;
import com.birdeye.social.model.instagram.InstagramAuthRequest;
import com.birdeye.social.service.IInstragramSetupService;
import com.birdeye.social.service.resellerservice.SocialReseller;
import com.birdeye.social.sro.*;
import com.birdeye.social.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class InstagramResellerService implements SocialReseller {

    @Autowired
    private IInstragramSetupService socialInstagramService;

    @Autowired
    private BusinessInstagramAccountRepository instagramAccountRepository;
    
    @Autowired
	private IBusinessCoreService iBusinessCoreService;

    @Override
    public String channelName() {
        return SocialChannel.INSTAGRAM.getName();
    }

	@Override
	public PaginatedConnectedPages getPages(Long resellerId, PageConnectionStatus pageConnectionStatus,
                                            Integer page, Integer size, String search, ResellerSearchType searchType, PageSortDirection sortDirection,
                                            ResellerSortType sortParam, List<Integer> locationIds, MappingStatus mappingStatus, List<Integer> userIds,
                                            Boolean locationFilterSelected, String type) {
		return socialInstagramService.getResellerPages(resellerId, pageConnectionStatus, page, size,search, searchType, sortDirection,
                sortParam, locationIds, mappingStatus, userIds, locationFilterSelected);
	}

	@Override
	public void removeResellerPages(List<String> pageIds, Integer limit) {
		socialInstagramService.removeIGPageForReseller(pageIds, limit);
	}

    @Override
    public ChannelPageInfo connectResellerPages(List<String> pageIds, Long resellerId, Boolean selectAll, String searchStr) {
        return connectInstagramAccountForReseller(resellerId, pageIds, selectAll, searchStr);
    }

    @Override
    public ChannelConnectedPageInfo checkIfAccountExistsByResellerId(Long accountId) {
        ChannelConnectedPageInfo channelConnectedPageInfo= new ChannelConnectedPageInfo();
        channelConnectedPageInfo.setInstagramAccountExists(instagramAccountRepository.existsByResellerIdAndIsSelected(accountId,1));
        return channelConnectedPageInfo;
    }

    @Override
    public void reconnectResellerPages(Long resellerId, ChannelAllPageReconnectRequest request,
                                       Integer userId, String type, Integer limit) {
        InstagramAuthRequest authRequest = createInstagramAuthRequest(userId, resellerId, request);
        socialInstagramService.reconnectInstagramAccounts(authRequest);
    }

    @Override
    public void submitFetchPageRequest(ChannelAuthRequest authRequest, String type) {
        socialInstagramService.submitFetchPageRequest(authRequest.getBusinessId(), authRequest.getBirdeyeUserId(),
                authRequest.getAuthCode(), authRequest.getRedirectUri(), authRequest.getTempAccessToken(), type);

    }

    @Override
    public Map<String, Object> getPaginatedPages(BusinessGetPageRequest businessGetPageRequest, Integer page, Integer size, String search) {
        return socialInstagramService.getPaginatedPages(businessGetPageRequest, page, size,search);
    }

    private InstagramAuthRequest createInstagramAuthRequest(Integer userId, Long resellerId, ChannelAllPageReconnectRequest request) {
        InstagramAuthRequest authRequest = new InstagramAuthRequest();
        authRequest.setBirdeyeUserId(userId);
        authRequest.setBusinessId(resellerId);
        authRequest.setAuthCode(request.getAuthCode());
        authRequest.setRedirectUri(request.getRedirectUri());
        authRequest.setType(Constants.RESELLER);
        return authRequest;
    }

    private ChannelPageInfo connectInstagramAccountForReseller(Long resellerId, List<String> pageIds, Boolean selectAll,
                                                               String searchStr) {
        ChannelPageInfo accountInfo;
        InstagramConnectAccountRequest instagramConnectAccountRequest=new InstagramConnectAccountRequest();
        instagramConnectAccountRequest.setId(pageIds);
        instagramConnectAccountRequest.setBusinessId(resellerId);
        instagramConnectAccountRequest.setType(Constants.RESELLER);
        instagramConnectAccountRequest.setSearchStr(searchStr);
        instagramConnectAccountRequest.setSelectAll(selectAll);
        accountInfo=socialInstagramService.connectInstagramAccountV1(instagramConnectAccountRequest,null);
        return accountInfo;
    }

	@Override
	public Map<String, Object> saveLocationPageMapping(String channel, Integer locationId, String pageId,String pageType
			,Integer userId, Boolean force, Long resellerId) {
		if (userId != null && StringUtils.isNotEmpty(pageId) && locationId != null) {
			socialInstagramService.saveInstagramLocationMapping(locationId, pageId, userId,Constants.RESELLER, resellerId);
			return new HashMap<>();
		} else {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST,
					"Invalid input present for saveLocationPageMapping");
		}
	}
	
	@Override
	public void removePageMappings(List<LocationPageMappingRequest> input) {
		socialInstagramService.removeInstagramLocationAccountMapping(input,Constants.RESELLER,false);
	}

    /**
     * @param resellerId
     * @param resellerLeafLocationIds
     * @return
     */
    @Override
    public List<Integer> getMappedResellerLeafLocationIds(List<Integer> resellerLeafLocationIds) {
        return socialInstagramService.getMappedResellerLeafLocations(resellerLeafLocationIds);
    }
}
