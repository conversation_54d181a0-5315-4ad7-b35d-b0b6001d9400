package com.birdeye.social.specification;

import com.birdeye.social.constant.PageSortDirection;
import com.birdeye.social.constant.ValidTypeEnum;
import com.birdeye.social.entities.BusinessInstagramAccount;
import com.birdeye.social.entities.BusinessTwitterAccounts;
import com.birdeye.social.entities.BusinessYoutubeChannel;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class YouTubeSpecification {

    public Specification<BusinessYoutubeChannel> hasResellerId(Long resellerId) {
        if(Objects.isNull(resellerId)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("resellerId"),  resellerId);
        });
    }

    public Specification<BusinessYoutubeChannel> hasPageName(String pageName) {

        if(Objects.isNull(pageName)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.like(root.get("channelName"),  "%" + pageName + "%");
        });
    }

    public Specification<BusinessYoutubeChannel> isSelected(Integer i) {
        if(Objects.isNull(i)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("isSelected"), i);
        });
    }
    public Specification<BusinessYoutubeChannel> hasRequestId(String requestId) {
        if(Objects.isNull(requestId)){
            return null;
        }
        return ((root, query, cb) -> {
            return cb.equal(root.get("requestId"), requestId);
        });
    }

    public Specification<BusinessYoutubeChannel> inBusinessIds(List<Integer> businessIds) {
        if(CollectionUtils.isEmpty(businessIds)){
            return null;
        }
        return ((root, query, cb) -> {
            return root.get("businessId").in(businessIds);
        });
    }

    public Specification<BusinessYoutubeChannel> inValidityTypes(List<Integer> validityTypes) {
        if(CollectionUtils.isEmpty(validityTypes)){
            return null;
        }
        return ((root, query, cb) -> {
            return root.get("validType").in(validityTypes);
        });
    }

    public Specification<BusinessYoutubeChannel> inCreatedByIds(List<Integer> createdByIds) {
        if(CollectionUtils.isEmpty(createdByIds)){
            return null;
        }
        return ((root, query, cb) -> {
            return root.get("createdBy").in(createdByIds);
        });
    }

    public Specification<BusinessYoutubeChannel> hasBusinessIdNullOrNotNull(boolean isNull) {
        return ((root, query, cb) -> {
            return isNull
                    ?cb.isNull(root.get("businessId"))
                    :cb.isNotNull(root.get("businessId"));
        });
    }

    public  Specification<BusinessYoutubeChannel> sortBusinessIdNullsFirst() {
        return (root, query, criteriaBuilder) -> {
            // Sort: NULL businessId first, then non-NULL
            query.orderBy(
                    criteriaBuilder.asc(
                            criteriaBuilder.selectCase()
                                    .when(criteriaBuilder.isNull(root.get("businessId")), 0)
                                    .otherwise(1)
                    ),
                    criteriaBuilder.asc(root.get("channelName"))
            );
            return criteriaBuilder.conjunction();
        };
    }

    public  Specification<BusinessYoutubeChannel> sortValidTypesInGroup(PageSortDirection sortDirection) {
        return (root, query, criteriaBuilder) -> {
            // Sort: NULL businessId first, then non-NULL
            query.orderBy(
                    PageSortDirection.ASC.equals(sortDirection)?criteriaBuilder.asc(
                            criteriaBuilder.selectCase()
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.VALID.getId()), 0)
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.PARTIAL_VALID.getId()),1)
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.INVALID.getId()),1)
                                    .otherwise(1)
                    )
                            : criteriaBuilder.desc(
                            criteriaBuilder.selectCase()
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.VALID.getId()), 0)
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.PARTIAL_VALID.getId()),1)
                                    .when(criteriaBuilder.equal(root.get("validType"), ValidTypeEnum.INVALID.getId()),1)
                                    .otherwise(1)
                    ),
                    criteriaBuilder.asc(root.get("channelName"))
            );
            return criteriaBuilder.conjunction();
        };
    }
}
