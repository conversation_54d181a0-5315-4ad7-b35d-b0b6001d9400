package com.birdeye.social.apitest.controller;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ForkJoinPool;

import com.birdeye.social.businessCore.IBusinessCoreService;
import com.birdeye.social.constant.SocialChannel;
import com.birdeye.social.dto.BusinessLiteDTO;
import com.birdeye.social.dto.report.SocialScanEventDTO;
import com.birdeye.social.entities.BusinessLinkedinPage;
import com.birdeye.social.entities.SocialPostPublishInfo;
import com.birdeye.social.external.service.KafkaProducerService;
import com.birdeye.social.facebook.response.FbMediaPublishResponse;
import com.birdeye.social.facebook.response.FbUploadStatusResponse;
import com.birdeye.social.scheduler.LinkedinPagesStatusUpdateScheduler;
import com.birdeye.social.service.*;
import com.birdeye.social.service.SocialReportService.SocialReportService;
import com.birdeye.social.service.instagram.impl.IInstagramService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.birdeye.social.cache.CacheManager;
import com.birdeye.social.cache.SystemPropertiesCache;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.birdeye.social.facebook.FacebookData;
import com.birdeye.social.facebook.FacebookFeedData;
import com.birdeye.social.facebook.FacebookPageAccessInfo;
import com.birdeye.social.facebook.FacebookPostInfo;
import com.birdeye.social.facebook.FacebookService;
import com.birdeye.social.googleplus.GooglePlusService;
import com.birdeye.social.lock.IRedisLockService;
import com.birdeye.social.twitter.TweetInfo;
import com.birdeye.social.twitter.TwitterCreds;
import com.birdeye.social.twitter.TwitterData;
import com.birdeye.social.twitter.TwitterData.FileType;
import com.birdeye.social.twitter.TwitterService;
import com.birdeye.social.service.Youtube.YoutubeService;

@RestController
@RequestMapping("/social")
public class APITestController {
	
	private static final Logger logger = LoggerFactory.getLogger(APITestController.class);

	@Autowired
	private SocialPostGooglePlusService socialPostGooglePlusService;

	@Autowired
	private SocialPostFacebookService socialPostFacebookService;
	
	@Autowired
	private SocialPostTwitterService socialPostTwitterService;
	
	@Autowired
	private FacebookSocialAccountService	facebookAccountService;
	
	@Autowired
	private TwitterSocialAccountService twitterAccountService;
	
	@Autowired
	private CommonService commonService;
	
	@Autowired
	private IRedisLockService redisService;
	
	@Autowired
	private FacebookPageService facebookPageService;

	@Autowired
	private KafkaProducerService kafkaProducerService;
	
	private static final String GOOGLE_CLIENT_ID = "************-87r1mlrt9t80l8tol18ekt90s9h8e3fu.apps.googleusercontent.com";
	private static final String GOOGLE_CLIENT_SECERET = "islqan8nqpGs_ib2sVLE0yuZ";
	private static final String WEBSITE_DOMAIN = "demo.birdeye.com";
	private static final String GOOGLE_PAGE_ID = "110231247107632538023"; // For Jitters
	private static final String GOOGLE_REFRESH_TOKEN = "1/rNQXI672XiZ50O4yw3Lx73Ke3T0l1eaUI6iwXvYA3UU";

	@Autowired
	private TwitterService twitterService;

	@Autowired
	private GooglePlusService googleplusService;

	@Autowired
	private GoogleAuthenticationService googleAuthService;
	
	@Autowired
	private YoutubeService youtubeService;
	
	@Autowired
	private GMBLocationDetailService gmbLocationDetailsService;

	@Autowired
	private LinkedinPagesStatusUpdateScheduler linkedinPagesStatusUpdateScheduler;

	@GetMapping(value = "/welcome")
	public String welcome() {
		return "Welcome to Birdeye Social Module";
	}

	@Autowired
	private FacebookService facebookService;

	@Autowired
	private IBusinessCoreService businessCoreService;



//	@PostMapping(value = "/facebook/txt")
//	public String postTxtFB(@RequestParam(value = "msg") String message) {
//		FacebookPageAccessInfo fbCreds = new FacebookPageAccessInfo();
//		fbCreds.setAccessToken("EAACLXaYYC40BAPhIDZA5O0KwLBkK2GJIgKBE48g0hhGamC5oHA7XeC7Q3SF1I7HIDZA8wVZCORVavIqZC6Rz5juVvM0OXZBiGnGAGm8cWXPfKahs6JkAkgR0p8hzvZB3zQEwW8N1skc84UOZA0hqzUxfVfimiFekx3E30GaO2pZBVgZDZD");
//		fbCreds.setProfileId("1352722748173726");
//		FacebookData data = new FacebookData();
//		data.setText(message);
//		return facebookService.postText2(fbCreds, data);
//	}

	/*
	 * @PostMapping(value = "/facebook/txt1") public FacebookPostInfo postTxtFB1(@RequestParam(value = "msg") String message) throws Exception {
	 * FacebookPageAccessInfo fbCreds = new FacebookPageAccessInfo(); fbCreds.setAccessToken(
	 * "EAACLXaYYC40BAPhIDZA5O0KwLBkK2GJIgKBE48g0hhGamC5oHA7XeC7Q3SF1I7HIDZA8wVZCORVavIqZC6Rz5juVvM0OXZBiGnGAGm8cWXPfKahs6JkAkgR0p8hzvZB3zQEwW8N1skc84UOZA0hqzUxfVfimiFekx3E30GaO2pZBVgZDZD"
	 * ); fbCreds.setProfileId("1352722748173726"); fbCreds.setBaseUrl("https://graph.facebook.com/v2.9/"); FacebookData data = new FacebookData();
	 * data.setText(message); return facebookService.postText1(fbCreds, data);
	 *
	 * }
	 */
	@PostMapping(value = "/facebook/txtnew")
	public FacebookPostInfo postTxtFBNew(@RequestParam(value = "msg") String message) throws Exception {
		FacebookPageAccessInfo fbCreds = new FacebookPageAccessInfo();
		fbCreds.setAccessToken(
				"aaEAACLXaYYC40BAPhIDZA5O0KwLBkK2GJIgKBE48g0hhGamC5oHA7XeC7Q3SF1I7HIDZA8wVZCORVavIqZC6Rz5juVvM0OXZBiGnGAGm8cWXPfKahs6JkAkgR0p8hzvZB3zQEwW8N1skc84UOZA0hqzUxfVfimiFekx3E30GaO2pZBVgZDZD");
		fbCreds.setPageId("1352722748173726");
		fbCreds.setBaseUrl("https://graph.facebook.com/v2.9/");
		FacebookData data = new FacebookData();
		data.setText(message);
		return facebookService.postTextRevamped(fbCreds, data);

	}

	@GetMapping(value = "/twitter/home")
	public List<TweetInfo> homeTimeline() {
		return twitterService.homeTimeLine(twitterCreds);
	}
	
	@PostMapping(value = "/twitter/delete")
	public String deleteTweet(@RequestParam(value = "id") long tweetId) {
		twitterService.deleteStatus(twitterCreds, tweetId);
		return "SUCCESS";
	}
	
	@PostMapping(value = "/twitter/txt")
	public TweetInfo postTxt(@RequestParam(value = "msg") String message) throws Exception {
		return socialPostTwitterService.postTwitterData(twitterCreds, new TwitterData(message));
	}
	
	@PostMapping(value = "/twitter/img", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
	public TweetInfo postTextWithImage(@RequestPart("imgfile") MultipartFile image,
			@RequestParam(value = "msg", required = false) String message) throws Exception {
		if (message == null) {
			message = "Image post by Birdeye";
		}
		return socialPostTwitterService.postTwitterData(twitterCreds, new TwitterData(message, FileType.IMAGE, createTempFile(image)));
	}

	@PostMapping(value = "/twitter/imgs", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
	public TweetInfo postMultipleImages(@RequestPart("imgfile") MultipartFile[] images,
			@RequestParam(value = "msg", required = false) String message) {
		if (message == null) {
			message = "Multiple Image post by Birdeye";
		}
		List<File> files = new ArrayList<>();
		Arrays.stream(images).forEach(image -> {
			try {
				files.add(createTempFile(image));
			} catch (IOException e) {
				logger.error("Error processing image {} ", image.getName());
			}
		});
		return twitterService.post(twitterCreds, files, message,null, null);
	}
	
	private File createTempFile(MultipartFile file) throws IOException {
		File dest = File.createTempFile(file.getOriginalFilename(), null);
		file.transferTo(dest);
		return dest;
	}
	
	@PostMapping(value = "/twitter/video", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
	public TweetInfo postTextWithVideo(@RequestPart("videofile") MultipartFile video,
			@RequestParam(value = "msg", required = false) String message) {
		long start = System.currentTimeMillis();
		TweetInfo tweet = null;
		if (message == null) {
			message = "Video post @ " + System.currentTimeMillis();
		}
		try {
			tweet = socialPostTwitterService.postTwitterData(twitterCreds, new TwitterData(message, FileType.VIDEO, createTempFile(video)));
		} catch (Exception e) {
            throw new RuntimeException(e);
        }
        logger.info("Total time taken: {}", (System.currentTimeMillis() - start));
		return tweet;
	}
	
	// Provide valid values for apis to work.
	private static TwitterCreds twitterCreds = new TwitterCreds("*************************", "xKvy8ISsBTQKfP93V9bsiDZGoN5z76UE0LhlhiMQ6iEfQAU5D5",
			"898462151173562369-qYwEBvg0xML4OC1aUqJLFom76EPq7r4", "VkZv6lpsH328laZsDOjGcqqztXwIc6mfSbRczkR42QjEf");
	
	@GetMapping("/facebook/business/updatepageinfo")
	public ResponseEntity<String> updateFBPageInfoForBusiness(@RequestParam("businessId") Integer businessId) throws Exception{
		String message = socialPostFacebookService.updateFbPageInfo(businessId);
		return new ResponseEntity<>(message,HttpStatus.OK);
	}

	@GetMapping("/facebook/updatepageinfo")
	public ResponseEntity<String> updateFBPageInfoForBusiness() throws Exception{
		String message = socialPostFacebookService.updateFbPageInfo();
		return new ResponseEntity<>(message,HttpStatus.OK);
	}

	@GetMapping("/twitter/business/updatepageinfo")
	public ResponseEntity<String> updateTwitterPageInfoForBusiness(@RequestParam("businessId") Integer businessId) throws Exception{
		String message = socialPostTwitterService.updateAccountInfo(businessId);
		return new ResponseEntity<>(message,HttpStatus.OK);
	}

	@GetMapping("/twitter/updatepageinfo")
	public ResponseEntity<String> updateTwitterPageInfo() throws Exception{
		String message = socialPostTwitterService.updateAccountInfo();
		return new ResponseEntity<>(message,HttpStatus.OK);
	}

	@GetMapping(value = "/facebook/homefeed")
	public FacebookFeedData getFBHomeFeed() throws Exception{
		FacebookPageAccessInfo fbCreds = new FacebookPageAccessInfo();
		fbCreds.setBaseUrl("https://graph.facebook.com/v2.9/");
		fbCreds.setAccessToken("EAACEdEose0cBANZBz3xbMWlIY1HdZCXbZBJdtzcbDZCSboKTccMaid2Por9dsptsdGLANWcE8RRyasQlWK5S8lFlYeJLefOTrnStP3jZCDlDmufXfRuy4khpsAQWpvQT9ytIVVA9ffE52KZBOA88KXrQ3eIqgfqD5fVIx0iT7OZBZBdNqAKSUKv7jU3CV40jOcsZD");
		fbCreds.setPageId("***************");
		return facebookService.getFacebookHomeFeed(fbCreds, null);
	}
        
	/**
	 * to save location level pages at enterprise level
	 * @return
	 */
	@PostMapping("/facebook/enterprise/sync")
	public ResponseEntity<?> syncFacebook() {
		facebookAccountService.copyPageToEnterprise();
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * to remove duplicate pages
	 * @return
	 */
	@PostMapping("/facebook/enterprise/remove/duplicate")
	public ResponseEntity<?> removeDuplicateFacebook() {
		facebookAccountService.removeDuplicatePages();
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * to keep single page per location	
	 * @return
	 */
	@PostMapping("/facebook/multipage/remove")
	public ResponseEntity<?> removeMultiPage() {
		facebookAccountService.removeMultiPage();
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * to save location level pages without userId at enterprise level
	 * @return
	 */
	@PostMapping("/facebook/enterprise/sync/null")
	public ResponseEntity<?> syncFacebookNullUser() {
		facebookAccountService.copyPageToEnterpriseWithNullUser();
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	@PostMapping("/facebook/sync")
	public ResponseEntity<?> syncFacebookForEnterprise(@RequestParam("enterpriseId") Long enterpriseId) {
		facebookAccountService.copyPageToEnterprise(enterpriseId);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping("/facebook/update/user")
	public ResponseEntity<?> udateUser() {
		facebookAccountService.updateUser();
		return new ResponseEntity<>(HttpStatus.GONE);
	}
	
	@PostMapping("/facebook/sync/rawtofacebookpage")
	public ResponseEntity<?> syncBusinessFacebookPageWithRawDataForEnterprise(@RequestParam("enterpriseId") Long enterpriseId) {
		facebookAccountService.syncBusinessFacebookPageWithRawTableForEnterprise(enterpriseId);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/**
	 * To Update Location of existing Twitter accounts at Location Level
	 * @return
	 */
	@PostMapping("/twitter/migration/enterprise")
	public ResponseEntity<?> enterpriseTwitterLocation() {
		twitterAccountService.updateTwitterAccountLocation();
		return new ResponseEntity<>(HttpStatus.OK);
	}
	
	/*
	* To clean redis lock for given channel and businesId.
	* @return
	*/
	@PostMapping("/redis/cleanup")
	public ResponseEntity<Void> cleanRedisLock(@RequestParam("channel") String channel, @RequestParam("businessId") String businessId) {
		if (StringUtils.isNotBlank(channel) && StringUtils.isNotBlank(businessId)) {
			logger.info("[Redis Lock] Received cleanRedisLock() request for channel:{} and businessId:{}", channel, businessId);
			commonService.cleanRedisLock(channel, businessId);
		} else {
			throw new BirdeyeSocialException(ErrorCodes.INVALID_REQUEST, "Channel or businessId can not be blank.");
		}
		return new ResponseEntity<>(HttpStatus.OK);
	}

	/**
	 * Endpoint to test youtube channel information
	 */

	@GetMapping("/youtube/channel")
	public Map<?, ?> getYoutubeChannelDetails(
			@RequestParam("id") String channelId) {
		// https://www.googleapis.com/youtube/v3/channels?part=snippet&id=UCyrQBJmX3pv3UhFjvQiZTbw&key=AIzaSyC-NXuCNvZLfQhfdInKp5n5r1poncsAxsM
		return youtubeService.getChannelInformation(channelId);
	}

	@GetMapping("/reviews/invalid/list")
	public ResponseEntity<String> getRedisList(
			@RequestParam("date") String date) {
		String result = redisService.getInvalidLocationForReviews(date);
		return new ResponseEntity<>(result, HttpStatus.OK);
	}
	
	/**
	 * Examples
	 * 
	 * https://www.facebook.com/pg/Heritage-Family-Dentist-711533552238739/reviews/
	 * https://www.facebook.com/pg/mybettervision/reviews/?ref=page_internal
	 * https://www.facebook.com/pg/Somerset-Smiles-Raj-Sinha-DMD-and-Joanna-Hrymoc-Sinha-DMD-149903485052920/reviews/
	 * https://www.facebook.com/pages/The-Face-Place/433805616666018
	 * http://www.facebook.com/pages/Coastline-Masonry-Inc/131402183633168
	 * http://www.facebook.com/PF-Changs-China-Bistro-198850380146515
	 * 
	 * Not returning results for unavailables pages
	 * https://www.facebook.com/pages/Essential-Licensed-Massage-Therapy-LLC/130561253629638?v=wall
	 */
	@GetMapping("/facebook/pageid")
	public ResponseEntity<Map<String,String>> getFBPageId(
			@RequestParam("url") String fbUrl) throws Exception {
		String accessToken = CacheManager.getInstance().getCache(SystemPropertiesCache.class).getFBAccessToken();
		// Demo accessToken - 113069365515461|aePk-WDY2ihQtv1WSLOiHqcUIG8
		Map<String,String> result = facebookService.getFacebookPageId(accessToken,fbUrl);
		return new ResponseEntity<>(result, HttpStatus.OK);
	}
	
	@PostMapping("/location/{locationId}/remove")
	public ResponseEntity<String> deleteGmbLocation(@PathVariable("locationId") String locationId) {
		gmbLocationDetailsService.deleteGmbLocationForBusiness(locationId);
		return new ResponseEntity<>("SUCCESS", HttpStatus.OK);
	}
	
	@PostMapping("/gmb/{tokenId}")
	public Map<?,?> fetchGmbAccountDetails(@PathVariable("tokenId") Integer refreshTokenId)  {
		// Will work for Birdeye accounts.
		return	gmbLocationDetailsService.getGMBAccountDetails(refreshTokenId);
	}
	
	
	@PostMapping("/google/user/{tokenId}")
	public Map<?,?> getUserInfoDetails(@PathVariable("tokenId") Integer refreshTokenId)  {
		return	gmbLocationDetailsService.getUserDetailsInfo(refreshTokenId);
	}
	
	/**
	 * It will check all invalid pages in business_facebook_page and print current status.
	 */
	@PostMapping("/facebook/checkValid")
	public void scanFacebookPages(@RequestParam(value="enterpriseId",required=false) Integer enterpriseId)  {
		facebookPageService.checkInvalidPages(enterpriseId);
	}

	@PostMapping("/redis/releaseLock")
	public void releaseLock(@RequestParam(value="enterpriseId") Long enterpriseId,@RequestParam(value="channel") String channel)  {
		String key = SocialChannel.getSocialChannelByName(channel).getName().concat(String.valueOf(enterpriseId));
		redisService.release(key);
	}

	@PostMapping("/linkedin/scheduler")
	public void linkedinScheduler() {
		List<BusinessLinkedinPage> businessLinkedinPages = linkedinPagesStatusUpdateScheduler.getIntegrations();
		if(CollectionUtils.isNotEmpty(businessLinkedinPages)){
			businessLinkedinPages.forEach(businessLinkedinPage -> {
				linkedinPagesStatusUpdateScheduler.updateIntegrationStatusForAPage(businessLinkedinPage);
			});
		}
	}

	@PostMapping("/migrate-fb-token")
	public ResponseEntity<Void> debugFbToken(@RequestBody List<Integer> rawIds){
		facebookAccountService.checkFbTokenAndUpdate(rawIds);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@Autowired
	private SocialReportService socialReportService;

	@PostMapping("/postTest")
	public ResponseEntity<Void> imageCDN(@RequestBody SocialScanEventDTO socialScanEventDTO){
		socialReportService.initiatePostsFetch("instagram",socialScanEventDTO);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@Autowired
	private IInstagramService instagramService;

	@GetMapping("/comment/{id}")
	public ResponseEntity<Void> comment(@PathVariable("id") String id){
		String accessToken ="EAABm1gNd1MUBAAgh7TbA7fGiEoF3IP8iZAa20lrpeSyCuwgeJpVriG6ExxJCBBGlaAXn713FG79a4rO461COg1GGLHYiTGLqu1IIhIOnXEU7kumL1umEApdIwtIgLBE50kemWInWdzWJQdjltBr284ZAvw9KcrbNm7Tr9aFW6LJudWhfyA33Yjg4ZBF5vcGsbnZCviM77wZDZD";
		String text = "encoded text test!!! \nHey you!!       text";
		instagramService.postCommentOnAnInstagramPost(accessToken,id,text);
		return new ResponseEntity<>(HttpStatus.OK);
	}
	@GetMapping("/retry-test/{id}")
	public ResponseEntity<Void> retryTest(@PathVariable("id") Integer id){
		logger.info("Testing retry for businessId: {}",id);
		BusinessLiteDTO businessLiteDTO = new BusinessLiteDTO();
		businessLiteDTO.setBusinessId(id);
		kafkaProducerService.sendObjectV1("test-topic",businessLiteDTO);
		logger.info("Response: {}",businessLiteDTO);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@PostMapping("/retryConsume")
	public ResponseEntity<Void> retryConsume(@RequestBody BusinessLiteDTO businessLiteDTO) throws Exception{
		Integer id = businessLiteDTO.getBusinessId();
		logger.info("Consume id: {}",id);
		if(id>5) {
			throw new Exception("Trial exception");
		}
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping("/test/social-post")
	public ResponseEntity<Void> socialPostTest(){
		SocialPostPublishInfo post = new SocialPostPublishInfo();

		post.setSourceId(2);
		kafkaProducerService.sendObjectV1("social-post-publish-event", post);

		post.setSourceId(110);
		kafkaProducerService.sendObjectV1("social-post-publish-event", post);

		post.setSourceId(109);
		kafkaProducerService.sendObjectV1("social-post-publish-event", post);

		post.setSourceId(108);
		kafkaProducerService.sendObjectV1("social-post-publish-event", post);

		post.setSourceId(140);
		kafkaProducerService.sendObjectV1("social-post-publish-event", post);

		post.setSourceId(195);
		kafkaProducerService.sendObjectV1("social-post-publish-event", post);

		post.setSourceId(22);
		kafkaProducerService.sendObjectV1("social-post-publish-event", post);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping("/parallel/count")
	public ResponseEntity<Void> checkParallelCount(){
		int parallelism = ForkJoinPool.getCommonPoolParallelism();
		logger.info("Parallelism level: {}", parallelism);
		return new ResponseEntity<>(HttpStatus.OK);
	}

	@GetMapping("/storyReel/{id}")
	public ResponseEntity<Void> storyReeltest(@PathVariable("id") Integer id) throws Exception{
		String accessToken ="EAABm1gNd1MUBO3q5eDfcTcCfJUFZCci0okDZAjjkMelKIeuOreAZAKSxtpCzJaNZCYabBaW60qQA7XnIx0WHPUj1vkDFyzc4Jr5wJzG35YA491RxjGZCB7ZCgRxSO2f3Uf6BVlAi32CpgQXqZARxu4kTIlMgB0MCnwdWcgI88zcRHExy67LZBzKFMm9scCZA9iZAtGpJQEqdG6";
		String pageId = "374662075732946";
		String text = "This is test from code for id: "+id;
		String imgUrl="https://ddjkm7nmu27lx.cloudfront.net/149546071353527/socialpublish/original/6n11bqyfhx/1740735825944.jpeg";
		String videoUrl="https://ddjkm7nmu27lx.cloudfront.net/149546071353527/socialpublish/g203wxkp9f/1741585417373.mp4";
		FacebookPageAccessInfo facebookPageAccessInfo = new FacebookPageAccessInfo();
		facebookPageAccessInfo.setAccessToken(accessToken);
		facebookPageAccessInfo.setPageId(pageId);

		if(id==1) {
			// photo story
			String photoId = facebookService.uploadPhotoStory(facebookPageAccessInfo, imgUrl);
			FbMediaPublishResponse mediaPublishResponse = facebookService.publishPhotoStory(facebookPageAccessInfo, photoId);
			logger.info("Response: {}",mediaPublishResponse);
		} else if(id==2) {
			//video story
			String videoId = facebookService.initiateSession(facebookPageAccessInfo,false);
			FbMediaPublishResponse mediaPublishResponse = facebookService.uploadVideo(facebookPageAccessInfo, videoId, videoUrl);
			FbMediaPublishResponse publishResponse = facebookService.publishMedia(facebookPageAccessInfo, videoId, "", false);
			logger.info("Response: {}",publishResponse);
			FbUploadStatusResponse statusResponse = facebookService.checkMediaStatus(facebookPageAccessInfo, videoId);
			logger.info("Status Response: {}",statusResponse);
		} else {
			//reel
			String videoId = facebookService.initiateSession(facebookPageAccessInfo,true);
			FbMediaPublishResponse mediaPublishResponse = facebookService.uploadVideo(facebookPageAccessInfo, videoId, videoUrl);
			FbMediaPublishResponse publishResponse = facebookService.publishMedia(facebookPageAccessInfo, videoId, text, true);
			logger.info("Response: {}",publishResponse);
			FbUploadStatusResponse statusResponse = facebookService.checkMediaStatus(facebookPageAccessInfo, videoId);
			logger.info("Status Response: {}",statusResponse);
			File imgFile = commonService.getFile(null, imgUrl);
			FbMediaPublishResponse thumbnailResponse = facebookService.uploadThumbnail(facebookPageAccessInfo, videoId, imgFile);
			logger.info("Thumbnail Response: {}",thumbnailResponse);
		}


		return new ResponseEntity<>(HttpStatus.OK);
	}

}
