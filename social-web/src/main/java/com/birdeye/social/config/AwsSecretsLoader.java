package com.birdeye.social.config;


import com.amazonaws.services.secretsmanager.AWSSecretsManager;
import com.amazonaws.services.secretsmanager.model.GetSecretValueRequest;
import com.amazonaws.services.secretsmanager.model.GetSecretValueResult;
import com.birdeye.social.constant.Constants;
import com.birdeye.social.exception.BirdeyeSocialException;
import com.birdeye.social.exception.ErrorCodes;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
@ConditionalOnClass(AWSSecretsManager.class)
public class AwsSecretsLoader {

    private static final Logger logger = LoggerFactory.getLogger(AwsSecretsLoader.class);

    @Value("${aws.sm.bazaarify.secret.id}")
    private String bazaarifyMasterSecretName;

    @Value("${aws.sm.social.secret.id}")
    private String socialSecretName;

    @Value("${aws.sm.gnip.secret.id}")
    private String gnipSecretName;

    @Value("${aws.secret.enabled}")
    private String smEnabled;

    @Autowired(required = false)
    @Qualifier("defaultSecretsManagerClient")
    private AWSSecretsManager awsSecretsManager;

    private Map<String, String> secretsMap = new HashMap<>();

    public Map<String, String> getSecretObject() {
        return secretsMap;
    }


    @PostConstruct
    public void retrieveSecrets() {
        if (Boolean.valueOf(smEnabled)) {
            getSecretValue(bazaarifyMasterSecretName, Constants.BAZAARIFY_MASTER_DB);
            getSecretValue(socialSecretName, Constants.SOCIAL_MASTER_DB);
            getSecretValue(gnipSecretName, Constants.GNIP_SECRET);
        }
    }

    private GetSecretValueResult getSecretValue(String secretName, String secretType) {
        GetSecretValueRequest getSecretValueRequest = new GetSecretValueRequest().withSecretId(secretName);
        GetSecretValueResult getSecretValueResult = null;
        try {
            getSecretValueResult = awsSecretsManager.getSecretValue(getSecretValueRequest);
            logger.info("secret value is {}", getSecretValueResult.toString());
            Map<String, String> awsSecretValMap = getSecretMap(getSecretValueResult.getSecretString());
            buildAwsSecretsMap(awsSecretValMap, secretType);
        } catch (Exception e) {
            logger.error("[SecretsLoader] Failed to retrive secrets ", e);
            throw new BirdeyeSocialException(ErrorCodes.FAILED_TO_GET_AWS_SM_SECRETS);
        }
        return getSecretValueResult;

    }

    @SuppressWarnings("unchecked")
    private Map<String, String> getSecretMap(String secretValueResult) {
        Map<String, String> awsSecretMap = null;
        try {
            ObjectMapper mapper = new ObjectMapper();
            awsSecretMap = mapper.readValue(secretValueResult, Map.class);
        } catch (Exception e) {
            logger.error("[SecretsLoader] Failed to unmarshal secrets", e);
            throw new BirdeyeSocialException(ErrorCodes.FAILED_TO_PARSE_AWS_SM_SECRETS);
        }
        return awsSecretMap;
    }

    private Map<String, String> buildAwsSecretsMap(Map<String, String> secretVal, String secretType) {
        if (Constants.BAZAARIFY_MASTER_DB.equals(secretType)) {
            secretsMap.put(Constants.BAZAARIFY_MASTER_HOST_NAME, secretVal.get(Constants.HOST_NAME));
            secretsMap.put(Constants.BAZAARIFY_MASTER_DB_PORT, secretVal.get(Constants.DB_PORT));
            secretsMap.put(Constants.BAZAARIFY_MASTER_DB_USERNAME, secretVal.get(Constants.DB_USERNAME));
            secretsMap.put(Constants.BAZAARIFY_MASTER_DB_PASSWORD, secretVal.get(Constants.DB_PASSWORD));
            secretsMap.put(Constants.BAZAARIFY_MASTER_DB_NAME, Constants.BAZAARIFY_MASTER_DB);
        } else if (Constants.SOCIAL_MASTER_DB.equals(secretType)) {
            secretsMap.put(Constants.SOCIAL_HOST_NAME, secretVal.get(Constants.HOST_NAME));
            secretsMap.put(Constants.SOCIAL_DB_PORT, secretVal.get(Constants.DB_PORT));
            secretsMap.put(Constants.SOCIAL_USERNAME, secretVal.get(Constants.DB_USERNAME));
            secretsMap.put(Constants.SOCIAL_PASSWORD, secretVal.get(Constants.DB_PASSWORD));
            secretsMap.put(Constants.SOCIAL_DB_NAME, Constants.SOCIAL_DB_NAME_VALUE);
        } else if (Constants.GNIP_SECRET.equals(secretType)) {
            secretsMap.put(Constants.GNIP_USERNAME, secretVal.get(Constants.GNIP_USERNAME));
            secretsMap.put(Constants.GNIP_PASSWORD, secretVal.get(Constants.GNIP_PASSWORD));
        }
        logger.info("[AWS SM] secrets are parsed successfully as: {} ", secretsMap);
        return secretsMap;
    }

}

