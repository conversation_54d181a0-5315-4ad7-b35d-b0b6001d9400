/**
 * 
 */
package com.birdeye.social.config;

import java.util.HashMap;
import java.util.Map;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

/**
 * <AUTHOR>
 *
 */
@Configuration
@EnableKafka
public class KafkaConfig {

	@Autowired
	private Environment env;

	@Bean
	@Qualifier("kafkaTemplate")
	public KafkaTemplate<String, Object> kafkaTemplate() {
		return new KafkaTemplate<>(this.kafkaProducerFactory());
	}

	private ProducerFactory<String, Object> kafkaProducerFactory() {
		return new DefaultKafkaProducerFactory<>(this.kafkaConfig());
	}

	private Map<String, Object> kafkaConfig() {
		Map<String, Object> config = new HashMap<>();
		config.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, env.getProperty("kafka.server"));
		config.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
		config.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
		config.put(ProducerConfig.LINGER_MS_CONFIG, 5);
		config.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 1);
		config.put(ProducerConfig.RETRIES_CONFIG, 3);
		config.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 15000);
		config.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, 1000);
		return config;
	}

	@Bean
	@Qualifier("newKafkaTemplate")
	public KafkaTemplate<String, Object> newKafkaTemplate() {
		return new KafkaTemplate<>(this.newKafkaProducerFactory());
	}

	private ProducerFactory<String, Object> newKafkaProducerFactory() {
		return new DefaultKafkaProducerFactory<>(this.newKafkaConfig());
	}

	private Map<String, Object> newKafkaConfig() {
		Map<String, Object> config = new HashMap<>();
		config.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, env.getProperty("kafka.server.new"));
		config.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
		config.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
		config.put(ProducerConfig.LINGER_MS_CONFIG, 5);
		config.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 1);
		config.put(ProducerConfig.RETRIES_CONFIG, 3);
		config.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 15000);
		config.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, 1000);
		return config;
	}
}
