package com.birdeye.social.controller;

import com.birdeye.social.model.ChannelPageRemoved;
import com.birdeye.social.model.SocialConnectPageRequest;
import com.birdeye.social.service.ISocialEsService;
import com.birdeye.social.sro.LocationPageMappingRequest;
import com.birdeye.social.sro.SocialEsSyncDTO;
import com.birdeye.social.sro.SocialEsValidRequest;
import freemarker.template.TemplateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/social/elastic")
public class SocialEsController {

    @Autowired
    private ISocialEsService socialEsService;

    /**
     * API to index social pages to ES index social, called from nifi consumed from event : Constants.SOCIAL_PAGE_CONNECT
     * @param socialConnectPageRequest
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/connect-page")
    public ResponseEntity<Void> addConnectedPage(@RequestBody SocialConnectPageRequest socialConnectPageRequest) throws IOException {
        socialEsService.addConnectedPage(socialConnectPageRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Api to add business_id in ES document whenever social page is mapped to location called from nifi consumed from events :
     * Constants.FB_PAGE_MAPPING_ADDED , Constants.TWITTER_PAGE_MAPPING_ADDED, Constants.LINKEDIN_PAGE_MAPPING_ADDED, Constants.GMB_PAGE_MAPPING_ADDED
     * if no document found in es then this api will create one with mapping
     * @param locationPageMappingRequest
     * @param channel
     * @return
     * @throws IOException
     */

    @PostMapping(value = "/mapping-page")
    public ResponseEntity<Void> addMappingPage(@RequestBody LocationPageMappingRequest locationPageMappingRequest, @RequestParam("channel") String channel) throws IOException {
        socialEsService.addMapping(locationPageMappingRequest,channel);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Api to remove business_id in ES document whenever social page is un-mapped to location called from nifi consumed from events :
     * Constants.FB_PAGE_MAPPING_REMOVED , Constants.GMB_PAGE_MAPPING_REMOVED, Constants.TWITTER_PAGE_MAPPING_REMOVED, Constants.LINKEDIN_PAGE_MAPPING_REMOVED
     * if no document found in es then this api will create one without mapping
     * @param input
     * @param channel
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/remove-mapping")
    public ResponseEntity<Void> removeMappingPage(@RequestBody List<LocationPageMappingRequest> input, @RequestParam("channel") String channel) throws IOException {
        socialEsService.removeMapping(input,channel);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * Api to remove document from ES document whenever social page is removed called from nifi consumed from events :
     * Constants.SOCIAL_PAGE_REMOVED
     * @param input
     * @return
     * @throws IOException
     */

    @PostMapping(value = "/remove-page")
    public ResponseEntity<Void> removePage(@RequestBody List<ChannelPageRemoved> input) throws IOException {
        socialEsService.removePage(input);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * This api is called to update is_valid field in ES whenever social page is reconnected,disconnected called from Nifi consumed from event :
     * Constants.INIT_DISCONNECTED_PAGE
     * @param socialEsValidRequest
     * @return
     * @throws IOException
     */

    // TODO: make this api generic to update any field for given document ID by passing map of key value. Key would be document field to update
    @PostMapping(value = "/mark-page-validity")
    public ResponseEntity<Void> markPageValidity(@RequestBody SocialEsValidRequest socialEsValidRequest) throws IOException {
        socialEsService.updatePageValidity(socialEsValidRequest);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping(value = "/mapped-location-account")
    public ResponseEntity<?> fetchDisconnectedLocation(@RequestBody List<String> businessIds) throws IOException {
        return new ResponseEntity<>(socialEsService.fetchAllMappedPagesForAccount(new String[]{"gmb","facebook"},businessIds),HttpStatus.OK);
    }

    /**
     * This API is for migration of data present in db to ES
     * NOTE : this api will fetch all integrations from database and index them to ES
     * @param channel
     * @throws IOException
     */
    @PostMapping(value = "/sync-es")
    public void bulkUploadIntegrationData(@RequestParam String channel) throws IOException {
        socialEsService.syncEsData(channel);
    }

    /**
     * This API is to delete all document from ES channel wise.
     * @param channel
     * @throws IOException
     */
    @DeleteMapping(value = "/es-data")
    public void deleteEsData(@RequestParam String channel) throws IOException {
        socialEsService.deleteEsData(channel);
    }

    @PostMapping(value = "/sync-es/event")
    public void syncSocialESEvent(@RequestBody SocialEsSyncDTO socialEsSyncDTO) throws IOException {
        socialEsService.syncSocialESEvent(socialEsSyncDTO);
    }

}
