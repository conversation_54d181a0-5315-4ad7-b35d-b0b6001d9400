package com.birdeye.social.controller;

import com.birdeye.social.constant.Constants;
import com.birdeye.social.model.SocialPostEsSyncRequest;
import com.birdeye.social.model.es.SocialPostsESSyncRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.birdeye.social.model.GlobalFilterCriteriaSchedulePostMessage;
import com.birdeye.social.model.SocialSchedulePostResponse;
import com.birdeye.social.service.SocialPostCalendarService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/social/post")
public class SocialPostCalendarController {

    private static final Logger LOG = LoggerFactory.getLogger(SocialPostCalendarController.class);

    @Autowired
    private SocialPostCalendarService socialPostCalendarService;

    @PostMapping(value = "/schedule/all")
    public @ResponseBody ResponseEntity<SocialSchedulePostResponse> getAllScheduledPosts(@RequestBody GlobalFilterCriteriaSchedulePostMessage filter ,
                                                                                         @RequestHeader("user-id") Integer userId,
                                                                                         @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String source,
                                                                                         @RequestParam(value = "timeZone", required = false) String timezone) throws Exception {
        LOG.info("Received request to get post with filters : {}", filter);
        SocialSchedulePostResponse posts;
        if(StringUtils.isBlank(source) || !StringUtils.equalsIgnoreCase(source, "dashboard")) {
            posts = socialPostCalendarService.getAllScheduledPostsV2(filter, userId, false, source);
        }
        else {
            LOG.info("Received request to get posts with source header : {} and timezone: {}", source, timezone);
            posts = socialPostCalendarService.getAllESScheduledPosts(filter,userId, false, source, timezone);
        }
        return new ResponseEntity<>(posts, HttpStatus.OK);
    }

    @PostMapping(value = "/schedule/list/all")
    public @ResponseBody ResponseEntity<SocialSchedulePostResponse> getAllScheduledPostsAsList(@RequestBody GlobalFilterCriteriaSchedulePostMessage filter ,
                                                                                               @RequestHeader("user-id") Integer userId,
                                                                                               @RequestHeader(value = Constants.CLIENT_REQUEST_SOURCE, required = false) String source) throws Exception {
        LOG.info(" Received request to get post with filters : {}", filter);
        SocialSchedulePostResponse posts = socialPostCalendarService.getAllScheduledPostsV2(filter,userId, true, source);
//		LOG.info(" Received response to send post  : {}", posts);
        return new ResponseEntity<>(posts, HttpStatus.OK);
    }

    @PostMapping(value = "/es/migration/all")
    public @ResponseBody ResponseEntity<Integer> findAllPosts(@RequestBody SocialPostsESSyncRequest socialPostsESSyncRequest) throws Exception {
        LOG.info("Received request to find all posts for ES sync up : {}", socialPostsESSyncRequest);
        Integer count = socialPostCalendarService.socialPostsESSyncRequest(socialPostsESSyncRequest);
        LOG.info("Sent {} posts for sync up", count);
        return new ResponseEntity<>(count, HttpStatus.OK);
    }

    @PostMapping(value = "/es/migration/posts")
    public @ResponseBody ResponseEntity<String> syncAllPostIds(@RequestBody SocialPostsESSyncRequest socialPostsESSyncRequest) throws Exception {
        LOG.info("Received request from nifi to sync all posts for ES posts : {}", socialPostsESSyncRequest);
        socialPostCalendarService.syncRecordsOnEsBySocialPostIds(socialPostsESSyncRequest.getPostIds());
        return new ResponseEntity<>("success", HttpStatus.OK);
    }
    @PostMapping(value = "suspended/post/backup")
    public @ResponseBody ResponseEntity<String> suspendedPostBackup(@RequestBody SocialPostEsSyncRequest socialPostsESSyncRequest) throws Exception {
        LOG.info("Received request for post backup of post id : {}", socialPostsESSyncRequest.getPostId());
        socialPostCalendarService.saveSuspendedDeletedPost(socialPostsESSyncRequest);
        return new ResponseEntity<>("success", HttpStatus.OK);
    }

}