{
  "size": 0,
  "query": {
    "bool": {
      "must": [
        {
          "terms": {
            "page_id": [
              ${pageIds}
            ]
          }
        },
        {
            "range": {
              "posted_date": {
             "gte": "${startDate}",
                    "lte": "${endDate}"
              }
            }
          }
      ]
    }
  },
  "aggs": {
    "report_data": {
      "terms": {
        "field": "page_id",
        "size": 10000
      },
      "aggs": {
     "histogram": {
            "aggs": {
              "postCount": {
                "sum": {
                  "field": "post_count"
                }
              }
            },
          "date_histogram": {
            "field": "posted_date",
            "interval": "${type}",
            "format": "yyyy-MM-dd HH:mm:ss"
            <#if includeExtendedBounds>,
            "extended_bounds" : {
            "min" : "${startDate}",
            "max" : "${endDate}"
            }
           </#if>
          }
        }
      }
    }
  }
}